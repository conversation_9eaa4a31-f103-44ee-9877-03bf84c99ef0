var WellWebViewAPI = Object.freeze({
  /**
   * Calls the 'connectHn' handler in the Flutter webview and returns a Promise.
   * Resolves if the result is falsy, otherwise rejects.
   * @returns {Promise} A promise that resolves or rejects based on the result of the callHandler.
   */
  close: () => {
    return new Promise((resolve, reject) => {
      window.flutter_inappwebview.callHandler("close").then((result) => {
        resolve(result);
      });
    });
  },
  /**
   * Calls the 'connectHn' handler in the Flutter webview and returns a Promise.
   * Resolves if the result is falsy, otherwise rejects.
   * @returns {Promise} A promise that resolves or rejects based on the result of the callHandler.
   */
  connectHn: () => {
    return new Promise((resolve, reject) => {
      window.flutter_inappwebview.callHandler("connectHn").then((result) => {
        if (!!result) {
          resolve(result);
        } else {
          reject(result);
        }
      });
    });
  },
  /**
   * Calls the 'immPayment' handler in the Flutter webview and returns a Promise.
   * Resolves if the result is falsy, otherwise rejects.
   * @returns {Promise} A promise that resolves or rejects based on the result of the callHandler.
   */
  immPayment: () => {
    return new Promise((resolve, reject) => {
      window.flutter_inappwebview.callHandler("immPayment").then((result) => {
        resolve(result);
      });
    });
  },
  home: () => {
    return new Promise((resolve, reject) => {
      window.flutter_inappwebview.callHandler("home").then((result) => {
        resolve(result);
      });
    });
  },
  calendar: () => {
    return new Promise((resolve, reject) => {
      window.flutter_inappwebview.callHandler("calendar").then((result) => {
        resolve(result);
      });
    });
  },
  notification: () => {
    return new Promise((resolve, reject) => {
      window.flutter_inappwebview.callHandler("notification").then((result) => {
        resolve(result);
      });
    });
  },
  chat: () => {
    return new Promise((resolve, reject) => {
      window.flutter_inappwebview.callHandler("chat").then((result) => {
        resolve(result);
      });
    });
  },
  coin: () => {
    return new Promise((resolve, reject) => {
      window.flutter_inappwebview.callHandler("coin").then((result) => {
        resolve(result);
      });
    });
  },
  virtual: () => {
    return new Promise((resolve, reject) => {
      window.flutter_inappwebview.callHandler("virtual").then((result) => {
        if (!!result) {
          resolve(result);
        } else {
          reject(result);
        }
      });
    });
  },
  connectDevice: () => {
    return new Promise((resolve, reject) => {
      window.flutter_inappwebview
        .callHandler("connectDevice")
        .then((result) => {
          if (!!result) {
            resolve(result);
          } else {
            reject(result);
          }
        });
    });
  },
  appointment: () => {
    return new Promise((resolve, reject) => {
      window.flutter_inappwebview.callHandler("appointment").then((result) => {
        resolve(result);
      });
    });
  },
  refresh: () => {
    return new Promise((resolve, reject) => {
      window.flutter_inappwebview.callHandler("refresh").then((result) => {
        resolve(result);
      });
    });
  },
  launchUrl: (url) => {
    const isValidUrl = (urlString) => {
      try {
        return Boolean(new URL(urlString));
      } catch (e) {
        return false;
      }
    };
    return new Promise((resolve, reject) => {
      if (!isValidUrl(url)) {
        return reject(new TypeError());
      }
      window.flutter_inappwebview
        .callHandler("launchUrl", url)
        .then((result) => {
          if (!!result) {
            resolve(result);
          } else {
            reject(result);
          }
        });
    });
  },
  share: ({ title, text, url, files }) => {
    const isValidUrl = (urlString) => {
      try {
        return Boolean(new URL(urlString));
      } catch (e) {
        return false;
      }
    };
    return new Promise((resolve, reject) => {
      if (!title && !text && !url) {
        reject(new TypeError());
      } else if (!!url && !isValidUrl(url)) {
        reject(new TypeError());
      } else if (!!files) {
        const file = Array.isArray(files) ? files[0] : files;
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = function () {
          window.flutter_inappwebview
            .callHandler("shareFile", title, text, reader.result)
            .then((_) => {
              resolve();
            });
        };
      } else {
        window.flutter_inappwebview
          .callHandler("shareLink", title, text, url)
          .then((_) => {
            resolve();
          });
      }
    });
  },
  getCurrentPosition: function () {
    return new Promise((resolve, reject) => {
      window.flutter_inappwebview
        .callHandler("getCurrentPosition")
        .then(function (result) {
          if (!!result || result.code === "SUCCESS") {
            return resolve(result);
          } else {
            return reject(result);
          }
        });
    });
  },
  saveImage: function (base64String) {
    return new Promise((resolve, reject) => {
      window.flutter_inappwebview
        .callHandler("saveImage", base64String)
        .then((result) => {
          if (!!result || result.code === "SUCCESS") {
            return resolve(result);
          } else {
            return reject(result);
          }
        });
    });
  },
  isWellWebView: function () {
    return true;
  },
});
window.WellWebViewAPI = WellWebViewAPI;

window.Android = {
  nativeShareLink: (title, text, url) => {
    window.flutter_inappwebview.callHandler("shareLink", title, text, url);
  },
  nativeShareFile: (title, text, files) => {
    const file = Array.isArray(files) ? files[0] : files;
    let reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function () {
      window.flutter_inappwebview.callHandler(
        "shareFile",
        title,
        text,
        reader.result
      );
    };
  },
};

window.well = {
  share: ({ title, text, url, files }) => {
    const isValidUrl = (urlString) => {
      try {
        return Boolean(new URL(urlString));
      } catch (e) {
        return false;
      }
    };
    return new Promise((resolve, reject) => {
      if (!title && !text && !url) {
        reject(new TypeError());
      } else if (!!url && !isValidUrl(url)) {
        reject(new TypeError());
      } else if (!!files) {
        const file = Array.isArray(files) ? files[0] : files;
        let reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = function () {
          window.flutter_inappwebview
            .callHandler("shareFile", title, text, reader.result)
            .then((_) => {
              resolve();
            });
        };
      } else {
        window.flutter_inappwebview
          .callHandler("shareLink", title, text, url)
          .then((_) => {
            resolve();
          });
      }
    });
  },
  geolocation: {
    getCurrentPosition: function () {
      return new Promise((resolve, reject) => {
        window.flutter_inappwebview
          .callHandler("getCurrentPosition")
          .then(function (result) {
            if (!!result || result.code === "SUCCESS") {
              return resolve(result);
            } else {
              return reject(result);
            }
          });
      });
    },
  },
};
navigator.share = window.well.share;
