<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<g transform="matrix(1 0 0 1 540 540)" id="93f8daa7-2819-4a1a-a661-110f66b208a2"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(1 0 0 1 540 540)" id="dc5a65d9-630c-4189-a93b-41827af208dc"  >
</g>
<g transform="matrix(2.21 0 0 2.21 540 540)"  >
<g style="" vector-effect="non-scaling-stroke"   >
		<g transform="matrix(1 0 0 1 32.12 -118.21)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-542.56, -422.27)" d="M 664.42 457.44 C 663.81 468.37 663.63 479.35 662.5 490.24 C 660.56 508.87 658.02 527.45 655.71 546.05 C 655.5100505179546 546.8727436254459 655.245907941614 547.6785456621303 654.9200000000001 548.4599999999999 C 647.2600000000001 547.41 639.59 546.4599999999999 631.97 545.28 C 583.34 537.5799999999999 536.03 525.0799999999999 490.73 505.55999999999995 C 476.32 499.34999999999997 462.26 492.55999999999995 449.19 483.55999999999995 C 440.78 477.71999999999997 430.5 474.55999999999995 420.71 470.03999999999996 C 422.28999999999996 466.54999999999995 425.78 463.29999999999995 430.34999999999997 461.15 C 438.73999999999995 457.15 447.58 456.25 456.34999999999997 459.15 C 469.72999999999996 463.58 483.08 468.22999999999996 496.13 473.56 C 512.24 480.13 528.93 482.91 546.13 484.4 C 562.06 485.78 578.13 487.60999999999996 593.65 491.14 C 603.14 493.28999999999996 611.99 498.71999999999997 620.81 503.28 C 626.3599999999999 506.14 631.3199999999999 506.05999999999995 634.28 501.66999999999996 C 637.24 497.28 636.05 492.19999999999993 630.53 488.78999999999996 C 624.74 485.21 618.53 482.34 612.6899999999999 478.84 C 610.93 477.78 608.8399999999999 475.84 608.55 473.96999999999997 C 602.6995640491115 437.4853070594792 601.5066861241908 400.4052848017328 605 363.61999999999983 C 607.0222830462321 340.81642511895774 610.9595016685287 318.22341358540615 616.77 296.08000000000004 L 631 296.08 C 635.75 298.46999999999997 641.15 300.08 645.13 303.4 C 654.9790318956422 311.4689028353283 660.9489351407392 323.33240502354613 661.56 336.04999999999995 C 662.2299999999999 346.65 662.17 357.29999999999995 662.56 367.92999999999995 C 663.14 385.03999999999996 663.79 402.13999999999993 664.41 419.24999999999994 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 1.53 -156.55)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-511.97, -383.93)" d="M 598.6 296.08 C 584.7666779091211 353.5977403724797 581.9537607871547 413.21529626264896 590.3100000000002 471.77999999999986 C 584.96 470.96999999999997 580.2 470.14 575.4000000000001 469.57 C 567.6800000000001 468.65 559.95 467.36 552.2 467.18 C 531.72 466.7 512.4300000000001 461.90000000000003 493.69000000000005 453.72 C 482.88211586992816 449.1617384985931 471.78455838816217 445.3233599475548 460.4699999999999 442.23 C 437.72 435.72 422.25 413.51 425.87 391.35 C 427.78000000000003 379.68 434.48 371.44 444.97 366.21000000000004 C 446.67 365.36 448.66 364.96000000000004 450.17 363.88000000000005 C 457.86 358.34000000000003 465.79 359.16 474.75 360.77000000000004 C 489.90483425193776 363.39480787332263 505.2133539750164 365.0382274028348 520.58 365.69 C 543.87 366.83000000000004 560.6800000000001 355.78000000000003 571.69 335.58000000000004 C 577.4418309660934 324.7505333522279 581.1023140044699 312.93453481407647 582.48 300.75000000000006 C 582.6700000000001 299.19000000000005 583.0500000000001 297.64000000000004 583.34 296.08000000000004 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 12.6 -218.32)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-523.05, -322.16)" d="M 565.22 296.08 C 564.03 309.74 560.51 322.62 552.22 333.82 C 545.36 343.11 535.95 348.42 524.57 348.26 C 510.07000000000005 348.07 495.57000000000005 346.53999999999996 481.1 345.58 L 480.88 344.06 C 481.8 343.06 482.65999999999997 342.01 483.65 341.06 C 495.460694571296 329.7366551238333 505.8685687532428 317.0356912430899 514.65 303.22999999999996 C 516.38 300.53000000000003 519.05 298.44 521.29 296.06 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -7.5 96.13)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-502.94, -636.61)" d="M 588 555.15 C 587.51 573.05 589.73 590.4 595.78 606.9499999999999 C 600.6899999999999 620.3499999999999 606.9499999999999 633.2399999999999 612.35 646.4699999999999 C 629 687.2 639.88 729.63 649 772.56 C 650.71 780.56 647.1 784.9 639 784.9 Q 530 784.9 421.06 784.9 C 413.51 784.9 409.85 781.24 410.71 774.06 C 412.4102968516449 759.8856822217184 416.0719793102837 746.0163868062307 421.5899999999999 732.8499999999999 C 423.06 733.3799999999999 424.36999999999995 733.79 425.59 734.3399999999999 C 458.48999999999995 748.7299999999999 492.85999999999996 753.6199999999999 528.5 750.3399999999999 C 529.61 750.2399999999999 530.71 750.0699999999999 531.8 749.8699999999999 C 537.4 748.8699999999999 540.3199999999999 745.0399999999998 539.55 739.8699999999999 C 538.8599999999999 735.1499999999999 534.78 732.4099999999999 529.4399999999999 732.5799999999999 C 517.0699999999999 732.9499999999999 504.63999999999993 734.04 492.3399999999999 733.18 C 464.55999999999995 731.25 438.2799999999999 723.53 414.56999999999994 708.5799999999999 C 389.23999999999995 692.5799999999999 372.00999999999993 670.0699999999999 363.56999999999994 641.31 C 354 608.07 353.5 574.81 365.8 542 C 373.91 520.37 388.11 503.33 406.69 489.86 C 408.2065122136022 488.7969456409494 410.02934205370036 488.25957961872393 411.88 488.33000000000004 C 422.04 489.09000000000003 431.81 491.77000000000004 439.88 498.08000000000004 C 450.40999999999997 506.34000000000003 462.42 511.67 474.36 517.1600000000001 C 509.52843766248134 533.4320438065715 546.3800369257467 545.7863337042755 584.2499999999999 554 C 585.33 554.25 586.37 554.66 588 555.15 Z M 452.69 571 C 451.12 569.61 449.95 568.54 448.69 567.53 C 426.13 548.8399999999999 391.95 559.16 384.37 587.27 C 379.62 604.9 384.71 620.73 397.21 633.27 C 410.72999999999996 646.87 425.40999999999997 659.35 439.84999999999997 672.01 C 452.46 683.01 452.79999999999995 683.0699999999999 465.43999999999994 672.01 C 479.88999999999993 659.35 494.43999999999994 646.74 508.0899999999999 633.26 C 518.28 623.1899999999999 523.0899999999999 610.36 522.3499999999999 595.61 C 521.2599999999999 573.19 500.3499999999999 555.48 478.06999999999994 558.35 C 468.28 559.59 460 563.86 452.69 571 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -57.77 77.82)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-452.67, -618.3)" d="M 452.53 661 C 466.84 648 480.61999999999995 636 493.63 623.26 C 498.0277634521534 618.6875816831226 501.4054529758571 613.2341979919764 503.54 607.26 C 507.85 595.78 503.49 584.2 493.94 578.5699999999999 C 484.86 573.2099999999999 472.89 575.2399999999999 464.82 583.6299999999999 C 463.2436780026055 585.4128772206662 461.8124498416104 587.3189521079729 460.54 589.3299999999999 C 455.97 595.9999999999999 449.26000000000005 595.9699999999999 444.72 589.2499999999999 C 443.52292044581793 587.3718950583711 442.1857889422598 585.586824501121 440.72 583.9099999999999 C 433.23 575.7699999999999 421.45000000000005 573.3099999999998 412.49 577.9099999999999 C 402.82831227848106 582.7245448548992 398.0079141718316 593.7726950608738 401.05 604.1299999999999 C 402.72 610.1999999999999 405.59000000000003 616.7599999999999 409.90000000000003 621.1299999999999 C 423.41 634.71 437.83 647.38 452.53 661 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(1 0 0 1 -57.74 77.81)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-452.7, -618.29)" d="M 452.53 661 C 437.83 647.38 423.40999999999997 634.71 409.92999999999995 621.1 C 405.61999999999995 616.74 402.74999999999994 610.1800000000001 401.0799999999999 604.1 C 398.0379141718315 593.7426950608739 402.858312278481 582.6945448548994 412.5199999999999 577.88 C 421.5199999999999 573.3 433.25999999999993 575.76 440.74999999999994 583.88 C 442.21578894225973 585.5568245011211 443.55292044581785 587.3418950583713 444.74999999999994 589.22 C 449.28999999999996 595.94 455.99999999999994 595.97 460.56999999999994 589.3000000000001 C 461.8424498416103 587.288952107973 463.27367800260544 585.3828772206664 464.8499999999999 583.6 C 472.9199999999999 575.21 484.8499999999999 573.1800000000001 493.9699999999999 578.5400000000001 C 503.5199999999999 584.1700000000001 507.87999999999994 595.7500000000001 503.56999999999994 607.2300000000001 C 501.435452975857 613.2041979919766 498.0577634521533 618.6575816831228 493.6599999999999 623.2300000000001 C 480.62 636 466.84 648 452.53 661 Z" stroke-linecap="round" />
</g>
</g>
</g>
</svg>