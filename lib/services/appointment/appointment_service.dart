import 'dart:math';

import 'package:dio/dio.dart';
import 'package:intl/intl.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/data/models/appointment/doctor_bookable_info_model.dart';
import 'package:samitivej_flutter_app/data/models/guardian/ward_hn_model.dart';
import 'package:samitivej_flutter_app/models/all_type_appointment_model.dart';
import 'package:samitivej_flutter_app/models/appointment_patient_model.dart';
import 'package:samitivej_flutter_app/models/hospital_model.dart';
import 'package:samitivej_flutter_app/models/interpreter_model.dart';
import 'package:samitivej_flutter_app/models/nationality_model.dart';
import 'package:samitivej_flutter_app/models/response_model.dart';
import 'package:samitivej_flutter_app/models/speciality_model.dart';
import 'package:samitivej_flutter_app/services/api_client.dart';
import 'package:samitivej_flutter_app/utils/exception_handle.dart';
import 'dart:developer' as developer;

class AppointmentService {
  static final _client = ApiClient();
  static final _clientWithTimeout10s = ApiClient(
    connectTimeout: 10000,
    receiveTimeout: 10000,
  );

  static Future<List<HospitalModel>> getHospital() async {
    try {
      final respModel = await _client.get(ApiConstant.getHospitals);
      final rawResp = respModel.data?['listData'] as List<dynamic>?;
      if (rawResp != null) {
        final result = rawResp.map((e) {
          return HospitalModel.fromJson(e as Map<String, dynamic>);
        }).toList();

        return result;
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<String> getHospitalContact({
    required String hospitalId,
    String? specialtyCode,
  }) async {
    try {
      final String queryParam =
          'hospitalId=$hospitalId${specialtyCode != null ? '&code=$specialtyCode' : ''}';
      final resp = await _client.get(
        '${ApiConstant.getHospitalContact}?$queryParam',
      );
      final rawResp = resp.data;
      if (rawResp != null && rawResp.containsKey('specialityTel')) {
        return rawResp['specialityTel'] as String;
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<List<SpecialityModel>> getSpecialtyList({
    String? site,
    bool? isHospital,
  }) async {
    try {
      String param = site != null || isHospital != null ? '?' : '';
      if (site != null) param += 'hospitalId=$site';
      if (isHospital != null) {
        param += '${site != null ? '&' : ''}isVirtual=${!isHospital}';
      }

      final resp = await _client.get('${ApiConstant.getSpecialties}$param');
      final rawResp = resp.data?['listData'] as List<dynamic>?;

      if (rawResp != null) {
        final result = rawResp.map((e) {
          return SpecialityModel.fromJson(e as Map<String, dynamic>);
        }).toList();

        return result;
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<List<InterpreterModel>> getInterpreterList() async {
    try {
      final resp = await _client.get(ApiConstant.getInterpreters);
      final rawResp = resp.data?['listData'] as List<dynamic>?;
      if (rawResp != null) {
        final result = rawResp.map((e) {
          return InterpreterModel.fromJson(e as Map<String, dynamic>);
        }).toList();

        return result;
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<List<NationalityModel>> getNationalities() async {
    try {
      final resp = await _client.get(ApiConstant.getNationalities);
      final rawResp = resp.data?['listData'] as List<dynamic>?;
      if (rawResp != null) {
        final result = rawResp.map((e) {
          return NationalityModel.fromJson(e as Map<String, dynamic>);
        }).toList();

        return result;
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<AppointmentPatientModel> getAppointmentPatient() async {
    try {
      final resp = await _client.post(
        ApiConstant.getPatient,
        data: {'patientId': 0},
      );

      final rawResp = resp.data;
      if (rawResp != null) {
        return AppointmentPatientModel.fromJson(rawResp);
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<bool> getIsPatientHN() async {
    try {
      final resp = await _client.get(ApiConstant.getCustomer);
      final rawResp = resp.data;
      if (rawResp != null) {
        return rawResp['ssid'] != null &&
            (rawResp['ssid'] as String).isNotEmpty;
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<Map<String, List<AllTypeAppointmentModel>>>
      getWellAppointments() async {
    try {
      final ResponseModel res = await _client.post(ApiConstant.getAppointment);
      if (res.data != null) {
        Map<String, List<AllTypeAppointmentModel>> data = {};
        res.data!.forEach((key, value) {
          data[key] = [];
          var listData = value as List;
          for (var element in listData) {
            var map = element as Map<String, dynamic>;
            if (!map.keys.contains('appointmentCode')) {
              map['appointmentCode'] = map['appointmentId'].toString();
            }
            map['type'] = 'appointment';
            data[key]?.add(
              AllTypeAppointmentModel.fromJson(map),
            );
          }
        });
        return data;
      } else {
        return {};
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<AllTypeAppointmentModel?> createAppointment({
    required String doctorId,
    required bool isAleadyMeetDoctor,
    required bool isCreateForSelf,
    required String name,
    required String phone,
    required String dateOfBirth,
    required String site,
    required String symptoms,
    required bool isHospital,
    required String startAppointmentDateTime,
    required String endAppointmentDateTime,
    required bool isInterpretation,
    required String interpretation,
    required List<String> symptomImgUrl,
    required String doctorLocation,
  }) async {
    try {
      final data = {
        'isDoctorOldPatient': isAleadyMeetDoctor,
        'isUser': isCreateForSelf,
        'name': name,
        'phone': phone,
        'dateOfBirth': dateOfBirth,
        'site': site,
        'doctorId': doctorId,
        'isVirtual': !isHospital,
        'startTime': startAppointmentDateTime,
        'endTime': endAppointmentDateTime,
        'symptoms': symptoms,
        'symptomImgUrl': symptomImgUrl,
        'isNeedInterpreter': isInterpretation,
        'interpreter': interpretation,
        'location': doctorLocation,
      };

      final resp =
          await _client.post(ApiConstant.createAppointment, data: data);
      final rawResp = resp.data;
      if (rawResp != null) {
        return AllTypeAppointmentModel.fromJson(rawResp);
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<DoctorBookableInfoModel> getDoctorBookableInfo({
    required String doctorId,
    required String site,
  }) async {
    try {
      final responseModel =
          await _client.post(ApiConstant.doctorBookableInfo, data: {
        'doctorId': doctorId,
        'site': site,
      });
      if (responseModel.code == 200 && responseModel.data != null) {
        return DoctorBookableInfoModel.fromJson(responseModel.data!);
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<AllTypeAppointmentModel?> bookingAppointment({
    required String slotId,
    required String doctorId,
    required bool isAlreadyMeetDoctor,
    required bool isCreateForSelf,
    required String name,
    required String phone,
    required String dateOfBirth,
    required String site,
    required String symptoms,
    required bool isHospital,
    required String appointmentDate,
    required String appointmentTime,
    required String startAppointmentDateTime,
    required String endAppointmentDateTime,
    required bool isInterpretation,
    required String interpretation,
    required List<String> symptomImgUrl,
    required String locationCode,
    required String doctorLocation,
  }) async {
    try {
      final data = {
        'doctorCode': doctorId,
        'locationCode': locationCode,
        'slotId': slotId,
        'appointmentDate': appointmentDate,
        'appointmentTime': appointmentTime,
        'isDoctorOldPatient': isAlreadyMeetDoctor,
        'isUser': isCreateForSelf,
        'name': name,
        'phone': phone,
        'dateOfBirth': dateOfBirth,
        'site': site,
        'doctorId': doctorId,
        'isVirtual': !isHospital,
        'startTime': startAppointmentDateTime,
        'endTime': endAppointmentDateTime,
        'symptoms': symptoms,
        'symptomImgUrl': symptomImgUrl,
        'isNeedInterpreter': isInterpretation,
        'interpreter': interpretation,
        'location': doctorLocation,
      };

      final newClient = ApiClient(
        connectTimeout: 10000,
        receiveTimeout: 10000,
      );
      final resp =
          await newClient.post(ApiConstant.bookingAppointment, data: data);

      final rawResp = resp.data;
      if (rawResp != null) {
        try {
          return AllTypeAppointmentModel.fromJson(rawResp);
        } catch (_) {
          return null;
        }
      } else {
        developer.log(rawResp.toString(),
            name: 'AppointmentService.bookingAppointment');
        return null;
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<void> saveSearch({required String condition}) async {
    try {
      await _client.post(
        ApiConstant.saveSearch,
        data: {'word': condition},
      );
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<Map<String, List<String>>> getTopAndRecentSearch() async {
    try {
      final resp = await _client.get(ApiConstant.getRecentSearch);
      final rawResp = resp.data;
      if (rawResp != null) {
        final recentSearch = rawResp['userSearchHistory'] as List<dynamic>;
        final mappedRecent = recentSearch
            .map<String>((e) => (e['word'] as String?) ?? '')
            .toList();

        final topSearch = rawResp['maxSearch'] as List<dynamic>;
        final mappedTop =
            topSearch.map<String>((e) => (e['word'] as String?) ?? '').toList();
        return {
          'top': mappedTop.sublist(0, min(5, mappedTop.length)),
          'recent': mappedRecent.sublist(0, min(5, mappedRecent.length)),
        };
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<List<AllTypeAppointmentModel>?> getAllAppointments() async {
    try {
      final resp = await _client.get(ApiConstant.getAllAppointments);
      final rawResp = resp.data;
      if (rawResp != null) {
        List<AllTypeAppointmentModel> appointments = [];
        for (Map<String, dynamic> item in rawResp['listData']) {
          appointments.add(AllTypeAppointmentModel.fromJson(item));
        }
        return appointments;
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<List<AllTypeAppointmentModel>?>
      getGuardianUpcomingAppointments() async {
    try {
      final resp =
          await _client.get(ApiConstant.getGuardianUpcomingAppointments);
      final rawResp = resp.data;
      if (rawResp != null) {
        List<AllTypeAppointmentModel> appointments = [];
        for (Map<String, dynamic> item in rawResp['listData']) {
          appointments.add(AllTypeAppointmentModel.fromJson(item));
        }
        return appointments;
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<Map<String, dynamic>?> deleteAppointment(
      {required int? appointmentId,
      required String startTime,
      required String endTime,
      required bool isNeedInterpreter,
      required String interpreter,
      required String symptoms,
      required List<String> symptomImgUrl,
      required String doctorId,
      required String doctorName,
      required String location,
      required String site,
      required String name,
      String? hn}) async {
    final payload = {
      "appointmentId": appointmentId,
      "startTime": startTime,
      "endTime": endTime,
      "isNeedInterpreter": isNeedInterpreter,
      "interpreter": interpreter,
      "symptoms": symptoms,
      "symptomImgUrl": symptomImgUrl,
      "doctorId": doctorId,
      "doctorName": doctorName,
      "location": location,
      "site": site,
      "name": name,
    };
    if (hn != null) {
      payload['hn'] = hn;
    }
    try {
      final resp = await _clientWithTimeout10s
          .post(ApiConstant.deleteAppointment, data: payload);
      return resp.data;
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  static Future<Map<String, dynamic>?> updateAppointment({
    required int? appointmentId,
    required String slotId,
    required String startTime,
    required String endTime,
    required bool isNeedInterpreter,
    required String interpreter,
    required String symptoms,
    required List<String> symptomImgUrl,
    required String doctorId,
    required String doctorName,
    required String locationCode,
    required String location,
    required String site,
    required DateTime lastStartTime,
    required String name,
    required bool isUser,
    String? hn,
    String? dob,
  }) async {
    final payload = {
      "appointmentId": appointmentId,
      "slotId": slotId,
      "startTime": startTime,
      "endTime": endTime,
      "isNeedInterpreter": isNeedInterpreter,
      "interpreter": interpreter,
      "symptoms": symptoms,
      "symptomImgUrl": symptomImgUrl,
      "doctorId": doctorId,
      "doctorName": doctorName,
      "locationCode": locationCode,
      "location": location,
      "site": site,
      "lastStartTime": lastStartTime.toIso8601String(),
      "name": name,
      "isUser": isUser
    };
    if (hn != null) {
      payload['hn'] = hn;
    }
    if (dob != null) {
      var birthDate = DateTime.tryParse(dob);
      if (birthDate != null) {
        payload['birthDate'] =
            DateFormat('yyyy-MM-ddTHH:mm:ss').format(birthDate);
      }
    }
    try {
      var resp = await _clientWithTimeout10s.post(ApiConstant.updateAppointment,
          data: payload);
      return resp.data;
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  /// Get ward HNs for appointment
  ///
  /// This function fetches the list of ward HNs that are available for appointment.
  /// It returns a list of WardHnModel objects.
  ///
  /// Parameters:
  /// - wardId: The ID of the ward to get the HNs for.
  ///
  /// Returns:
  /// - A list of WardHnModel objects representing the available ward HNs.
  static Future<WardHnModelList> getWardHns(int wardId) async {
    try {
      final resp = await _client
          .get(ApiConstant.getAppointmentWardHns, queryParameters: {
        'wardId': wardId,
      });
      final rawResp = resp.data as List<dynamic>?;
      if (rawResp != null) {
        return rawResp.map((e) => WardHnModel.fromJson(e)).toList();
      } else {
        return [];
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }
}
