import 'dart:async';
import 'dart:math';

import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/apps/appointment/plus_appointment_app.dart';
import 'package:samitivej_flutter_app/apps/cart/cart_app.dart';
import 'package:samitivej_flutter_app/apps/coin/coin_app.dart';
import 'package:samitivej_flutter_app/apps/feature_flags/feature_flags_app.dart';
import 'package:samitivej_flutter_app/apps/health_app/health_app.dart';
import 'package:samitivej_flutter_app/apps/home/<USER>';
import 'package:samitivej_flutter_app/apps/notification/notification_app.dart';
import 'package:samitivej_flutter_app/apps/payment/payment2_app.dart';
import 'package:samitivej_flutter_app/apps/queue/queue_app.dart';
import 'package:samitivej_flutter_app/apps/user/user_app.dart';
import 'package:samitivej_flutter_app/commons/api/network_info.dart';
import 'package:samitivej_flutter_app/commons/di/app_injector.dart';
import 'package:samitivej_flutter_app/commons/services/analytics_service.dart';
import 'package:samitivej_flutter_app/components/confirm_dialog.dart';
import 'package:samitivej_flutter_app/components/custom_shimmer.dart';
import 'package:samitivej_flutter_app/components/filled_button_widget.dart';
import 'package:samitivej_flutter_app/components/screen_with_bg_image.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/constants/app_asset.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/constants/app_constant.dart';
import 'package:samitivej_flutter_app/constants/app_layout.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';
import 'package:samitivej_flutter_app/localization/app_translate_key.dart';
import 'package:samitivej_flutter_app/models/popup_model.dart';
import 'package:samitivej_flutter_app/presentation/widgets/dialog/no_internet_dialog.dart';
import 'package:samitivej_flutter_app/screen/home/<USER>/advertise_dialog.dart';
import 'package:samitivej_flutter_app/screen/home/<USER>/avatar_home_header.dart';
import 'package:samitivej_flutter_app/screen/home/<USER>/banner_slider.dart';
import 'package:samitivej_flutter_app/screen/home/<USER>/coin_badge.dart';
import 'package:samitivej_flutter_app/screen/home/<USER>/daily_task_section.dart';
import 'package:samitivej_flutter_app/screen/home/<USER>/discover_section.dart';
import 'package:samitivej_flutter_app/screen/home/<USER>/doctor_online_section.dart';
import 'package:samitivej_flutter_app/screen/home/<USER>/ecoupon_badge.dart';
import 'package:samitivej_flutter_app/screen/home/<USER>/health_tracker_section2.dart';
import 'package:samitivej_flutter_app/screen/home/<USER>/how_you_feel_section.dart';
import 'package:samitivej_flutter_app/screen/home/<USER>/pending_invoivces_section.dart';
import 'package:samitivej_flutter_app/screen/home/<USER>/product_feed_section.dart';
import 'package:samitivej_flutter_app/screen/home/<USER>/services_section.dart';
import 'package:samitivej_flutter_app/screen/home/<USER>/upcoming_section.dart';
import 'package:samitivej_flutter_app/screen/home/<USER>/username_section.dart';
import 'package:samitivej_flutter_app/screen/queue/components/activity_queue_card.dart';
import 'package:samitivej_flutter_app/screen/queue/components/error_queue_card.dart';
import 'package:samitivej_flutter_app/screen/setting/setting_child_screens/connect_hn_flow/connect_hn_wizard.dart';
import 'package:samitivej_flutter_app/services/storage_service.dart';
import 'package:samitivej_flutter_app/services/well_navigate_service.dart';
import 'package:samitivej_flutter_app/utils/extensions/date_format.dart';
import 'package:visibility_detector/visibility_detector.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  /// The appointment (plus) controller.
  final PlusAppointmentApp _plusAppointmentApp = Get.find<PlusAppointmentApp>();
  final NotificationApp _notificationApp = Get.find<NotificationApp>();
  final UserApp _userApp = Get.find<UserApp>();
  final CoinApp _coinApp = Get.find<CoinApp>();
  final HomeApp _homeApp = Get.find<HomeApp>();
  final CartApp _cartApp = Get.find<CartApp>();
  final HealthApp _healthApp = Get.find<HealthApp>();
  final QueueApp _queueApp = Get.find<QueueApp>();
  final FeatureFlagsApp _featureFlagsApp = Get.find<FeatureFlagsApp>();

  /// The payment 2 controller.
  final Payment2App _paymentApp = Get.find<Payment2App>();

  final ScrollController _controller = ScrollController();

  bool connectHnBarVisible = false;

  DateTime now = DateTime.now();

  AppsflyerSdk appsflyerSdk = AppsflyerSdk(AppsFlyerOptions(
    afDevKey: ApiConstant.appflyerKey,
    appId: ApiConstant.appflyerId,
    showDebug: true,
  ));

  Future<void> showAdvertising() async {
    await Future.delayed(Duration.zero);

    final storage = StorageService();
    final List<PopupModel> popupList = await _homeApp.getPopup();
    if (popupList.isEmpty) return;

    final lastViewTimestamp = storage.read<int?>(AppConstant.dayAdvertisingKey);
    final savedPopupIdList =
        (storage.read<List<dynamic>>(AppConstant.popupIdListKey) ?? [])
            .map((e) => e.toString())
            .toList();
    final popupIdList = popupList.map((e) => e.popupId).toList();

    if (lastViewTimestamp != null) {
      final savedTimestamp =
          DateTime.fromMillisecondsSinceEpoch(lastViewTimestamp);
      final now = DateTime.now();
      if (savedTimestamp.add(const Duration(hours: 12)).isAfter(now)) {
        return;
      }
    }

    if (lastViewTimestamp != null &&
        const DeepCollectionEquality().equals(savedPopupIdList, popupIdList)) {
      bool isSameDay(DateTime date1, DateTime date2) {
        return (date1.year == date2.year &&
            date1.month == date2.month &&
            date1.day == date2.day);
      }

      final savedTimestamp =
          DateTime.fromMillisecondsSinceEpoch(lastViewTimestamp);
      final now = DateTime.now();

      if (isSameDay(now, savedTimestamp)) {
        return;
      }
    }

    final dontShowThisAgain = await Get.dialog<bool>(
      AdvertisieDialog(popupList: popupList),
      barrierDismissible: false,
    );

    await Future.wait([
      storage.write(
        AppConstant.popupIdListKey,
        dontShowThisAgain == true ? popupIdList : null,
      ),
      storage.write(AppConstant.dayAdvertisingKey, now.millisecondsSinceEpoch),
    ]);
  }

  Future<void> checkNeedUserPermission() async {
    await Future.delayed(Duration.zero);
    if (_healthApp.needUserPermission &&
        _healthApp.askPermissionFirstTime.value) {
      final bool? grant = await Get.dialog<bool>(
        ConfirmDialog(
          message: AppTranslateKey.askhealthapppermission,
          cancelButtonLabel: AppTranslateKey.later,
          confirmButtonLabel: AppTranslateKey.ok,
          dismissable: false,
        ),
        barrierDismissible: false,
      );
      if (grant != null) {
        await _healthApp.setUserPermission(grant);
        if (grant) {
          await _healthApp.fetchHealthData(_healthApp.deviceStatus.value);
        }
      }
    }
  }

  Future<void> queueCheckIn() async {
    if (_queueApp.isScroll.value) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        await _controller.animateTo(
          270,
          duration: const Duration(seconds: 1),
          curve: Curves.easeInOut,
        );
        await Future.delayed(const Duration(milliseconds: 1500));
        _queueApp.isScroll.value = false;
        _queueApp.isHighlight.value = false;
      });
    }
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        // Get merchant IDs.
        _paymentApp.getMerchantIds();

        // Loads upcoming appointments.
        _queueApp.getQueueList();
        _queueApp.getCurrentQueueStatus();
        _notificationApp.setNotificationToken();
        _notificationApp.notificationListener();
        _coinApp.getTotalCoins();
        _healthApp.getDeviceInfo();
        if (_healthApp.deviceInfos.value != null) {
          if (_healthApp.deviceInfos.value!.device != null &&
              !_healthApp.needUserPermission) {
            _healthApp.fetchHealthData(_healthApp.deviceInfos.value!.device);
          } else {
            checkNeedUserPermission();
          }
        }
      },
    );
    showAdvertising();
    if (_queueApp.isScroll.value) {
      queueCheckIn();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _controller.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      if (kDebugMode) {
        debugPrint('Home Screen is resumed');
      }
      _userApp.checkCustomerHn();
      _queueApp.getCurrentQueueStatus();
      queueCheckIn();
    }
  }

  // Track if the screen is currently visible to detect returning from other screens
  bool _isScreenReturnVisible = false;

  // This method will be called when returning to this screen from another screen
  void _onScreenReturnVisible() {
    if (!_isScreenReturnVisible) {
      if (kDebugMode) {
        debugPrint('Home Screen became visible again (back from other screen)');
      }

      _checkNetworkAndInitialize();
      _isScreenReturnVisible = true;
    }
  }

  /// Checks network connectivity and initializes screen data
  /// Uses async/await for better readability and error handling
  Future<void> _checkNetworkAndInitialize() async {
    try {
      final isConnected = await AppInjector.get<NetworkInfo>().isConnected;

      if (!isConnected) {
        _doTaskWhenNetworkNotAvailable();
        NoInternetDialog.show(
          onRetry: _checkNetworkAndInitialize,
        );
        return;
      }

      // Load screen data when connected
      _loadScreenData();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error checking network connectivity: $e');
      }

      // Show no internet dialog on any network check error
      NoInternetDialog.show(
        onRetry: _checkNetworkAndInitialize,
      );
    }
  }

  void _doTaskWhenNetworkNotAvailable() {
    _homeApp.shouldShowConnectHnBar.value = false;
    connectHnBarVisible = false;
  }

  /// Loads all screen data when network is available
  void _loadScreenData() {
    _userApp.getPlusUserInfo().then((_) {
      _checkShowConnectHnBar();
    });
    _queueApp.getCurrentQueueStatus();
    _plusAppointmentApp.getAllAppointments();

    if (_queueApp.isScroll.value) {
      queueCheckIn();
    }
  }

  // This method will be called when navigating away from this screen
  void _onScreenInvisible() {
    _isScreenReturnVisible = false;
    if (kDebugMode) {
      debugPrint('Home Screen became invisible (navigated away)');
    }
  }

  WellBackgroundFormat getTodayBackground({bool isTablet = false}) {
    final backgroundList = [
      WellBackgroundFormat(
        imagePath: AppAsset.homeValentine,
        start: DateTime(2024, 2, 1),
        end: DateTime(2024, 2, 29),
        isRecurring: true,
        top: 40,
        right: 0,
        width: (isTablet) ? 275 : 40.w,
      ),
      WellBackgroundFormat(
        imagePath: AppAsset.homeColon,
        start: DateTime(2023, 3, 1),
        end: DateTime(2023, 3, 31),
        isRecurring: true,
        top: 40,
        right: 0,
        width: (isTablet) ? 275 : 40.w,
      ),
      WellBackgroundFormat(
        imagePath: AppAsset.homeSongkran,
        start: DateTime(2024, 4, 1),
        end: DateTime(2024, 4, 30),
        isRecurring: true,
        top: 40,
        right: 0,
        width: (isTablet) ? 275 : 40.w,
      ),
      WellBackgroundFormat(
        imagePath: AppAsset.homePride,
        start: DateTime(2024, 6, 1),
        end: DateTime(2024, 6, 30),
        isRecurring: true,
        top: 40,
        right: 0,
        width: (isTablet) ? 275 : 40.w,
      ),
      WellBackgroundFormat(
        imagePath: AppAsset.homeMom,
        start: DateTime(2024, 8, 1),
        end: DateTime(2024, 8, 31),
        isRecurring: true,
        top: 75,
        right: (isTablet) ? 15 : -25,
        width: (isTablet) ? 275 : 40.w,
      ),
      WellBackgroundFormat(
        imagePath: AppAsset.homeLoyKraThong,
        start: DateTime(2023, 11, 1),
        end: DateTime(2023, 11, 30),
        isRecurring: true,
        top: 90,
        right: 0,
        width: (isTablet) ? 275 : 35.w,
      ),
      WellBackgroundFormat(
        imagePath: AppAsset.homeChristmas,
        start: DateTime(2024, 12, 1),
        end: DateTime(2024, 12, 31),
        isRecurring: true,
        top: 40,
        right: 0,
        width: (isTablet) ? 275 : 45.w,
      ),
      WellBackgroundFormat(
        imagePath: AppAsset.homeNewYear,
        start: DateTime(2024, 1, 1),
        end: DateTime(2024, 1, 31),
        isRecurring: true,
        top: 75,
        right: 0,
        width: (isTablet) ? 275 : 40.w,
      ),
    ];
    final debugBackground =
        backgroundList.firstWhereOrNull((element) => element.debug);
    if (debugBackground != null) return debugBackground;
    return backgroundList.firstWhere(
      (bg) {
        if (bg.isRecurring == true) {
          final thisYear = DateTime.now().year;
          final yearDiff = (bg.end.year - bg.start.year);
          final start = DateTime(thisYear, bg.start.month, bg.start.day);
          final end = DateTime(thisYear + yearDiff, bg.end.month, bg.end.day);
          return now.isAfter(start) && now.isBefore(end);
        } else {
          return now.isAfter(bg.start) && now.isBefore(bg.end);
        }
      },
      orElse: () => WellBackgroundFormat(
          imagePath: AppAsset.homeBGFade,
          start: DateTime(2020),
          end: DateTime(2030),
          top: -25,
          right: 0,
          width: (isTablet) ? 375 : 100.w),
    );
  }

  void _checkShowConnectHnBar() {
    bool? isHnConnected = _userApp.isHnConnected;
    if (isHnConnected == false) {
      final storage = StorageService();
      final dateTimeString =
          storage.read<int?>(AppConstant.connectHnBarTimestamp);
      if (dateTimeString == null) {
        _homeApp.shouldShowConnectHnBar.value = true;
        connectHnBarVisible = true;
      } else {
        DateTime nextAlert =
            DateTime.fromMillisecondsSinceEpoch(dateTimeString);
        nextAlert = nextAlert.setTime(setHour: 0, setMinute: 0, setSecound: 0);
        final now = DateTime.now();
        if (now.isAfter(nextAlert)) {
          _homeApp.shouldShowConnectHnBar.value = true;
          connectHnBarVisible = true;
        } else {
          _homeApp.shouldShowConnectHnBar.value = false;
          connectHnBarVisible = false;
        }
      }
    } else if (isHnConnected == true) {
      _homeApp.shouldShowConnectHnBar.value = false;
      connectHnBarVisible = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool isTablet = MediaQuery.of(context).size.width >= 768;
    final todayBackground = getTodayBackground(isTablet: isTablet);
    appsflyerSdk
        .logEvent("Home_interaction", {"af_event_start": now.toString()});
    return MediaQuery(
      data: MediaQuery.of(context).copyWith(textScaler: TextScaler.noScaling),
      child: RefreshIndicator(
        key: const Key('HomeScreen'),
        color: AppColor.green700,
        onRefresh: () async {
          _coinApp.getTotalCoins();
          _plusAppointmentApp.getAllAppointments();
          _homeApp.getDataHome();
          _cartApp.getPendingPaymentInvoices();
          _userApp.getPlusUserInfo();
          _queueApp.getQueueList();
          _queueApp.getCurrentQueueStatus();
          _featureFlagsApp.updateFlags();
        },
        child: VisibilityDetector(
          key: const Key('main_home_screen'),
          onVisibilityChanged: (visibilityInfo) {
            if (visibilityInfo.visibleFraction == 1.0) {
              // Screen is visible - may be initial load or returning from another screen
              _onScreenReturnVisible();
            } else if (visibilityInfo.visibleFraction == 0.0) {
              // Screen is not visible - navigated away to another screen
              _onScreenInvisible();
            }
          },
          child: Stack(
            children: [
              ScreenWithBGImage(
                scrollController: _controller,
                padding: const EdgeInsets.only(left: 0),
                isHasScroll: true,
                children: [
                  if (_featureFlagsApp.homeConnectHNBar.value)
                    Obx(() {
                      return AnimatedPadding(
                        padding: EdgeInsets.only(
                          top: _homeApp.shouldShowConnectHnBar.value ? 160 : 0,
                        ),
                        curve: Curves.easeOut,
                        duration: const Duration(
                          milliseconds: 250,
                        ),
                      );
                    }),
                  Stack(
                    children: [
                      Positioned(
                        top: todayBackground.top,
                        right: todayBackground.right,
                        child: Image.asset(
                          todayBackground.imagePath,
                          width: todayBackground.width,
                          alignment: Alignment.topCenter,
                        ),
                      ),
                      Column(
                        children: [
                          Obx(
                            () => ColorFiltered(
                              colorFilter: _queueApp.isHighlight.isTrue
                                  ? ColorFilter.mode(
                                      Colors.black.withOpacity(0.6),
                                      BlendMode.srcOver)
                                  : const ColorFilter.mode(
                                      Colors.transparent, BlendMode.srcOver),
                              child: const AvatarHomeHeader(),
                            ),
                          ),
                          ConstrainedBox(
                            constraints:
                                BoxConstraints(maxWidth: min(100.w, 775)),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const UsernameSection(),
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 0,
                                    vertical: 8,
                                  ),
                                  child: Row(
                                    children: [
                                      GestureDetector(
                                        onTap: () async {
                                          await AnalyticsService
                                              .logEventHomeInteraction(
                                                  areaName: 'coins');
                                          if (!mounted) return;
                                          WellNavigationService.openCoin();
                                        },
                                        child: const CoinBadge(),
                                      ),
                                      if (_featureFlagsApp.homeEcoupon.value)
                                        const Padding(
                                          padding: EdgeInsets.only(left: 15),
                                          child: ECouponBadge(),
                                        ),
                                    ],
                                  ),
                                ),
                                AppLayout.mediumH,
                                Obx(
                                  () => _queueApp.currentQueue.value.isEmpty
                                      ? const SizedBox.shrink()
                                      : _queueApp.isGetCurrentQueueLoading.value
                                          ? Container(
                                              margin: const EdgeInsets.only(
                                                  bottom: 20),
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 15),
                                              child: CustomShimmer.container(
                                                width: 100.w,
                                                height: 300,
                                              ),
                                            )
                                          : _queueApp.currentQueue.value.first
                                                  .currentStatus!
                                                  .contains("no service")
                                              ? const ErrorQueueCard()
                                              : Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    ..._queueApp
                                                        .currentQueue.value
                                                        .asMap()
                                                        .entries
                                                        .map((e) =>
                                                            ActivityQueueCard(
                                                              currentStatus:
                                                                  e.value,
                                                            )),
                                                  ],
                                                ),
                                ),
                                Obx(
                                  () => ColorFiltered(
                                    colorFilter: _queueApp.isHighlight.isTrue
                                        ? ColorFilter.mode(
                                            Colors.black.withOpacity(0.6),
                                            BlendMode.srcOver)
                                        : const ColorFilter.mode(
                                            Colors.transparent,
                                            BlendMode.srcOver),
                                    child: Column(
                                      children: [
                                        if (_featureFlagsApp.homeServices.value)
                                          const ServicesSection(),
                                        if (_featureFlagsApp.homeBanners.value)
                                          BannerSlider(
                                            onBannerPressed: (redirectUrl) {
                                              WebUri url = WebUri(redirectUrl);
                                              if (!url.isValidUri) return false;

                                              if (WellNavigationService
                                                  .isWellDhvLife(url)) {
                                                final path = WellNavigationService
                                                    .getInitialRouteWithQuery(
                                                        url);
                                                if (path.startsWith("/?") ||
                                                    path.startsWith("/home?")) {
                                                  _controller.animateTo(
                                                    0,
                                                    duration: const Duration(
                                                        milliseconds: 200),
                                                    curve: Curves.easeOut,
                                                  );
                                                  return true;
                                                }
                                              }
                                              return false;
                                            },
                                          ),
                                        if (_featureFlagsApp
                                            .homePendingPayments.value)
                                          const PendingInvoicesSection(),
                                        if (_featureFlagsApp
                                            .homeDoctorConsult.value)
                                          const DoctorConsultation(),
                                        if (_featureFlagsApp
                                            .homeUpcomings.value)
                                          GestureDetector(
                                            onTap: () async {
                                              await AnalyticsService
                                                  .logEventHomeInteraction(
                                                areaName: 'upcoming',
                                              );
                                            },
                                            child: UpcomingSection(
                                              title:
                                                  AppLocalizations.of(context)!
                                                      .upcoming,
                                              titleTextStyle:
                                                  AppText.theme(context)
                                                      .titleLarge,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 15,
                                              ),
                                            ),
                                          ),
                                        if (_featureFlagsApp
                                            .homeProductFeed.value)
                                          ProductFeedSection(
                                            onProductFeedPressed:
                                                (redirectUrl) {
                                              WebUri url = WebUri(redirectUrl);
                                              if (!url.isValidUri) return false;

                                              if (WellNavigationService
                                                  .isWellDhvLife(url)) {
                                                final path = WellNavigationService
                                                    .getInitialRouteWithQuery(
                                                        url);
                                                if (path.startsWith("/?") ||
                                                    path.startsWith("/home?")) {
                                                  _controller.animateTo(
                                                    0,
                                                    duration: const Duration(
                                                        milliseconds: 200),
                                                    curve: Curves.easeOut,
                                                  );
                                                  return true;
                                                }
                                              }
                                              return false;
                                            },
                                          ),
                                        if (_featureFlagsApp
                                            .homeHealthTrackers.value)
                                          HealthTrackerSection2(),
                                        if (_featureFlagsApp.homeMoods.value)
                                          const HowYouFeelSection(),
                                        if (_featureFlagsApp
                                                .homeFitDailyTasks.value ||
                                            _featureFlagsApp
                                                .homeFoodDailyTasks.value)
                                          const DailyTaskSection(),
                                        if (_featureFlagsApp
                                            .homeDiscovers.value)
                                          const DiscoverSection(),
                                        SizedBox(height: min(5.h, 50)),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  )
                ],
              ),
              if (_featureFlagsApp.homeConnectHNBar.value)
                Obx(() {
                  return Visibility(
                    visible: connectHnBarVisible,
                    child: SafeArea(
                      child: _AskConnectHnBar(
                        shouldShowConnectHnBar:
                            _homeApp.shouldShowConnectHnBar.value,
                        onTapConnect: () async {
                          await AnalyticsService.logEventHomeInteraction(
                              areaName: 'connect hn button');
                          await HomeApp.postponeConnectHnBar(
                              snoozeFor: const Duration(days: 14));
                          Get.to<bool>(const ConnectHNWizard());
                        },
                        onTapCancel: () async {
                          await AnalyticsService.logEventHomeInteraction(
                              areaName: 'later connect hn button');
                          await HomeApp.postponeConnectHnBar(
                              snoozeFor: const Duration(days: 14));
                          setState(() {
                            _homeApp.shouldShowConnectHnBar.value =
                                !_homeApp.shouldShowConnectHnBar.value;
                          });
                        },
                        onEnd: () {
                          setState(() {
                            connectHnBarVisible =
                                _homeApp.shouldShowConnectHnBar.value;
                          });
                        },
                      ),
                    ),
                  );
                }),
            ],
          ),
        ),
      ),
    );
  }
}

class WellBackgroundFormat {
  final String imagePath;
  final DateTime start;
  final DateTime end;
  final bool isRecurring;
  final double top;
  final double right;
  final double width;
  final bool debug;

  WellBackgroundFormat({
    required this.imagePath,
    required this.start,
    required this.end,
    this.isRecurring = false,
    required this.top,
    required this.right,
    required this.width,
    this.debug = false,
  });
}

class _AskConnectHnBar extends StatelessWidget {
  const _AskConnectHnBar({
    super.key,
    required this.shouldShowConnectHnBar,
    required this.onTapConnect,
    required this.onTapCancel,
    required this.onEnd,
  });

  final void Function() onTapConnect;
  final void Function() onTapCancel;
  final void Function() onEnd;
  final bool shouldShowConnectHnBar;

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: shouldShowConnectHnBar ? 1 : 0,
      duration: const Duration(milliseconds: 300),
      onEnd: onEnd,
      child: Container(
        width: 100.w,
        padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 15),
        decoration: BoxDecoration(
          color: const Color(0xFFFBFEF2),
          boxShadow: [
            BoxShadow(
              offset: const Offset(2, 5),
              color: Colors.black.withOpacity(0.1),
              blurRadius: 5,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                    width: 38,
                    height: 38,
                    padding: const EdgeInsets.all(8),
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Color(0xFFC2AB52),
                    ),
                    child: SvgPicture.asset(
                      AppAsset.samitivejServiceIcon,
                    )),
                SizedBox(
                  width: 16.px,
                ),
                Expanded(
                  child: Text(
                    AppLocalizations.of(context)!.connectHnBarDetail,
                    softWrap: true,
                    maxLines: 3,
                    style: AppText.theme(context).titleMedium,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 16.px,
            ),
            Row(
              children: [
                Expanded(
                  child: FilledButtonWidget(
                    label: AppTranslateKey.connectpatientprofile,
                    labelStyle: AppText.theme(context).titleMedium!.copyWith(
                          color: AppColor.black,
                        ),
                    onPressed: onTapConnect,
                  ),
                ),
                SizedBox(
                  width: 16.px,
                ),
                FilledButtonWidget(
                  label: AppTranslateKey.later,
                  borderColor: AppColor.primaryGreen4,
                  buttonColor: AppColor.white,
                  padding: const EdgeInsets.symmetric(horizontal: 25),
                  boxConstraints: BoxConstraints.tightFor(
                    height: min(14.w, 50),
                  ),
                  labelStyle: AppText.theme(context).titleMedium!.copyWith(
                        color: AppColor.primaryGreen4,
                      ),
                  onPressed: onTapCancel,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
