import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/components/click_resize_button.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';

class TwoChoicesDialog {
  Future<void> open({
    String title = '',
    String textContinue = '',
    String textCancel = '',
    Color backgroundColorContinue = AppColor.grey750,
    Color backgroundColorCancel = AppColor.green300,
    VoidCallback? onPressedContinue,
    VoidCallback? onPressedCancel,
  }) async {
    await Get.dialog<void>(
      barrierDismissible: false,
      _TwoChoicesWidget(
        title: title,
        textContinue: textContinue,
        textCancel: textCancel,
        backgroundColorContinue: backgroundColorContinue,
        backgroundColorCancel: backgroundColorCancel,
        onPressedContinue: onPressedContinue,
        onPressedCancel: onPressedCancel,
      ),
    );
  }
}

class _TwoChoicesWidget extends StatelessWidget {
  const _TwoChoicesWidget({
    required this.title,
    required this.textContinue,
    required this.textCancel,
    this.onPressedContinue,
    this.onPressedCancel,
    required this.backgroundColorContinue,
    required this.backgroundColorCancel,
  });

  final String title;
  final String textContinue;
  final String textCancel;
  final Color backgroundColorContinue;
  final Color backgroundColorCancel;
  final VoidCallback? onPressedContinue;
  final VoidCallback? onPressedCancel;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            margin: EdgeInsets.symmetric(horizontal: 5.w),
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(10)),
              color: Colors.white,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    ClickResizeButton(
                      onPressed: () => Get.back<void>(),
                      child: Icon(
                        Icons.close,
                        size: 20.sp,
                        color: AppColor.closeDialog,
                      ),
                    )
                  ],
                ),
                const SizedBox(height: 10),
                Center(
                  child: Text(
                    title,
                    style: AppText.theme(context).bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    _buildButton(
                      context,
                      textCancel,
                      color: backgroundColorCancel,
                      onPressed: () {
                        if (onPressedCancel != null) onPressedCancel!();
                        Get.back<void>();
                      },
                    ),
                    SizedBox(width: 5.w),
                    _buildButton(
                      context,
                      textContinue,
                      color: backgroundColorContinue,
                      onPressed: () {
                        if (onPressedContinue != null) onPressedContinue!();
                        Get.back<void>();
                      },
                    ),
                  ],
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildButton(
    BuildContext context,
    String title, {
    VoidCallback? onPressed,
    Color color = AppColor.green300,
  }) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(200),
          ),
        ),
      ),
      onPressed: onPressed,
      child: Container(
        constraints: BoxConstraints(minWidth: 15.w, maxWidth: 20.w),
        child: Center(
          child: Text(
            title,
            style: AppText.theme(context).bodyMedium!.copyWith(
                  color: AppColor.black,
                ),
          ),
        ),
      ),
    );
  }
}
