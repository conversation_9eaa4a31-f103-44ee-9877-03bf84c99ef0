import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/route_manager.dart';
import 'package:samitivej_flutter_app/components/custom_circle_button.dart';
import 'package:samitivej_flutter_app/constants/app_asset.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';

class CustomScaffold extends StatelessWidget {
  const CustomScaffold({
    super.key,
    required this.showAppBar,
    this.title,
    this.icon = CustomScaffoldIcon.back,
    required this.body,
  });

  final Widget body;
  final String? title;
  final CustomScaffoldIcon icon;
  final bool showAppBar;

  @override
  Widget build(BuildContext context) {
    final scaffoldTitle = title;
    return Scaffold(
      appBar: (showAppBar)
          ? AppBar(
              elevation: 0,
              backgroundColor: AppColor.scaffoldBackground,
              leading: GestureDetector(
                onTap: () {
                  Get.back<void>();
                },
                child: CustomCircleButton(
                  icon: icon == CustomScaffoldIcon.home
                      ? SvgPicture.asset(
                          AppAsset.appBarHome,
                          width: 16,
                          fit: BoxFit.scaleDown,
                          colorFilter: const ColorFilter.mode(
                              AppColor.green600, BlendMode.srcIn),
                        )
                      : const Icon(
                          FontAwesomeIcons.chevronLeft,
                          color: AppColor.green600,
                          size: 16,
                        ),
                ),
              ),
              title: scaffoldTitle != null
                  ? Text(
                      scaffoldTitle,
                      style: AppText.theme(context).headlineSmall!.copyWith(
                            color: AppColor.black,
                          ),
                    )
                  : null,
              centerTitle: true,
            )
          : null,
      body: body,
    );
  }
}

enum CustomScaffoldIcon { back, home }
