import 'package:flutter/material.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';

class CustomGreenButton extends StatelessWidget {
  const CustomGreenButton({
    super.key,
    this.onPressed,
    this.child,
    this.color = AppColor.green300,
    this.disabledColor = AppColor.grey750,
  });

  final VoidCallback? onPressed;
  final Widget? child;
  final Color color;
  final Color disabledColor;

  @override
  Widget build(BuildContext context) {
    return MaterialButton(
      onPressed: onPressed,
      disabledColor: disabledColor,
      color: color,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(40)),
      child: child,
    );
  }
}
