import 'package:flutter/material.dart';
import 'package:get/route_manager.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';

class CustomDialogOld {
  CustomDialogOld._();

  static Future<T?> showAlertDialog<T>(
    BuildContext context,
    String title,
    String message,
  ) {
    return showDialog<T>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: <Widget>[
          TextButton(
            onPressed: () {
              Get.back<void>();
            },
            child: const Text(
              'Close',
              style: TextStyle(
                color: AppColor.oceanGreen,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
