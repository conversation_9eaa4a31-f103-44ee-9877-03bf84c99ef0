import 'package:flutter/material.dart';

class DashLine extends StatelessWidget {
  const DashLine({
    super.key,
    required this.line,
    this.lineColor,
  });

  final double line;
  final Color? lineColor;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 15, bottom: 5),
      child: Row(
        children: List.generate(
          line ~/ 10,
          (index) => Expanded(
            child: Container(
              color: index % 2 == 0 ? Colors.transparent : lineColor,
              height: 1,
            ),
          ),
        ),
      ),
    );
  }
}
