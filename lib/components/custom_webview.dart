import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/instance_manager.dart';
import 'package:get/route_manager.dart';
import 'package:location/location.dart';
import 'package:permission_handler/permission_handler.dart'
    as permission_handler;
import 'package:samitivej_flutter_app/apps/auth/auth_app.dart';
import 'package:samitivej_flutter_app/apps/common/common_app.dart';
import 'package:samitivej_flutter_app/apps/home/<USER>';
import 'package:samitivej_flutter_app/commons/services/analytics_service.dart';
import 'package:samitivej_flutter_app/components/confirm_dialog.dart';
import 'package:samitivej_flutter_app/components/custom_scaffold.dart';
import 'package:samitivej_flutter_app/constants/app_asset.dart';
import 'package:samitivej_flutter_app/localization/app_translate_key.dart';
import 'package:samitivej_flutter_app/models/fit_arguments/fit_arguments.dart';
import 'package:samitivej_flutter_app/routes/app_pages.dart';
import 'package:samitivej_flutter_app/screen/calendar/calendar_screen_w_scaffold.dart';
import 'package:samitivej_flutter_app/screen/cart/cart_webview_w_scaffold.dart';
import 'package:samitivej_flutter_app/screen/coins/coins_screen.dart';
import 'package:samitivej_flutter_app/screen/home_fit/views/home_fit_view.dart';
import 'package:samitivej_flutter_app/screen/notification/notification_screen.dart';
import 'package:samitivej_flutter_app/screen/setting/connect_device/new_customer_screen.dart';
import 'package:samitivej_flutter_app/screen/setting/setting_child_screens/connect_hn_flow/connect_hn_wizard.dart';
import 'package:samitivej_flutter_app/screen/stack_bar/home_stack_bar.dart';
import 'package:samitivej_flutter_app/services/well_navigate_service.dart';
import 'package:samitivej_flutter_app/utils/save_base64_image_to_gallery.dart';
import 'package:samitivej_flutter_app/utils/file_reader.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class CustomWebView extends StatefulWidget {
  const CustomWebView({
    super.key,
    required this.url,
    this.cookieWebUrl,
    this.onWebViewCreated,
    this.onLoadStop,
    this.onPermissionRequest,
    this.onProgressChanged,
    this.onUpdateVisitedHistory,
    this.initialSettings,
  });

  final WebUri url;
  final String? cookieWebUrl;
  final CustomWebViewSettings? initialSettings;

  final void Function(InAppWebViewController controller)? onWebViewCreated;
  final void Function(InAppWebViewController controller, WebUri? url)?
      onLoadStop;
  final Future<PermissionResponse?> Function(InAppWebViewController controller,
      PermissionRequest permissionRequest)? onPermissionRequest;
  final void Function(InAppWebViewController controller, int progress)?
      onProgressChanged;
  final void Function(
          InAppWebViewController controller, WebUri? url, bool? isReload)?
      onUpdateVisitedHistory;

  @override
  State<CustomWebView> createState() => _CustomWebViewState();
}

class _CustomWebViewState extends State<CustomWebView> {
  final HomeApp homeApp = Get.find<HomeApp>();
  final CommonApp commonApp = Get.find<CommonApp>();
  final CookieManager cookieManager = CookieManager.instance();

  InAppWebViewController? webViewController;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  String checkSamitivejDomain(WebUri url) {
    if (url.host.endsWith('samitivejhospitals.com')) {
      return url.host;
    } else {
      return "";
    }
  }

  Future<void> refresh(InAppWebViewController controller) async {
    final cookiesUrl =
        WebUri(widget.cookieWebUrl ?? checkSamitivejDomain(widget.url));
    if (cookiesUrl.isValidUri && cookiesUrl.rawValue.isNotEmpty) {
      AuthApp authApp = Get.find<AuthApp>();
      await authApp.setupCognitoIdToken();
      await commonApp.createCookie();
      final cookies = commonApp.cookies.toList();
      await cookieManager.deleteAllCookies();
      for (var cookie in cookies) {
        await cookieManager.setCookie(
          url: WebUri(widget.cookieWebUrl ?? checkSamitivejDomain(widget.url)),
          domain: cookie.domain,
          name: cookie.name,
          value: cookie.value,
        );
      }
      await controller.reload();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: InAppWebView(
        initialUrlRequest: URLRequest(
          url: widget.url,
        ),
        initialSettings: widget.initialSettings ?? CustomWebViewSettings(),
        onWebViewCreated: (controller) async {
          widget.onWebViewCreated?.call(controller);

          webViewController = controller;
          final cookiesUrl =
              WebUri(widget.cookieWebUrl ?? checkSamitivejDomain(widget.url));
          if (cookiesUrl.isValidUri && cookiesUrl.rawValue.isNotEmpty) {
            await commonApp.createCookie();
            final cookies = commonApp.cookies.toList();
            await cookieManager.deleteAllCookies();
            for (var cookie in cookies) {
              await cookieManager.setCookie(
                url: cookiesUrl,
                domain: cookie.domain,
                name: cookie.name,
                value: cookie.value,
              );
            }
          }

          controller.addJavaScriptHandler(
              handlerName: "launchUrl",
              callback: (args) async {
                final url = args[0];
                if (url is String) {
                  await launchUrl(Uri.parse(url));
                } else {
                  await launchUrl(
                    url,
                  );
                }
              });
          controller.addJavaScriptHandler(
              handlerName: "close",
              callback: (args) async {
                homeApp.getDataHome(
                  discover: false,
                  mood: false,
                  banner: false,
                );
                Get.back<void>();
                return true;
              });
          controller.addJavaScriptHandler(
              handlerName: "home",
              callback: (args) async {
                homeApp.getDataHome();
                Get.back();
                Get.offAll<void>(
                  const HomeStackBar(),
                  transition: Transition.noTransition,
                );
                return true;
              });
          controller.addJavaScriptHandler(
              handlerName: "shareLink",
              callback: (args) {
                try {
                  Share.share(
                    args[2] as String,
                    subject: args[0] as String,
                  );
                  return true;
                } catch (e) {
                  return false;
                }
              });

          controller.addJavaScriptHandler(
              handlerName: "shareFile",
              callback: (args) async {
                try {
                  final encodedStr = args[2] as String;
                  Uint8List bytes = base64.decode(encodedStr.split(",").last);
                  await Share.shareXFiles(
                    [
                      XFile.fromData(
                        bytes,
                        mimeType: encodedStr.split(";").first.split(":").last,
                      )
                    ],
                    subject: args[0] as String,
                    text: args[1] as String,
                  );
                  return true;
                } catch (e) {
                  return false;
                }
              });

          controller.addJavaScriptHandler(
              handlerName: "saveImage",
              callback: (args) async {
                try {
                  var status = await commonApp
                      .requestPermission(permission_handler.Permission.storage);
                  if (!status) {
                    status = await commonApp.requestPermission(
                        permission_handler.Permission.manageExternalStorage);
                  }
                  if (status) {
                    var result = await saveBase64ImageToGallery(args[0]);
                    Fluttertoast.showToast(
                        msg: result ??
                            AppTranslateKey.anerrorhasanoccured, // message
                        toastLength: Toast.LENGTH_SHORT, // length
                        gravity: ToastGravity.BOTTOM, // location
                        timeInSecForIosWeb: 5 // duration
                        );
                  } else {
                    Fluttertoast.showToast(
                        msg: AppTranslateKey.anerrorhasanoccured, // message
                        toastLength: Toast.LENGTH_SHORT, // length
                        gravity: ToastGravity.BOTTOM, // location
                        timeInSecForIosWeb: 5 // duration
                        );
                  }
                  return true;
                } catch (e) {
                  Fluttertoast.showToast(
                      msg: e.toString(), // message
                      toastLength: Toast.LENGTH_SHORT, // length
                      gravity: ToastGravity.BOTTOM, // location
                      timeInSecForIosWeb: 5 // duration
                      );
                  return false;
                }
              });

          controller.addJavaScriptHandler(
              handlerName: "getCurrentPosition",
              callback: (args) async {
                try {
                  Location location = Location();
                  bool serviceEnabled;
                  PermissionStatus permissionGranted;
                  LocationData locationData;

                  serviceEnabled = await location.serviceEnabled();
                  if (!serviceEnabled) {
                    serviceEnabled = await location.requestService();
                    if (!serviceEnabled) {
                      return {"code": "POSITION_UNAVAILABLE"};
                    }
                  }

                  permissionGranted = await location.hasPermission();
                  if (permissionGranted == PermissionStatus.denied) {
                    permissionGranted = await location.requestPermission();
                    if (permissionGranted != PermissionStatus.granted) {
                      return {"code": "PERMISSION_DENIED"};
                    }
                  }

                  locationData = await location.getLocation();
                  return {
                    "code": "SUCCESS",
                    "coords": {
                      "latitude": locationData.latitude,
                      "longitude": locationData.longitude,
                      "altitude": locationData.altitude,
                      "accuracy": locationData.accuracy,
                      "heading": locationData.heading,
                      "speed": locationData.speed
                    },
                    "timestamp": locationData.time
                  };
                } catch (e) {
                  return {"code": "PERMISSION_DENIED"};
                }
              });
          controller.addJavaScriptHandler(
              handlerName: "connectHn",
              callback: (args) async {
                final result =
                    await Get.to<bool>(() => const ConnectHNWizard());
                return result;
              });

          controller.addJavaScriptHandler(
              handlerName: "immPayment",
              callback: (args) async {
                CartWebviewWithScaffold.open();
              });

          controller.addJavaScriptHandler(
              handlerName: "calendar",
              callback: (args) async {
                Get.to<void>(
                  () => const CalendarScreenWithScaffold(),
                );
              });

          controller.addJavaScriptHandler(
              handlerName: "notification",
              callback: (args) async {
                Get.to<void>(const NotificationScreen());
              });

          controller.addJavaScriptHandler(
              handlerName: "chat",
              callback: (args) async {
                WellNavigationService.openChat();
              });

          controller.addJavaScriptHandler(
              handlerName: "coin",
              callback: (args) async {
                Get.to<void>(const CoinsScreen());
              });

          controller.addJavaScriptHandler(
              handlerName: "virtual",
              callback: (args) async {
                AnalyticsService.logEventHomeInteraction(
                  areaName: 'virtual button',
                );
                await WellNavigationService.openVirtual();
              });

          controller.addJavaScriptHandler(
              handlerName: "connectDevice",
              callback: (args) async {
                WellNavigationService.openConnectDevice(false);
              });

          controller.addJavaScriptHandler(
              handlerName: "appointment",
              callback: (args) async {
                WellNavigationService.openAppointment();
              });

          controller.addJavaScriptHandler(
              handlerName: "refresh",
              callback: (args) async {
                await refresh(controller);
              });
          await controller.reload();
        },
        onLoadStop: (controller, url) async {
          widget.onLoadStop?.call(controller, url);
          try {
            final wellApiJs =
                await FileReader.loadAsset(AppAsset.wellWebViewApi);
            await controller.evaluateJavascript(source: wellApiJs);
          } catch (_) {}
        },
        onProgressChanged: (controller, progress) {
          widget.onProgressChanged?.call(controller, progress);
        },
        onUpdateVisitedHistory: (controller, url, isReload) async {
          widget.onUpdateVisitedHistory?.call(controller, url, isReload);

          if (url == null) return;

          if (url.query.contains('mainapp')) {
            Get.back<void>();
          } else if (url.query.contains('calendarWell')) {
            Get.to<void>(const CalendarScreenWithScaffold());
          } else if (url.query.contains('noti')) {
            Get.to<void>(const NotificationScreen());
          } else if (url.query.contains('chatfood')) {
            await AnalyticsService.logEventHomeInteraction(
              areaName: 'chat food service food',
            );
            WellNavigationService.openChat(module: ChatModule.food);
          } else if (url.query.contains('chatfit')) {
            await AnalyticsService.logEventHomeInteraction(
              areaName: 'chat fit service fit',
            );
            WellNavigationService.openChat(module: ChatModule.fit);
          } else if (url.query.contains('chatmedrefill')) {
            await AnalyticsService.logEventHomeInteraction(
              areaName: 'chat fit service fit',
            );
            WellNavigationService.openChat(module: ChatModule.fit);
          } else if (url.query.contains('coin')) {
            WellNavigationService.openCoin();
          } else if (url.query.contains('virtualNow')) {
            AnalyticsService.logEventHomeInteraction(
              areaName: 'virtual button',
            );
            WellNavigationService.openVirtual();
          } else if (url.query.contains('connectdevice')) {
            WellNavigationService.openConnectDevice(false);
          } else if (url.query.contains('appointmentFood')) {
            WellNavigationService.openAppointment();
          } else if (url.query.contains('appointmentFit')) {
            WellNavigationService.openAppointment();
          } else if (url.query.contains('exercise-detail')) {
            Get.toNamed<void>(
              AppRoutes.fit,
              arguments: FitArguments(
                routineId: url.rawValue
                    .replaceAll(RegExp('.*exercise-detail[?]routineId='), ''),
              ),
            );
          } else if (url.rawValue
              .contains(Get.find<CommonApp>().fitTestAgain)) {
            await refresh(controller);
          } else if (url.rawValue
              .contains(Get.find<CommonApp>().fitDomainName)) {
            Get.to<void>(() => const HomeFitView());
          } else if (url.query.contains('httpstatuserror400')) {
            await refresh(controller);
          } else if (url.toString().contains('backtoflutter')) {
            homeApp.getDataHome();
            Get.back();
            Get.offAll<void>(
              const HomeStackBar(),
              transition: Transition.noTransition,
            );
          } else if (url.toString().contains('backtoterm')) {
            Get.off(NewCustomerScreen());
          }
        },
        shouldOverrideUrlLoading: (controller, navigationAction) async {
          final url = navigationAction.request.url;
          if (url != null) {
            if (url.toString().startsWith("data:image/png;base64")) {
              var result = await saveBase64ImageToGallery(url.toString());
              Fluttertoast.showToast(
                  msg: result ?? AppTranslateKey.anerrorhasanoccured, // message
                  toastLength: Toast.LENGTH_SHORT, // length
                  gravity: ToastGravity.BOTTOM, // location
                  timeInSecForIosWeb: 5 // duration
                  );
            }
            final bool isWellDhvLife = WellNavigationService.isWellDhvLife(url);
            final bool isWellCustomScheme = url.scheme == "wellbysamitivej";
            String initialRoute = "/";
            if (isWellDhvLife || isWellCustomScheme) {
              if (isWellDhvLife) {
                initialRoute =
                    WellNavigationService.getInitialRouteWithQuery(url);
              } else if (isWellCustomScheme) {
                initialRoute = "/${url.host.split(".").first}?${url.query}";
              }

              if (initialRoute == "/?") {
                homeApp.getDataHome();
                Get.back();
                return NavigationActionPolicy.CANCEL;
              } else if (initialRoute == "/home?") {
                WellNavigationService.openHome();
                return NavigationActionPolicy.CANCEL;
              } else if (url.toString() == "webview://close") {
                WellNavigationService.openHome();
                return NavigationActionPolicy.CANCEL;
              } else {
                final haveHandle = WellNavigationService.handleRoute(
                  initialRoute,
                );
                if (haveHandle) {
                  return NavigationActionPolicy.CANCEL;
                }
              }
            }

            if (!(url.scheme == "http" || url.scheme == "https")) {
              if (url.toString() == "webview://close") {
                WellNavigationService.openHome();
                return NavigationActionPolicy.CANCEL;
              }
              if (await canLaunchUrl(url)) {
                launchUrl(url);
              }
              return NavigationActionPolicy.CANCEL;
            }
          }
          return NavigationActionPolicy.ALLOW;
        },
        onPermissionRequest: widget.onPermissionRequest ??
            (controller, permissionRequest) async {
              return PermissionResponse(
                action: PermissionResponseAction.GRANT,
                resources: permissionRequest.resources,
              );
            },
        onReceivedServerTrustAuthRequest: (controller, challenge) async {
          return ServerTrustAuthResponse(
              action: ServerTrustAuthResponseAction.PROCEED);
        },
        onDownloadStartRequest: (controller, download) async {
          var result = await saveBase64ImageToGallery(download.url.toString());
          Fluttertoast.showToast(
              msg: result ?? AppTranslateKey.anerrorhasanoccured, // message
              toastLength: Toast.LENGTH_SHORT, // length
              gravity: ToastGravity.BOTTOM, // location
              timeInSecForIosWeb: 5 // duration
              );
        },
      ),
    );
  }
}

class CustomWebViewSettings extends InAppWebViewSettings {
  CustomWebViewSettings({
    super.userAgent = null,
    super.useHybridComposition = true,
    super.allowsInlineMediaPlayback = true,
    super.mediaPlaybackRequiresUserGesture = false,
    super.isInspectable = kDebugMode,
    super.supportZoom = false,
  }) : super();
}

class CustomWebViewWithScaffold extends StatefulWidget {
  const CustomWebViewWithScaffold({
    super.key,
    required this.url,
    this.showAppBar = false,
    this.title,
    this.service,
    this.icon,
    this.cookieWebUrl,
  });

  final WebUri url;
  final bool showAppBar;
  final String? title;
  final String? service;
  final CustomScaffoldIcon? icon;
  final String? cookieWebUrl;

  static Future<void> launch({
    required WebUri url,
    bool showAppBar = false,
    String? title,
    String? service,
    CustomScaffoldIcon? icon,
    required String? cookieWebUrl,
  }) async {
    Get.to(
      CustomWebViewWithScaffold(
        url: url,
        showAppBar: showAppBar,
        title: title,
        service: service,
        icon: icon,
        cookieWebUrl: cookieWebUrl,
      ),
      preventDuplicates: false,
    );
  }

  @override
  State<StatefulWidget> createState() => _CustomWebViewWithScaffoldState();
}

class _CustomWebViewWithScaffoldState extends State<CustomWebViewWithScaffold> {
  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      body: SafeArea(
        child: CustomWebView(
          url: widget.url,
          cookieWebUrl: widget.cookieWebUrl,
        ),
      ),
      showAppBar: widget.showAppBar,
      title: widget.title,
      icon: widget.icon ?? CustomScaffoldIcon.home,
    );
  }
}
