import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/components/app_text_style.dart';
import 'package:samitivej_flutter_app/components/custom_button.dart';
import 'package:samitivej_flutter_app/constants/app_dimen.dart';

class ErrorDialog extends StatelessWidget {
  const ErrorDialog({super.key, this.message = ''});

  /// Message.
  final String message;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(25),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Align(
              alignment: Alignment.topRight,
              child: IconButton(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 15,
                ),
                onPressed: () => Navigator.pop(context, false),
                icon: const Icon(Icons.close, color: Color(0xFFAAAAAA)),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(AppDimen.pagePadding),
              child: Text(
                message,
                style: AppTextStyle.dbAdmanXStyle(fontSize: 18.sp),
                textAlign: TextAlign.center,
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(AppDimen.pagePadding),
              child: Row(
                children: [
                  Expanded(
                    child: CustomButton(
                      label: AppLocalizations.of(context)!.close,
                      backgroundColor: const Color(0xFF8BA78D),
                      borderRadiusSize: 100,
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
