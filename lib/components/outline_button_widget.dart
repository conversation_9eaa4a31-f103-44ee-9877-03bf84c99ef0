import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';

class OutlineButtonWidget extends StatelessWidget {
  final Color buttonColor;
  final BoxShadow? shadow;
  final double? height;
  final double? width;
  final String label;
  final TextStyle? labelStyle;
  final VoidCallback? onPressed;
  final String imagePath;
  final TextAlign textAlign;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final double? imageHeight;
  final double? imageWidth;
  final double imagePadding;
  final double? textScaleFactor;

  const OutlineButtonWidget({
    super.key,
    required this.label,
    this.buttonColor = AppColor.white,
    this.shadow,
    this.height,
    this.width,
    this.labelStyle,
    this.onPressed,
    this.imagePath = '',
    this.textAlign = TextAlign.center,
    this.borderColor = AppColor.green200,
    this.borderWidth = 1.5,
    this.borderRadius = 15,
    this.imageHeight,
    this.imageWidth,
    this.imagePadding = 0,
    this.textScaleFactor,
  });

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: buttonColor,
        borderRadius: BorderRadius.circular(borderRadius),
        border: Border.all(
          color: borderColor,
          width: borderWidth,
        ),
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints.tightFor(
          width: width ?? 100.w,
          height: height ?? 14.w,
        ),
        child: ElevatedButtonTheme(
          data: ElevatedButtonThemeData(
            style: ButtonStyle(
              overlayColor: MaterialStateProperty.all<Color?>(
                borderColor.withOpacity(.3),
              ),
              shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(borderRadius),
                ),
              ),
            ),
          ),
          child: ElevatedButton(
            onPressed: onPressed ?? () {},
            style: ElevatedButton.styleFrom(
              alignment: Alignment.center,
              backgroundColor: Colors.transparent,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              disabledForegroundColor: Colors.transparent.withOpacity(0.38),
              disabledBackgroundColor: Colors.transparent.withOpacity(0.12),
              shadowColor: Colors.transparent,
            ),
            child: Row(
              children: [
                if (imagePath.isNotEmpty)
                  Container(
                    width: imageWidth ?? 7.69.w,
                    height: imageHeight ?? 7.69.w,
                    margin: const EdgeInsets.only(right: 15),
                    padding: EdgeInsets.all(imagePadding),
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                    ),
                    child: Image.asset(imagePath),
                  ),
                Expanded(
                  child: Text(label,
                      textScaler: TextScaler.linear(textScaleFactor ?? 1),
                      textAlign:
                          imagePath.isEmpty ? textAlign : TextAlign.start,
                      style: labelStyle ??
                          AppText.theme(context).titleMedium!.copyWith(
                              color: AppColor.black,
                              fontWeight: FontWeight.w400)),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
