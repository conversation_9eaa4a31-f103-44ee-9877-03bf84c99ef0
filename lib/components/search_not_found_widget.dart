import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/constants/app_asset.dart';
import 'package:samitivej_flutter_app/constants/app_layout.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';

class SearchNotFoundWidget extends StatelessWidget {
  const SearchNotFoundWidget({
    super.key,
    this.message,
    this.image,
  });

  final String? message;
  final String? image;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AppLayout.mediumH,
        Image.asset(
          image ?? AppAsset.searchNotFound,
          fit: BoxFit.fitHeight,
          height: 8.h,
        ),
        AppLayout.mediumH,
        Text(
          message ?? AppLocalizations.of(context)!.searchNotFound,
          textAlign: TextAlign.center,
          style: AppText.theme(context).bodyMedium,
        ),
      ],
    );
  }
}
