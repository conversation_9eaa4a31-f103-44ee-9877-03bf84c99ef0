import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';

class TextFillBackground extends StatelessWidget {
  const TextFillBackground({
    super.key,
    required this.width,
    required this.height,
    this.backgroundColor = AppColor.lightYellow,
    this.title = '',
    this.subtitle = '',
    this.titleColor = AppColor.black,
    this.subtitleColor = AppColor.green900,
  });
  final double width;
  final double height;
  final String title;
  final String subtitle;
  final Color backgroundColor;
  final Color titleColor;
  final Color subtitleColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(width/12),
        color: backgroundColor,),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title,
            style:
            AppText
                .theme(context)
                .displaySmall!
                .copyWith(color: titleColor),
          ),
          SizedBox(height: (0.5).h),
          Text(
            subtitle,
            style: AppText
                .theme(context)
                .displaySmall!
                .copyWith(color: subtitleColor),
          ),
        ],
      ),
    );
  }
}
