import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/components/custom_green_button.dart';
import 'package:samitivej_flutter_app/components/workout_summary_result.dart';
import 'package:samitivej_flutter_app/constants/app_asset.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';
import 'package:samitivej_flutter_app/localization/app_translate_key.dart';
import 'package:samitivej_flutter_app/screen/workout/views/widgets/shared/header_text_workout.dart';

class WorkoutSummary extends StatelessWidget {
  const WorkoutSummary({
    super.key,
    required this.coin,
    required this.title,
    required this.hour,
    required this.minute,
    required this.second,
    required this.paceSummary,
    required this.distanceTotal,
    required this.burn,
    required this.heartRate,
    this.onPressedFinish,
    this.onPressedShared,
  });

  final int coin;
  final String title;
  final String hour;
  final String minute;
  final String second;
  final double paceSummary;
  final double distanceTotal;
  final double burn;
  final double heartRate;
  final VoidCallback? onPressedFinish;
  final VoidCallback? onPressedShared;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          SizedBox(height: 2.h),
          Text(
            '${AppTranslateKey.congratulation}!!',
            style: AppText.theme(context).bodyMedium,
          ),
          Image.asset(
            AppAsset.iconCircleStar,
            width: 40.w,
          ),
          // Text(
          //   '+$coin ${AppTranslateKey.coins}',
          //   style: AppText.theme(context).bodyLarge!.copyWith(
          //         color: AppColor.greenButtonSetting,
          //       ),
          // ),
          HeaderTextWorkout(title: title),
          WorkoutSummaryResult(
            hour: hour,
            minute: minute,
            second: second,
            paceSummary: paceSummary,
            distanceTotal: distanceTotal,
            burn: burn,
            heartRate: heartRate,
          ),
          SizedBox(height: 2.h),
          CustomGreenButton(
            onPressed: onPressedFinish,
            child: Text(
              AppTranslateKey.finish,
              style: AppText.theme(context).bodyMedium,
            ),
          ),
          SizedBox(height: 1.h),
          // TextIconWorkoutButton(
          //   title: AppTranslateKey.share,
          //   svg: AppAsset.iconShareSvg,
          //   onPressed: onPressedShared,
          // ),
        ],
      ),
    );
  }
}
