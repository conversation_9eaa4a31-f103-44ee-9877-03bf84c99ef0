import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';

class CustomTextField extends StatelessWidget {
  final String? initialValue;
  final String? label;
  final TextStyle? labelStyle;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final bool obscureText;
  final bool autocorrect;
  final bool enableSuggestions;
  final int? maxLength;
  final MaxLengthEnforcement? maxLengthEnforcement;
  final int? maxLines;
  final int? minLines;
  final void Function(String)? onChanged;
  final String? errorText;
  final String? hintText;
  final TextStyle? hintStyle;
  final Widget? prefixIcon;
  final double? prefixIconHeight;
  final double? prefixIconWidth;
  final Widget? suffixWidget;
  final Widget? suffixIcon;
  final double? suffixIconHeight;
  final double? suffixIconWidth;
  final double? borderRadius;
  final double? borderWidth;
  final EdgeInsetsGeometry? contentPadding;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final List<TextInputFormatter>? inputFormatters;
  final void Function()? onTap;
  final bool? enabled;
  final bool readOnly;
  final TextStyle? textStyle;
  final BoxShadow? boxShadow;
  final bool disableFake;
  final InputBorder? border;
  final InputBorder? enabledBorder;
  final InputBorder? errorBorder;
  final InputBorder? focusedBorder;
  final InputBorder? disabledBorder;
  final FocusNode? focusNode;
  final void Function(String)? onFieldSubmitted;
  final void Function()? onEditingComplete;
  final TextAlign textAlign;

  const CustomTextField({
    super.key,
    this.initialValue,
    this.label,
    this.labelStyle,
    this.keyboardType,
    this.textInputAction,
    this.obscureText = false,
    this.autocorrect = false,
    this.enableSuggestions = false,
    this.maxLength,
    this.maxLengthEnforcement,
    this.maxLines = 1,
    this.minLines,
    this.onChanged,
    this.errorText,
    this.hintText,
    this.prefixIcon,
    this.prefixIconHeight,
    this.prefixIconWidth,
    this.suffixWidget,
    this.suffixIcon,
    this.suffixIconHeight,
    this.suffixIconWidth,
    this.borderRadius = 10,
    this.borderWidth = 2,
    this.hintStyle,
    this.contentPadding,
    this.controller,
    this.validator,
    this.inputFormatters,
    this.onTap,
    this.enabled,
    this.readOnly = false,
    this.textStyle,
    this.boxShadow,
    this.disableFake = false,
    this.border,
    this.enabledBorder,
    this.errorBorder,
    this.focusedBorder,
    this.disabledBorder,
    this.focusNode,
    this.onFieldSubmitted,
    this.onEditingComplete,
    this.textAlign = TextAlign.start,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius!),
        boxShadow: boxShadow != null ? [boxShadow!] : null,
      ),
      child: TextFormField(
        textAlign: textAlign,
        focusNode: focusNode,
        onFieldSubmitted: onFieldSubmitted,
        onEditingComplete: onEditingComplete,
        controller: controller,
        initialValue: initialValue,
        keyboardType: keyboardType,
        textInputAction: textInputAction,
        cursorColor: AppColor.green600,
        obscureText: obscureText,
        autocorrect: autocorrect,
        enableSuggestions: enableSuggestions,
        maxLength: maxLength,
        maxLengthEnforcement: maxLengthEnforcement,
        maxLines: maxLines,
        minLines: minLines,
        validator: validator,
        enabled: enabled,
        style: textStyle ?? AppText.theme(context).bodyMedium,
        readOnly: readOnly,
        decoration: InputDecoration(
          labelText: label,
          labelStyle: labelStyle ??
              const TextStyle(
                color: AppColor.oceanGreen,
              ),
          contentPadding: contentPadding ??
              EdgeInsets.symmetric(
                horizontal: 14.sp,
                vertical: 12.sp,
              ),
          hintText: hintText,
          hintStyle: hintStyle ??
              AppText.theme(context).bodyMedium!.copyWith(
                    color: AppColor.grey,
                  ),
          errorStyle: const TextStyle(height: 0),
          errorText: errorText,
          filled: true,
          fillColor: AppColor.white,
          border: border ??
              OutlineInputBorder(
                borderSide: borderWidth != 0
                    ? BorderSide(
                        color: AppColor.grey500,
                        width: borderWidth!,
                      )
                    : BorderSide.none,
                borderRadius: BorderRadius.circular(borderRadius!),
              ),
          enabledBorder: enabledBorder ??
              OutlineInputBorder(
                borderSide: borderWidth != 0
                    ? BorderSide(
                        color: AppColor.grey500,
                        width: borderWidth!,
                      )
                    : BorderSide.none,
                borderRadius: BorderRadius.circular(borderRadius!),
              ),
          errorBorder: errorBorder ??
              OutlineInputBorder(
                borderSide: borderWidth != 0
                    ? BorderSide(
                        color: AppColor.red,
                        width: borderWidth!,
                      )
                    : BorderSide.none,
                borderRadius: BorderRadius.circular(borderRadius!),
              ),
          focusedErrorBorder: errorBorder ??
              OutlineInputBorder(
                borderSide: borderWidth != 0
                    ? BorderSide(
                        color: AppColor.red,
                        width: borderWidth!,
                      )
                    : BorderSide.none,
                borderRadius: BorderRadius.circular(borderRadius!),
              ),
          focusedBorder: focusedBorder ??
              OutlineInputBorder(
                borderSide: borderWidth != 0
                    ? BorderSide(
                        color: AppColor.green700,
                        width: borderWidth!,
                      )
                    : BorderSide.none,
                borderRadius: BorderRadius.circular(borderRadius!),
              ),
          disabledBorder: disabledBorder ??
              (disableFake
                  ? OutlineInputBorder(
                      borderSide: borderWidth != 0
                          ? BorderSide(
                              color: AppColor.grey500,
                              width: borderWidth!,
                            )
                          : BorderSide.none,
                      borderRadius: BorderRadius.circular(borderRadius!),
                    )
                  : null),
          counterStyle: const TextStyle(
            height: double.minPositive,
          ),
          counterText: '',
          prefixIcon: prefixIcon,
          prefixIconConstraints:
              prefixIconHeight != null || prefixIconWidth != null
                  ? BoxConstraints(
                      maxHeight: prefixIconHeight!,
                      maxWidth: prefixIconWidth!,
                    )
                  : null,
          suffix: suffixWidget,
          suffixIcon: suffixIcon,
          suffixIconConstraints:
              suffixIconHeight != null || suffixIconWidth != null
                  ? BoxConstraints(
                      maxHeight: suffixIconHeight!,
                      maxWidth: suffixIconWidth!,
                    )
                  : null,
        ),
        onChanged: onChanged,
        onTap: onTap,
        inputFormatters: inputFormatters,
      ),
    );
  }
}
