import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:shimmer/shimmer.dart';

class CustomShimmer extends StatelessWidget {
  final Widget child;
  final Color? baseColor;
  final Color? highlightColor;

  const CustomShimmer({
    super.key,
    required this.child,
    this.baseColor,
    this.highlightColor,
  });

  static CustomShimmer avatar({
    double? size,
    Color? baseColor,
    Color? highlightColor,
  }) {
    return CustomShimmer(
      baseColor: baseColor,
      highlightColor: highlightColor,
      child: Container(
        width: size ?? 20,
        height: size ?? 20,
        decoration: const BoxDecoration(
          color: AppColor.white,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  static CustomShimmer textLine({
    double? height,
    required double width,
    double borderRadius = 10,
    Color? baseColor,
    Color? highlightColor,
  }) {
    return CustomShimmer(
      baseColor: baseColor,
      highlightColor: highlightColor,
      child: Container(
        height: height ?? 2.5.w,
        width: width,
        decoration: BoxDecoration(
          color: AppColor.white,
          borderRadius: BorderRadius.circular(borderRadius),
        ),
      ),
    );
  }

  static CustomShimmer container({
    double? height,
    double? width,
    double borderRadiusSize = 10,
    Color? baseColor,
    Color? highlightColor,
  }) {
    return CustomShimmer(
      baseColor: baseColor,
      highlightColor: highlightColor,
      child: Container(
        height: height ?? 10.w,
        width: width ?? 10.w,
        decoration: BoxDecoration(
          color: AppColor.white,
          borderRadius: BorderRadius.circular(borderRadiusSize),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: baseColor ?? AppColor.grey100,
      highlightColor: highlightColor ?? AppColor.white,
      child: child,
    );
  }
}
