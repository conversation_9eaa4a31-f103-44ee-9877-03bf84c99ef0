import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/instance_manager.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:samitivej_flutter_app/apps/common/common_app.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';
import 'package:samitivej_flutter_app/utils/exception_handle.dart';

class ActionSheet {
  ActionSheet({required this.label, required this.actionIndex});
  final String label;
  final int actionIndex;
}

class PlatformBottomSheet extends StatelessWidget {
  const PlatformBottomSheet({
    super.key,
    required this.actionButton,
    required this.isCancel,
  });

  final List<ActionSheet> actionButton;
  final bool isCancel;

  Future<int?> show(BuildContext context) async {
    return Platform.isIOS
        ? await showCupertinoModalPopup<int?>(
            context: context,
            useRootNavigator: true,
            builder: (BuildContext context) => this,
          )
        : await showModalBottomSheet<int?>(
            context: context,
            useRootNavigator: true,
            builder: (BuildContext context) => this,
          );
  }

  @override
  Widget build(BuildContext context) {
    return Platform.isAndroid
        ? Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: SafeArea(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  ...actionButton.map(
                    (e) => ListTile(
                      onTap: () => Navigator.of(
                        context,
                        rootNavigator: true,
                      ).pop(e.actionIndex),
                      title: Text(
                        e.label,
                        style: AppText.theme(context).titleMedium!.copyWith(
                              color: AppColor.acceptColorFont,
                            ),
                      ),
                    ),
                  ),
                  isCancel
                      ? ListTile(
                          onTap: () => Navigator.of(
                            context,
                            rootNavigator: true,
                          ).pop(),
                          title: Text(
                            AppLocalizations.of(context)!.cancel,
                            style: AppText.theme(context).titleMedium!.copyWith(
                                  color: AppColor.red,
                                ),
                          ),
                        )
                      : const SizedBox.shrink(),
                ],
              ),
            ),
          )
        : CupertinoActionSheet(
            actions: [
              ...actionButton.map(
                (e) => CupertinoActionSheetAction(
                  child: Text(
                    e.label,
                    style: AppText.theme(context).titleMedium!.copyWith(
                          color: AppColor.acceptColorFont,
                        ),
                  ),
                  onPressed: () => Navigator.of(
                    context,
                    rootNavigator: true,
                  ).pop(e.actionIndex),
                ),
              ),
            ],
            cancelButton: isCancel
                ? CupertinoActionSheetAction(
                    isDestructiveAction: true,
                    onPressed: () => Navigator.of(
                      context,
                      rootNavigator: true,
                    ).pop(),
                    child: Text(
                      AppLocalizations.of(context)!.cancel,
                      style: AppText.theme(context).titleMedium!.copyWith(
                            color: AppColor.red,
                          ),
                    ),
                  )
                : null,
          );
  }

  static Future<File?> imagePicker(
    BuildContext context, {
    bool isOnlyCamera = false,
    bool isOnlyGallery = false,
    bool skipSelection = false,
  }) async {
    final picker = ImagePicker();
    try {
      int? actionResult = isOnlyCamera
          ? 1
          : isOnlyGallery
              ? 2
              : 1;

      if (!skipSelection) {
        actionResult = await PlatformBottomSheet(
          actionButton: [
            if (!isOnlyGallery)
              ActionSheet(
                  label: AppLocalizations.of(context)!.takePhoto,
                  actionIndex: 1),
            if (!isOnlyCamera)
              ActionSheet(
                  label: AppLocalizations.of(context)!.uploadPhoto,
                  actionIndex: 2)
          ],
          isCancel: true,
        ).show(context);
      }

      if (actionResult != null) {
        Get.find<CommonApp>().appLoading.value = true;
        final pickedImage = await picker.pickImage(
          source: actionResult == 1 ? ImageSource.camera : ImageSource.gallery,
          imageQuality: 30,
        );

        if (pickedImage == null) return null;
        final file = File(pickedImage.path);
        return file;
      } else {
        return null;
      }
    } on Exception catch (e) {
      if (e is! PlatformException) rethrow;
      if (e.code == 'photo_access_denied' || e.code == 'camera_access_denied') {
        throw CommonException(
          AppLocalizations.of(context)!.accessDeniedExceptionMessage,
        );
      } else {
        throw CommonException(e.message ?? 'Can\'t get image');
      }
    } finally {
      Get.find<CommonApp>().appLoading.value = false;
    }
  }
}
