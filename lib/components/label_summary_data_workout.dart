import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';

class LabelSummaryDataWorkout extends StatelessWidget {
  const LabelSummaryDataWorkout({
    super.key,
    required this.icon,
    required this.title,
    required this.value,
    required this.unit,
    this.backgroundColor = AppColor.bgLabelSummaryWorkout,
    this.iconColor,
  });
  final String icon;
  final Color? iconColor;
  final String title;
  final String value;
  final String unit;
  final Color backgroundColor;

  @override
  Widget build(BuildContext context) {
    double iconSize = 6.w;
    return Container(
      height: 5.h,
      margin: EdgeInsets.only(bottom: 1.h),
      child: Row(
        children: [
          SizedBox(width: 3.w),
          SizedBox(
            width: iconSize + 1.w,
            child: (icon.contains('.svg'))
                ? SvgPicture.asset(
                    icon,
                    width: iconSize,
                    height: iconSize,
                    colorFilter: (iconColor != null)
                        ? ColorFilter.mode(iconColor!, BlendMode.srcIn)
                        : null,
                  )
                : Image.asset(
                    icon,
                    width: iconSize,
                    height: iconSize,
                    color: iconColor,
                  ),
          ),
          SizedBox(width: 4.w),
          SizedBox(
            width: 23.w,
            child: Padding(
              padding: EdgeInsets.only(right: 2.w),
              child: Text(
                title,
                style: AppText.theme(context).bodyMedium,
              ),
            ),
          ),
          Container(
            width: 45.w,
            height: 5.h,
            padding: EdgeInsets.symmetric(horizontal: 3.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: backgroundColor),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    value,
                    style: AppText.theme(context).bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(
                  width: 15.w,
                  child: Text(
                    unit,
                    style: AppText.theme(context)
                        .bodyMedium
                        ?.copyWith(color: AppColor.unitLabel),
                    textAlign: TextAlign.end,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
