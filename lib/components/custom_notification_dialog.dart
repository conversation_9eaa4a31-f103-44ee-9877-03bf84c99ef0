import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/components/custom_button.dart';
import 'package:samitivej_flutter_app/components/custom_dialog.dart';
// import 'package:samitivej_flutter_app/components/filled_button_widget.dart';
// import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/constants/app_layout.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';
import 'package:samitivej_flutter_app/localization/app_translate_key.dart';

class CustomNotificationDialog extends StatelessWidget {
  final String title;
  final String description;

  const CustomNotificationDialog({
    super.key,
    required this.title,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    return CustomDialog(
      insetPadding: const EdgeInsets.symmetric(
        horizontal: 50,
        vertical: 24,
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(
          vertical: 6.7.w,
          horizontal: 10.w,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: AppText.theme(context).titleMedium!.copyWith(
                    color: const Color.fromARGB(255, 27, 70, 151),
                  ),
            ),
            AppLayout.mediumH,
            Text(
              description,
              textAlign: TextAlign.center,
              style: AppText.theme(context).bodyMedium,
            ),
            AppLayout.mediumH,
            CustomButton(
              width: 25.h,
              label: AppTranslateKey.ok,
              onPressed: () => Get.back<void>(),
              borderRadiusSize: 50.sp,
            ),
          ],
        ),
      ),
    );
  }
}
