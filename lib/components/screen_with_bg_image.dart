import 'package:flutter/material.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/utils/utils.dart';

class ScreenWithBGImage extends StatelessWidget {
  final double opacity;
  final List<Widget> children;
  final List<Widget>? bgStackChildren;
  final PreferredSizeWidget? appBar;
  final Widget? background;
  final Color? backgroundColor;
  final EdgeInsets padding;
  final VoidCallback? onPressedBackground;
  final Widget? loadingWidget;
  final bool loadingWithAppBar;
  final bool resizeToAvoidBottomInset;
  final bool isHasScroll;
  final bool safeAreaTop;
  final bool safeAreaBottom;
  final ScrollController? scrollController;

  const ScreenWithBGImage({
    Key? key,
    required this.children,
    this.opacity = .65,
    this.appBar,
    this.backgroundColor,
    this.padding = const EdgeInsets.symmetric(horizontal: 15),
    this.onPressedBackground,
    this.loadingWidget,
    this.loadingWithAppBar = false,
    this.resizeToAvoidBottomInset = false,
    this.isHasScroll = false,
    this.bgStackChildren,
    this.safeAreaTop = true,
    this.safeAreaBottom = true,
    this.background,
    this.scrollController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bgStackChildren = this.bgStackChildren;
    return Stack(
      children: [
        Scaffold(
          resizeToAvoidBottomInset: resizeToAvoidBottomInset,
          backgroundColor: backgroundColor ?? AppColor.white,
          appBar: appBar,
          body: Stack(
            children: [
              (background != null) ? background! : const SizedBox.shrink(),
              SafeArea(
                top: safeAreaTop,
                bottom: safeAreaBottom,
                child: GestureDetector(
                  onTap: onPressedBackground ?? () => AppUtils.unfocus(context),
                  child: Stack(
                    children: [
                      if (bgStackChildren != null) ...bgStackChildren,
                      Padding(
                        padding: padding,
                        child: isHasScroll
                            ? scrollController != null
                                ? SingleChildScrollView(
                                    controller: scrollController,
                                    child: Column(
                                      children: children,
                                    ),
                                  )
                                : SingleChildScrollView(
                                    child: Column(
                                      children: children,
                                    ),
                                  )
                            : Column(
                                children: children,
                              ),
                      )
                    ],
                  ),
                ),
              ),
              if (!loadingWithAppBar) loadingWidget ?? const SizedBox(),
            ],
          ),
        ),
        if (loadingWithAppBar) loadingWidget ?? const SizedBox.shrink()
      ],
    );
  }
}
