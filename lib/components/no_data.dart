import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';

class NoData extends StatelessWidget {
  const NoData({super.key, this.text});

  /// Text to display.
  final String? text;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 15),
        child: Text(
          text ?? AppLocalizations.of(context)!.noData,
          textAlign: TextAlign.center,
          style: AppText.theme(context).titleSmall,
          textScaleFactor: 1,
        ),
      ),
    );
  }
}
