import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/apps/common/common_app.dart';
import 'package:samitivej_flutter_app/components/app_text_style.dart';
import 'package:samitivej_flutter_app/components/custom_container.dart';
import 'package:samitivej_flutter_app/constants/app_asset.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';
import 'package:samitivej_flutter_app/localization/app_localization.dart';

class LanguageSelectionWidget extends StatefulWidget {
  const LanguageSelectionWidget({super.key});

  @override
  State<LanguageSelectionWidget> createState() =>
      _LanguageSelectionWidgetState();
}

class _LanguageSelectionWidgetState extends State<LanguageSelectionWidget> {
  @override
  Widget build(BuildContext context) {
    CommonApp commonApp = Get.find<CommonApp>();

    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Obx(
          () {
            return Image.asset(
              commonApp.selectedLanguage.value == 'th'
                  ? AppAsset.thaiLanguageIcon
                  : AppAsset.englishLanguageIcon,
              width: min(6.2.w, 30),
              height: min(6.2.w, 30),
            );
          },
        ),
        const SizedBox(
          width: 7,
        ),
        CustomContainer(
          height: min(8.w, 30),
          width: min(22.w, 100),
          borderRadius: 50,
          backgroundColor: AppColor.green500,
          isHaveShadows: false,
          child: Obx(
            () {
              return DropdownButtonFormField<String>(
                isDense: true,
                isExpanded: true,
                icon: const Icon(
                  Icons.keyboard_arrow_down_rounded,
                  color: AppColor.black,
                  size: 18,
                ),
                hint: Text(
                  'กรุณาเลือกภาษา',
                  style: AppTextStyle.prompt(
                    fontSize: 13,
                    fontWeight: FontWeight.w400,
                    color: AppColor.black,
                  ),
                ),
                value: commonApp.selectedLanguage.value,
                style: AppText.theme(context).bodyMedium,
                onChanged: (newValue) {
                  if (newValue != null) {
                    commonApp.selectedLanguage.value = newValue;
                    commonApp.setLangauge(newValue);
                  }
                },
                decoration: const InputDecoration(
                  isDense: true,
                  border: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  errorBorder: InputBorder.none,
                  disabledBorder: InputBorder.none,
                  contentPadding: EdgeInsets.only(
                    top: 3,
                    left: 8,
                    bottom: 0,
                    right: 8,
                  ),
                  filled: false,
                ),
                selectedItemBuilder: (context) {
                  return AppLocalization.langs
                      .map((lang) => Text(
                            lang.displayName,
                            textAlign: TextAlign.center,
                            style: AppTextStyle.prompt(
                              color: AppColor.black,
                              fontSize: 15,
                              fontWeight: FontWeight.w500,
                            ),
                          ))
                      .toList();
                },
                items: AppLocalization.langs
                    .map(
                      (lang) => DropdownMenuItem(
                        value: lang.languageCode,
                        child: Row(
                          children: [
                            Image.asset(
                              lang.languageCode == 'th'
                                  ? AppAsset.thaiLanguageIcon
                                  : AppAsset.englishLanguageIcon,
                              width: min(6.2.w, 30),
                              height: min(6.2.w, 30),
                            ),
                            SizedBox(
                              width: min(0.8.w, 5),
                            ),
                            Text(
                              lang.displayName,
                              textAlign: TextAlign.center,
                              style: AppTextStyle.prompt(
                                color: AppColor.black,
                                fontSize: 15,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                    .toList(),
              );
            },
          ),
        ),
        const SizedBox(width: 7)
      ],
    );
  }
}
