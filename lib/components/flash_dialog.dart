import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/components/app_text_style.dart';
import 'package:samitivej_flutter_app/components/custom_container.dart';
import 'package:samitivej_flutter_app/constants/app_asset.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/constants/app_layout.dart';

class FlashDialog extends StatefulWidget {
  const FlashDialog({
    super.key,
    required this.message,
    this.controller,
  });

  /// The message.
  final String message;

  /// The controller.
  final FlashDialogController? controller;

  @override
  State<FlashDialog> createState() => _FlashDialogState();
}

class _FlashDialogState extends State<FlashDialog> {
  /// Whether to show the dialog or not.
  bool _showDialog = false;

  @override
  void initState() {
    super.initState();
    widget.controller?.show = _show;
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: AnimatedOpacity(
        opacity: _showDialog ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 300),
        child: IgnorePointer(
          child: UnconstrainedBox(
            child: CustomContainer(
              backgroundColor: const Color(0xB3252525),
              borderRadius: 15,
              isHaveShadows: false,
              padding: EdgeInsets.all(
                AppLayout.defaultPageHorizontialPadding(context) * 2,
              ),
              child: Column(
                children: [
                  Container(
                    decoration: const BoxDecoration(
                      color: AppColor.white,
                      shape: BoxShape.circle,
                    ),
                    padding: const EdgeInsets.all(15),
                    child: SvgPicture.asset(AppAsset.warning),
                  ),
                  AppLayout.mediumH,
                  Text(
                    widget.message,
                    style: AppTextStyle.prompt(
                      fontSize: 16.sp,
                      color: AppColor.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// SHows the dialog.
  void _show() {
    setState(() => _showDialog = true);
    Future.delayed(const Duration(seconds: 2), () {
      setState(() => _showDialog = false);
    });
  }
}

class FlashDialogController {
  Function? show;

  void dispose() {
    show = null;
  }
}
