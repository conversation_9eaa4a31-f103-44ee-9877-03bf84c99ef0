import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';

class TextFieldWidget extends StatelessWidget {
  final String? initialValue;
  final String? label;
  final TextStyle? labelStyle;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final bool obscureText;
  final bool autoCorrect;
  final bool autoFocus;
  final FocusNode? focusNode;
  final bool enableSuggestions;
  final MaxLengthEnforcement? maxLengthEnforcement;
  final int? maxLines;
  final int? minLines;
  final void Function(String)? onChanged;
  final String? errorText;
  final TextStyle? errorStyle;
  final String? hintText;
  final TextStyle? hintStyle;
  final Widget? prefixIcon;
  final double? prefixIconHeight;
  final double? prefixIconWidth;
  final Widget? suffixWidget;
  final Widget? suffixIcon;
  final double? suffixIconHeight;
  final double? suffixIconWidth;
  final double borderRadius;
  final double borderWidth;
  final EdgeInsetsGeometry? contentPadding;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final List<TextInputFormatter>? inputFormatters;
  final void Function()? onTap;
  final bool? enabled;
  final TextStyle? textStyle;
  final BoxShadow? boxShadow;
  final bool disableFake;
  final InputBorder? border;
  final InputBorder? enabledBorder;
  final InputBorder? errorBorder;
  final InputBorder? focusedBorder;
  final InputBorder? disabledBorder;
  final double? height;
  final double? width;
  final bool expanded;
  final Color? fillColor;
  final Widget? decorationPrefix;

  const TextFieldWidget({
    super.key,
    this.initialValue,
    this.label,
    this.labelStyle,
    this.keyboardType,
    this.textInputAction,
    this.obscureText = false,
    this.autoCorrect = false,
    this.autoFocus = false,
    this.focusNode,
    this.enableSuggestions = false,
    this.maxLengthEnforcement,
    this.maxLines = 1,
    this.minLines,
    this.onChanged,
    this.errorText,
    this.errorStyle,
    this.hintText,
    this.prefixIcon,
    this.prefixIconHeight,
    this.prefixIconWidth,
    this.suffixWidget,
    this.suffixIcon,
    this.suffixIconHeight,
    this.suffixIconWidth,
    this.borderRadius = 15,
    this.borderWidth = 1.5,
    this.hintStyle,
    this.contentPadding,
    this.controller,
    this.validator,
    this.inputFormatters,
    this.onTap,
    this.enabled,
    this.textStyle,
    this.boxShadow,
    this.disableFake = false,
    this.border,
    this.enabledBorder,
    this.errorBorder,
    this.focusedBorder,
    this.disabledBorder,
    this.height,
    this.width,
    this.expanded = false,
    this.fillColor,
    this.decorationPrefix,
  });

  @override
  Widget build(BuildContext context) {
    final double scaleFactor = MediaQuery.of(context).textScaleFactor;
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        boxShadow: boxShadow != null ? [boxShadow!] : null,
      ),
      child: TextFormField(
        controller: controller,
        initialValue: initialValue,
        keyboardType: keyboardType,
        textInputAction: textInputAction,
        cursorColor: AppColor.green600,
        obscureText: obscureText,
        autocorrect: autoCorrect,
        autofocus: autoFocus,
        focusNode: focusNode,
        enableSuggestions: enableSuggestions,
        maxLengthEnforcement: maxLengthEnforcement,
        maxLines: maxLines,
        minLines: minLines,
        validator: validator,
        enabled: enabled,
        style: TextStyle(fontSize: 16 / scaleFactor),
        expands: expanded,
        decoration: InputDecoration(
          prefix: decorationPrefix,
          labelText: label,
          labelStyle: labelStyle ??
              AppText.theme(context).titleMedium!.copyWith(
                  color: AppColor.grey280, fontWeight: FontWeight.w400),
          contentPadding: contentPadding ??
              const EdgeInsets.symmetric(
                horizontal: 25,
                vertical: 20,
              ),
          hintText: hintText,
          hintStyle: hintStyle ??
              AppText.theme(context).titleMedium!.copyWith(
                    color: AppColor.grey280,
                    fontWeight: FontWeight.w400,
                  ),
          errorText: errorText,
          errorStyle: errorStyle,
          errorMaxLines: 3,
          filled: true,
          fillColor: fillColor ?? AppColor.white,
          border: border ??
              OutlineInputBorder(
                borderSide: BorderSide(
                  color: AppColor.green200,
                  width: borderWidth,
                ),
                borderRadius: BorderRadius.circular(borderRadius),
              ),
          enabledBorder: enabledBorder ??
              OutlineInputBorder(
                borderSide: BorderSide(
                  color: AppColor.green200,
                  width: borderWidth,
                ),
                borderRadius: BorderRadius.circular(borderRadius),
              ),
          focusedBorder: focusedBorder ??
              OutlineInputBorder(
                borderSide: BorderSide(
                  color: AppColor.green700,
                  width: borderWidth,
                ),
                borderRadius: BorderRadius.circular(borderRadius),
              ),
          errorBorder: errorBorder ??
              OutlineInputBorder(
                borderSide: BorderSide(
                  color: AppColor.red300,
                  width: borderWidth,
                ),
                borderRadius: BorderRadius.circular(borderRadius),
              ),
          focusedErrorBorder: errorBorder ??
              OutlineInputBorder(
                borderSide: BorderSide(
                  color: AppColor.red300,
                  width: borderWidth,
                ),
                borderRadius: BorderRadius.circular(borderRadius),
              ),
          disabledBorder: disabledBorder ??
              (disableFake
                  ? OutlineInputBorder(
                      borderSide: BorderSide(
                        color: AppColor.grey500,
                        width: borderWidth,
                      ),
                      borderRadius: BorderRadius.circular(borderRadius),
                    )
                  : null),
          prefixIcon: prefixIcon,
          prefixIconConstraints:
              prefixIconHeight != null || prefixIconWidth != null
                  ? BoxConstraints(
                      maxHeight: prefixIconHeight!,
                      maxWidth: prefixIconWidth!,
                    )
                  : null,
          suffix: suffixWidget,
          suffixIcon: suffixIcon,
          suffixIconConstraints:
              suffixIconHeight != null || suffixIconWidth != null
                  ? BoxConstraints(
                      maxHeight: suffixIconHeight!,
                      maxWidth: suffixIconWidth!,
                    )
                  : null,
        ),
        onChanged: onChanged,
        onTap: onTap,
        inputFormatters: inputFormatters,
      ),
    );
  }
}
