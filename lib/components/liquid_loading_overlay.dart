import 'package:flutter/material.dart';
import 'package:liquid_progress_indicator_v2/liquid_progress_indicator.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:samitivej_flutter_app/constants/app_layout.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';

class LiquidLoadingOverlay extends StatefulWidget {
  const LiquidLoadingOverlay({super.key});

  @override
  State<LiquidLoadingOverlay> createState() => _LiquidLoadingOverlayState();
}

class _LiquidLoadingOverlayState extends State<LiquidLoadingOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  static const int maxSeconds = 30;
  int _currentSeconds = 0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    )..addListener(() {
        if (_animationController.isCompleted && _currentSeconds < maxSeconds) {
          setState(() {
            _currentSeconds++;
          });
          _animationController.reset();
          _animationController.forward();
        }
      });
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white.withOpacity(0.85),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 80.w,
              height: 60,
              child: Stack(
                children: [
                  LiquidLinearProgressIndicator(
                    value: _currentSeconds / maxSeconds,
                    backgroundColor: Colors.white,
                    valueColor: const AlwaysStoppedAnimation(AppColor.green400),
                    borderWidth: 4.0,
                    borderColor: AppColor.green400,
                    borderRadius: 30,
                  ),
                  Center(
                    child: Text(
                      '${((_currentSeconds / maxSeconds) * 100).toInt()}%',
                      style: AppText.theme(context).titleMedium!.copyWith(
                            color: Colors.black,
                          ),
                    ),
                  ),
                ],
              ),
            ),
            AppLayout.mediumH,
            Text(
              AppLocalizations.of(context)!.pleaseWait,
              textAlign: TextAlign.center,
              style: AppText.theme(context).titleMedium!.copyWith(
                    color: Colors.black,
                  ),
            ),
            AppLayout.hugeH,
          ],
        ),
      ),
    );
  }
}
