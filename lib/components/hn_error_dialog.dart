import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/apps/home/<USER>';
import 'package:samitivej_flutter_app/components/custom_button.dart';
import 'package:samitivej_flutter_app/components/custom_dialog.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';
import 'package:samitivej_flutter_app/screen/setting/setting_child_screens/connect_hn_flow/connect_hn_wizard.dart';

class HNErrorDialog extends StatelessWidget {
  const HNErrorDialog({
    super.key,
    this.message = '',
  });

  /// Message.
  final String message;

  @override
  Widget build(BuildContext context) {
    return CustomDialog(
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 20,
          vertical: 20,
        ),
        child: Column(
          mainAxisSize:
              MainAxisSize.min, // Ensure the dialog adjusts to its content
          children: [
            Container(
              padding: const EdgeInsets.only(bottom: 5),
              child: Text(
                message,
                textScaler: const TextScaler.linear(1.0),
                style: AppText.theme(context).titleMedium!.copyWith(
                      fontWeight: FontWeight.normal,
                      color: AppColor.black,
                    ),
              ),
            ),
            SizedBox(height: 15.sp), // Add gap between message & buttons
            Container(
              width: 90.w,
              padding: const EdgeInsets.only(bottom: 5),
              child: Row(
                mainAxisAlignment:
                    MainAxisAlignment.center, // Center the buttons
                children: [
                  Expanded(
                    child: CustomButton(
                      width: double.infinity, // Let Expanded handle the width
                      height: 30.sp,
                      margin: EdgeInsets.only(
                          right: 10.sp), // Add spacing between buttons
                      label: AppLocalizations.of(context)!.connect,
                      onPressed: () async {
                        // await AnalyticsService.logEventHomeInteraction(
                        //     areaName: 'connect hn button');
                        await HomeApp.postponeConnectHnBar(
                            snoozeFor: const Duration(days: 14));
                        Get.back<void>();
                        Get.to<bool>(const ConnectHNWizard());
                      },
                      borderRadiusSize: 50.sp,
                      labelStyle: AppText.theme(context).titleMedium!.copyWith(
                            fontWeight: FontWeight.w400,
                            color: AppColor.black,
                          ),
                    ),
                  ),
                  Expanded(
                    child: CustomButton(
                      width: double.infinity, // Let Expanded handle the width
                      height: 30.sp,
                      margin: EdgeInsets.only(
                          left: 10.sp), // Add spacing between buttons
                      label: AppLocalizations.of(context)!.later,
                      onPressed: () => Get.back<void>(), // Close the dialog
                      borderRadiusSize: 50.sp,
                      backgroundColor: AppColor.grey750,
                      labelStyle: AppText.theme(context).titleMedium!.copyWith(
                            fontWeight: FontWeight.normal,
                            color: AppColor.black,
                          ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
