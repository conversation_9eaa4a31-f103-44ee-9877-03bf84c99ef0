import 'package:flutter/material.dart';
import 'package:samitivej_flutter_app/constants/app_asset.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';

class HeartCircle extends StatelessWidget {
  const HeartCircle({
    super.key,
    required this.size,
    this.isActive = false,
  });
  final double size;
  final bool isActive;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            offset: const Offset(5, 5),
            blurRadius: 20,
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 1,
          )
        ],
      ),
      child: ClipOval(
        child: Container(
          width: size,
          height: size,
          color: AppColor.pink100,
          child: Center(
            child: Image.asset(
              isActive
                  ? AppAsset.hearthActiveIcon
                  : AppAsset.hearthInActiveIcon,
              width: size / 1.4,
              color: isActive ? null:AppColor.red,
              fit: BoxFit.scaleDown,
            ),
          ),
        ),
      ),
    );
  }
}
