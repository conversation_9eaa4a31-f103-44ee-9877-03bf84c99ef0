import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/components/image_preview_dialog.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';

class ImagePreviewBox extends StatelessWidget {
  final File? imageFile;
  final String? imageUrl;

  final double? width;
  final double? height;
  final bool isEditing;
  final VoidCallback? onClick;
  final VoidCallback? onDelete;

  const ImagePreviewBox({
    super.key,
    this.imageFile,
    this.width,
    this.height,
    this.isEditing = false,
    this.onDelete,
    this.onClick,
    this.imageUrl,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Stack(
        children: [
          Padding(
            padding: EdgeInsets.all(10.sp),
            child: GestureDetector(
              onTap: () {
                if (imageFile != null) {
                  Get.dialog<void>(
                    ImagePreviewDialog(
                      imageFile: imageFile,
                    ),
                  );
                } else {
                  Get.dialog<void>(
                    ImagePreviewDialog(
                      imageUrl: imageUrl,
                    ),
                  );
                }
              },
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: imageFile != null
                    ? Image.file(
                        imageFile!,
                        width: width ?? 24.w,
                        height: height ?? 24.w,
                        fit: BoxFit.cover,
                      )
                    : Image.network(
                        imageUrl!,
                        width: width ?? 24.w,
                        height: height ?? 24.w,
                        fit: BoxFit.cover,
                      ),
              ),
            ),
          ),
          if (isEditing)
            Positioned(
              right: 0,
              child: GestureDetector(
                onTap: onDelete,
                child: Container(
                  decoration: const BoxDecoration(
                    color: AppColor.white,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.cancel,
                    color:const Color(0xFFDD2525),
                    size: 3.h,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
