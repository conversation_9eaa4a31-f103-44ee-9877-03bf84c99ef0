import 'dart:math';

import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';

class FilledButtonWidget extends StatelessWidget {
  final Color buttonColor;
  final BoxShadow? shadow;
  final double? height;
  final double? width;
  final double? borderRadius;
  final Color? borderColor;
  final String label;
  final TextStyle? labelStyle;
  final VoidCallback? onPressed;
  final bool isHaveShadow;
  final bool enable;
  final double? textScaleFactor;
  final BoxConstraints? boxConstraints;
  final EdgeInsets? padding;

  const FilledButtonWidget({
    super.key,
    required this.label,
    this.buttonColor = AppColor.green300,
    this.shadow,
    this.height,
    this.width,
    this.labelStyle,
    this.borderRadius,
    this.borderColor,
    this.onPressed,
    this.isHaveShadow = true,
    this.enable = true,
    this.textScaleFactor,
    this.boxConstraints,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: boxConstraints ??
          BoxConstraints.tightFor(
            width: width ?? 100.w,
            height: height ?? min(14.w, 50),
          ),
      child: ElevatedButton(
        onPressed: onPressed ?? () {},
        style: ElevatedButton.styleFrom(
          alignment: Alignment.center,
          backgroundColor: enable ? buttonColor : AppColor.disableButton,
          shape: const StadiumBorder().copyWith(
            side: BorderSide(
              color: borderColor ?? AppColor.transparent,
              width: 3,
            ),
          ),
          padding: padding ?? const EdgeInsets.all(0),
          disabledForegroundColor: Colors.transparent.withOpacity(0.38),
          disabledBackgroundColor: Colors.transparent.withOpacity(0.12),
          shadowColor: Colors.transparent,
        ),
        child: Text(
          label,
          textScaler: TextScaler.linear(textScaleFactor ?? 1),
          textAlign: TextAlign.center,
          style: labelStyle ??
              AppText.theme(context).titleLarge!.copyWith(
                    color: AppColor.black,
                  ),
        ),
      ),
    );
  }
}
