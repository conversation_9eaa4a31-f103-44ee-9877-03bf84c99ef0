import 'package:flutter/material.dart';
import 'package:samitivej_flutter_app/constants/app_asset.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/localization/app_translate_key.dart';
import 'package:samitivej_flutter_app/components/label_summary_data_workout.dart';
import 'package:samitivej_flutter_app/screen/workout/views/widgets/shared/time_card_workout.dart';

class WorkoutSummaryResult extends StatelessWidget {
  const WorkoutSummaryResult({
    super.key,
    required this.hour,
    required this.minute,
    required this.second,
    required this.paceSummary,
    required this.distanceTotal,
    required this.burn,
    required this.heartRate,
    this.onPressed,
  });

  final String hour;
  final String minute;
  final String second;
  final double paceSummary;
  final double distanceTotal;
  final double burn;
  final double heartRate;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    String textBurn = burn.toStringAsFixed(0);
    String textHeartRate = heartRate.toStringAsFixed(0);
    String textPaceSummary = paceSummary.toStringAsFixed(1);
    String textDistanceTotal = distanceTotal.toStringAsFixed(2);

    if (paceSummary.isNaN || paceSummary == 0 || paceSummary > 120) {
      textPaceSummary = '-';
    }

    if (heartRate.isNaN || heartRate == 0) {
      textHeartRate = '-';
    }

    if (burn.isNaN || burn == 0) {
      textBurn = '-';
    }

    if (distanceTotal.isNaN || distanceTotal == 0) {
      textDistanceTotal = '-';
    }
    return TimeCardWorkout(
      hour: hour,
      minute: minute,
      second: second,
      onPressed: onPressed,
      child: Column(
        children: [
          LabelSummaryDataWorkout(
            icon: AppAsset.iconBurn,
            title: AppTranslateKey.burned,
            value: textBurn,
            unit: AppTranslateKey.kcal,
            backgroundColor: AppColor.green180,
          ),
          LabelSummaryDataWorkout(
            icon: AppAsset.iconWalk,
            title: AppTranslateKey.distance,
            value: textDistanceTotal,
            unit: '${AppTranslateKey.km}.',
          ),
          LabelSummaryDataWorkout(
            icon: AppAsset.iconTimeGauge,
            title: AppTranslateKey.pace,
            value: textPaceSummary,
            unit: '${AppTranslateKey.min}/${AppTranslateKey.km}.',
          ),
          LabelSummaryDataWorkout(
            icon: AppAsset.hearthActiveIcon,
            iconColor: AppColor.red350,
            title: AppTranslateKey.heartRate,
            value: textHeartRate,
            unit: 'BPM',
          ),
        ],
      ),
    );
  }
}
