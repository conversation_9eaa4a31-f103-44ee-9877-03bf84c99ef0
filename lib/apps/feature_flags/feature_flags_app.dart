import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:samitivej_flutter_app/apps/home/<USER>';
import 'package:samitivej_flutter_app/apps/user/user_app.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/constants/app_feature_flags.dart';
import 'package:samitivej_flutter_app/models/home_service_item_model.dart';
import 'package:samitivej_flutter_app/services/storage_service.dart';
import 'package:samitivej_flutter_app/services/well_navigate_service.dart';
import 'package:samitivej_flutter_app/utils/utils.dart';
import 'package:statistics/statistics.dart';
import 'package:unleash_proxy_client_flutter/unleash_proxy_client_flutter.dart';

const List<List<Map<String, String>>> defaultServicesPlacement = [
  [
    {"key": "samitivej"},
    {"key": "food"},
    {"key": "fit"},
    {"key": "health"},
    {"key": "gene"},
    {"key": "mom"},
    {"key": "medrefill"},
    {"key": "vaccine"},
  ]
];

class FeatureFlagsApp extends GetxController {
  /// Unleash
  var unleash = UnleashClient(
      url: Uri.parse(ApiConstant.unleashFrontendEndpoint),
      clientKey: dotenv.env['UNLEASH_CLIENT_KEY'] ?? "",
      refreshInterval: 30,
      appName: 'Well',
      environment: dotenv.env['RUN_MODE'] ?? "dev");

  // Register
  Rx<bool> registerHomeCountryCode = RxBool(true);

  // Home
  Rx<bool> homeProfile = Rx(true);
  Rx<bool> homeChat = Rx(true);
  Rx<bool> homeNotification = Rx(true);
  Rx<bool> homeCoins = Rx(true);
  Rx<bool> homeEcoupon = Rx(false);
  Rx<bool> homeServices = Rx(true);
  RxList<List<HomeServiceItemModel>> homeServicesPlacement = RxList();
  Rx<bool> homeBanners = Rx(true);
  Rx<bool> homePendingPayments = Rx(true);
  Rx<bool> homeUpcomings = Rx(true);
  Rx<bool> homeDoctorConsult = Rx(true);
  Rx<bool> homeHealthTrackers = Rx(true);
  Rx<bool> homeMoods = Rx(true);
  Rx<bool> homeFoodDailyTasks = Rx(true);
  Rx<bool> homeFitDailyTasks = Rx(true);
  Rx<bool> homeDiscovers = Rx(true);
  Rx<bool> homeConnectHNBar = Rx(false);
  Rx<bool> homeProductFeed = Rx(true);

  // Mini App
  Rx<bool> miniAppSamitivej = Rx(true);
  Rx<bool> miniAppFood = Rx(true);
  Rx<bool> miniAppFit = Rx(true);
  Rx<bool> miniAppHealthCheckup = Rx(true);
  Rx<bool> miniAppGene = Rx(true);
  Rx<bool> miniAppMom = Rx(true);
  Rx<bool> miniAppMedRefill = Rx(true);
  Rx<bool> miniAppVaccine = Rx(true);
  Rx<bool> miniAppAiDoctor = Rx(true);
  Rx<bool> miniAppWearable = Rx(true);
  Rx<bool> miniAppCorp = Rx(true);

  // iStatus
  Rx<bool> iStatus = Rx(true);

  // Appointment
  Rx<bool> appointment = Rx(true);
  Rx<bool> myAppointment = Rx(true);

  // Virtual Hospital
  Rx<bool> virtualHospital = Rx(true);

  // Emergency Call
  Rx<bool> emergencyCall = Rx(true);

  // Samitivej Home Menu
  Rx<bool> homeSamitivejCheckupReport = Rx(true);
  Rx<bool> homeSamitivejVisitTimeline = Rx(true);
  Rx<bool> homeSamitivejLabResult = Rx(true);
  Rx<bool> homeSamitivejRadioResult = Rx(true);
  Rx<bool> samitivejGuardianSwitcher = Rx(true);
  Rx<bool> homeSamitivejInpatientService = Rx(true);

  // Payment
  Rx<bool> payment = Rx(true);

  // Membership
  Rx<bool> membership = Rx(true);

  // Settings
  Rx<bool> settingGuardianMode = Rx(true);

  @override
  void onInit() {
    super.onInit();
    unleash.on('ready', (data) => updateFlags());
    unleash.start();
  }

  void updateFlags() {
    // Register
    registerHomeCountryCode.value =
        unleash.isEnabled(AppFeatureFlags.registerPhoneCountryCode);

    // Home
    homeProfile.value = unleash.isEnabled(AppFeatureFlags.homeProfile);
    homeChat.value = unleash.isEnabled(AppFeatureFlags.homeChat);
    homeNotification.value =
        unleash.isEnabled(AppFeatureFlags.homeNotification);
    homeCoins.value = unleash.isEnabled(AppFeatureFlags.homeCoins);
    homeEcoupon.value = unleash.isEnabled(AppFeatureFlags.homeEcoupon);
    homeServices.value = unleash.isEnabled(AppFeatureFlags.homeServices);
    homeBanners.value = unleash.isEnabled(AppFeatureFlags.homeBanners);
    homePendingPayments.value =
        unleash.isEnabled(AppFeatureFlags.homePendingPayments);
    homeUpcomings.value = unleash.isEnabled(AppFeatureFlags.homeUpcomings);
    homeDoctorConsult.value =
        unleash.isEnabled(AppFeatureFlags.homeDoctorConsult);
    homeHealthTrackers.value =
        unleash.isEnabled(AppFeatureFlags.homeHealthTrackers);
    homeMoods.value = unleash.isEnabled(AppFeatureFlags.homeMoods);
    homeFoodDailyTasks.value =
        unleash.isEnabled(AppFeatureFlags.homeFoodDailyTasks);
    homeFitDailyTasks.value =
        unleash.isEnabled(AppFeatureFlags.homeFitDailyTasks);
    homeDiscovers.value = unleash.isEnabled(AppFeatureFlags.homeDiscovers);
    homeConnectHNBar.value =
        unleash.isEnabled(AppFeatureFlags.homeConnectHNBar);
    homeProductFeed.value = unleash.isEnabled(AppFeatureFlags.homeProductFeed);

    // Mini App
    miniAppSamitivej.value =
        unleash.isEnabled(AppFeatureFlags.miniAppSamitivej);
    miniAppFood.value = unleash.isEnabled(AppFeatureFlags.miniAppFood);
    miniAppFit.value = unleash.isEnabled(AppFeatureFlags.miniAppFit);
    miniAppHealthCheckup.value =
        unleash.isEnabled(AppFeatureFlags.miniAppHealthCheckup);
    miniAppGene.value = unleash.isEnabled(AppFeatureFlags.miniAppGene);
    miniAppMom.value = unleash.isEnabled(AppFeatureFlags.miniAppMom);
    miniAppMedRefill.value =
        unleash.isEnabled(AppFeatureFlags.miniAppMedRefill);
    miniAppVaccine.value = unleash.isEnabled(AppFeatureFlags.miniAppVaccine);
    miniAppAiDoctor.value = unleash.isEnabled(AppFeatureFlags.miniAppAiDoctor);
    miniAppWearable.value = unleash.isEnabled(AppFeatureFlags.miniAppWearable);
    miniAppCorp.value = unleash.isEnabled(AppFeatureFlags.miniAppCorp);
    homeServicesPlacement.value = getHomeServicesPlacement();

    // iStatus
    iStatus.value = unleash.isEnabled(AppFeatureFlags.iStatus);

    // Appointment
    appointment.value = unleash.isEnabled(AppFeatureFlags.appointment);
    myAppointment.value = unleash.isEnabled(AppFeatureFlags.myAppointment);

    // Virtual Hospital
    virtualHospital.value = unleash.isEnabled(AppFeatureFlags.virtualHospital);

    // Emergency Call
    emergencyCall.value = unleash.isEnabled(AppFeatureFlags.emergencyCall);

    // Samitivej Home Menu
    homeSamitivejCheckupReport.value =
        unleash.isEnabled(AppFeatureFlags.homeSamitivejCheckupReport);
    homeSamitivejVisitTimeline.value =
        unleash.isEnabled(AppFeatureFlags.homeSamitivejVisitTimeline);
    homeSamitivejLabResult.value =
        unleash.isEnabled(AppFeatureFlags.homeSamitivejLabResult);
    homeSamitivejRadioResult.value =
        unleash.isEnabled(AppFeatureFlags.homeSamitivejRadioResult);
    samitivejGuardianSwitcher.value =
        unleash.isEnabled(AppFeatureFlags.samitivejGuardianSwitcher);
    homeSamitivejInpatientService.value =
        unleash.isEnabled(AppFeatureFlags.homeSamitivejInpatientService);

    // Payment
    payment.value = unleash.isEnabled(AppFeatureFlags.payment);

    // Membership
    membership.value = unleash.isEnabled(AppFeatureFlags.membership);

    // Settings
    settingGuardianMode.value =
        unleash.isEnabled(AppFeatureFlags.settingGuardianMode);

    saveDatadogConfig();
  }

  Future<void> saveDatadogConfig() async {
    final storage = StorageService();
    await storage.write('datadog', unleash.isEnabled(AppFeatureFlags.datadog));

    try {
      final variant = unleash.getVariant(AppFeatureFlags.datadogSampleRate);
      if (variant.payload != null) {
        if (variant.payload!.type == "number") {
          final datadogSampleRate = parseDouble(variant.payload!.value);
          await storage.write('datadogSampleRate', datadogSampleRate);
        }
      }
    } catch (_) {}
  }

  Future<void> setUser() async {
    UserApp userApp = Get.find<UserApp>();
    final context = {
      "appName": "Well",
      "currentTime": DateTime.now().toIso8601String(),
      "environment": dotenv.env['RUN_MODE'] ?? "dev",
      "userId": userApp.userInfo?.value.uuid ?? "anonymous",
      "memberId": userApp.customerData.value.customer?.memberId.toString() ??
          "anonymous",
    };
    await unleash.setContextFields(context);
  }

  num? get minimumAppVersion {
    try {
      final variant = unleash.getVariant(AppFeatureFlags.minimumAppVersion);
      if (variant.payload != null) {
        if (variant.payload!.type == "number") {
          return parseNum(variant.payload!.value);
        }
      }
    } catch (_) {}
    return null;
  }

  HomeServiceItemModel? getServiceInfo(String key) {
    return homeServicesPlacement
        .expand((element) => element)
        .firstWhereOrNull((element) => element.key == key);
  }

  List<List<HomeServiceItemModel>> getHomeServicesPlacement() {
    final HomeApp homeApp = Get.find<HomeApp>();
    try {
      final variant = unleash.getVariant(AppFeatureFlags.homeServices);
      if (variant.payload != null) {
        if (variant.payload!.type == "json") {
          final payload = jsonDecode(variant.payload!.value);
          final result = (payload as List).map((page) {
            return (page as List)
                .map((e) {
                  final json = e as Map<String, dynamic>;
                  final key = json["key"];
                  final homeService = homeApp.getServiceData(key);
                  if (homeService == null) {
                    if (HomeServiceItemModel.isValid(json)) {
                      try {
                        return HomeServiceItemModel.fromJson(json);
                      } catch (e) {
                        return null;
                      }
                    }
                  } else {
                    final color = json["color"] != null
                        ? AppUtils.hexToColor(json["color"]!).value
                        : null;
                    return HomeServiceItemModel(
                      key: homeService.key,
                      name: json["name"] ?? homeService.name,
                      icon: json["icon"] ?? homeService.icon,
                      color: color ?? homeService.color,
                      redirectUrl:
                          json["redirectUrl"] ?? homeService.redirectUrl,
                    );
                  }
                })
                .whereNotNull()
                .toList();
          }).toList();
          return result;
        }
      }
      throw Error();
    } catch (e) {
      return defaultServicesPlacement
          .map((page) => page
              .map((json) => homeApp.getServiceData(json["key"]))
              .whereNotNull()
              .toList())
          .toList();
    }
  }

  bool isServiceAvailable(WellServiceKey serviceKey) {
    switch (serviceKey) {
      case WellServiceKey.samitivej:
        return miniAppSamitivej.value;
      case WellServiceKey.food:
        return miniAppFood.value;
      case WellServiceKey.fit:
        return miniAppFit.value;
      case WellServiceKey.health:
        return miniAppHealthCheckup.value;
      case WellServiceKey.gene:
        return miniAppGene.value;
      case WellServiceKey.mom:
        return miniAppMom.value;
      case WellServiceKey.medrefill:
        return miniAppMedRefill.value;
      case WellServiceKey.vaccine:
        return miniAppVaccine.value;
      case WellServiceKey.wearable:
        return miniAppWearable.value;
      case WellServiceKey.aidoctor:
        return miniAppAiDoctor.value;
      case WellServiceKey.corp:
        return miniAppCorp.value;
      case WellServiceKey.shop:
      case WellServiceKey.kid:
      case WellServiceKey.engage:
        return false;
    }
  }

  List<List<HomeServiceItemModel>> filterService(
      List<List<HomeServiceItemModel>> servicePageList) {
    return servicePageList
        .map((page) {
          return page
              .where((service) {
                if (service.hasServiceKey) {
                  final serviceKey = service.serviceKey!;
                  return isServiceAvailable(serviceKey);
                } else {
                  return true;
                }
              })
              .whereNotNull()
              .toList();
        })
        .where((element) => element.isNotEmpty)
        .toList();
  }

  @override
  void onClose() {
    unleash.stop();
    super.onClose();
  }
}
