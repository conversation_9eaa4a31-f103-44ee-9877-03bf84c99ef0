import 'dart:async';

import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:samitivej_flutter_app/apps/cart/cart_app.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/models/address_model.dart';
import 'package:samitivej_flutter_app/models/district_model.dart';
import 'package:samitivej_flutter_app/models/postal_code_model.dart';
import 'package:samitivej_flutter_app/models/province_model.dart';
import 'package:samitivej_flutter_app/models/response_model.dart';
import 'package:samitivej_flutter_app/models/sub_district_model.dart';
import 'package:samitivej_flutter_app/services/api_client.dart';

class AddressApp extends GetxController {
  /// API client for the address services.
  final ApiClient _client = ApiClient(baseUrl: ApiConstant.totalHealthDomain);

  /// Error message.
  RxString errorMessage = ''.obs;

  /// Whether the loading of provinces is in progress or not.
  RxBool isProvincesLoading = false.obs;

  /// The province list.
  Rx<List<ProvinceModel>> provinces = Rx([]);

  /// Whether the loading of district list is in progress or not.
  RxBool isDistrictsLoading = false.obs;

  /// The district list.
  Rx<List<DistrictModel>> districts = Rx([]);

  /// Whether the loading of sub-district list is in progress or not.
  RxBool isSubDistrictsLoading = false.obs;

  /// The district list.
  Rx<List<SubDistrictModel>> subDistricts = Rx([]);

  /// Whether the loading of postal code list is in progress or not.
  RxBool isPostalCodesLoading = false.obs;

  /// The district list.
  Rx<List<PostalCodeModel>> postalCodes = Rx([]);

  /// Whether the loading of addresses is in progress or not.
  RxBool isAddressesLoading = false.obs;

  /// The address list.
  Rx<List<AddressModel>> addresses = Rx([]);

  /// The ID of the selected address.
  Rx<int?> selectedAddressId = Rx(null);

  StreamSubscription<int?>? _selectedAddressIdStreamSubscription;

  /// Whether the creating new address is in progress or not.
  RxBool isCreateAddressesLoading = false.obs;

  /// Whether the updating address is in progress or not.
  RxBool isUpdateAddressLoading = false.obs;

  /// Whether the deleting address is in progress or not.
  RxBool isDeleteAddressLoading = false.obs;

  @override
  void onInit() {
    super.onInit();

    // Recalculate delivery free every time an address is selected.
    _selectedAddressIdStreamSubscription = selectedAddressId.listen((value) {
      Get.find<CartApp>().calculateDeliveryFee();
    });
  }

  @override
  void onClose() {
    super.onClose();

    // Cancel stream subscription to free resources.
    _selectedAddressIdStreamSubscription?.cancel();
  }

  /// Gets province list.
  Future<void> getProvinces() async {
    // If the province list has been loaded before, don't reload it.
    // Provinces are very rare to change.
    if (provinces.value.isNotEmpty) {
      isProvincesLoading.value = false;
      return;
    }

    try {
      isProvincesLoading.value = true;
      final ResponseModel response =
          await _client.get(ApiConstant.getProvinces);
      final List listData = response.data!['listData'] as List;
      provinces.value = listData
          .map((province) =>
              ProvinceModel.fromJson(province as Map<String, dynamic>))
          .toList();
      isProvincesLoading.value = false;
    } on DioException {
      addresses.value = [];
      isProvincesLoading.value = false;
    }
  }

  /// Gets district list.
  Future<void> getDistricts() async {
    // If the district list has been loaded before, don't reload it.
    // Districts are very rare to change.
    if (districts.value.isNotEmpty) {
      isDistrictsLoading.value = false;
      return;
    }

    try {
      isDistrictsLoading.value = true;
      final ResponseModel response =
          await _client.get(ApiConstant.getDistricts);
      final List listData = response.data!['listData'] as List;
      districts.value = listData
          .map((district) =>
              DistrictModel.fromJson(district as Map<String, dynamic>))
          .toList();
      isDistrictsLoading.value = false;
    } on DioException {
      districts.value = [];
      isDistrictsLoading.value = false;
    }
  }

  /// Gets sub-district list.
  Future<void> getSubDistricts() async {
    // If the sub-district list has been loaded before, don't reload it.
    // Sub-districts are very rare to change.
    if (subDistricts.value.isNotEmpty) {
      isSubDistrictsLoading.value = false;
      return;
    }

    try {
      isSubDistrictsLoading.value = true;
      final ResponseModel response =
          await _client.get(ApiConstant.getSubDistricts);
      final List listData = response.data!['listData'] as List;
      subDistricts.value = listData
          .map((subDistrict) =>
              SubDistrictModel.fromJson(subDistrict as Map<String, dynamic>))
          .toList();
      isSubDistrictsLoading.value = false;
    } on DioException {
      subDistricts.value = [];
      isSubDistrictsLoading.value = false;
    }
  }

  /// Gets postal code list.
  Future<void> getPostalCode() async {
    // If the postal code list has been loaded before, don't reload it.
    // Postal codes are very rare to change.
    if (postalCodes.value.isNotEmpty) {
      isPostalCodesLoading.value = false;
      return;
    }

    try {
      isPostalCodesLoading.value = true;
      final ResponseModel response =
          await _client.get(ApiConstant.getPostalCodes);
      final List listData = response.data!['listData'] as List;
      postalCodes.value = listData
          .map((postalCode) =>
              PostalCodeModel.fromJson(postalCode as Map<String, dynamic>))
          .toList();
      isPostalCodesLoading.value = false;
    } on DioException {
      postalCodes.value = [];
      isPostalCodesLoading.value = false;
    }
  }

  /// Gets addresses of the current user.
  Future<void> getAddresses() async {
    try {
      isAddressesLoading.value = true;
      final ResponseModel response =
          await _client.get(ApiConstant.getAddresses);
      final List listData = response.data!['listData'] as List;
      addresses.value = listData.reversed
          .map((address) =>
              AddressModel.fromJson(address as Map<String, dynamic>))
          .toList();
      isAddressesLoading.value = false;
    } on DioException {
      addresses.value = [];
      isAddressesLoading.value = false;
    }
  }

  /// Creates a new address for the current user.
  Future<void> createAddress(AddressModel address) async {
    try {
      isCreateAddressesLoading.value = true;
      await _client.post(ApiConstant.createAddress, data: {
        'insertCustomerAddressRequestBean': {
          'name': address.name,
          'phoneNumber': address.phone,
          'address': address.address,
          'provinceCode': address.province?.code,
          'districtCode': address.district?.code,
          'subDistrictCode': address.subDistrict?.code,
          'postalCode': address.postalCode
        }
      });

      // Reload address list.
      await getAddresses();

      // Select the newly created address.
      if (addresses.value.isNotEmpty) {
        selectedAddressId.value = addresses.value[0].id;
      }

      isCreateAddressesLoading.value = false;
    } on DioException {
      isCreateAddressesLoading.value = false;
    }
  }

  /// Updates an address.
  Future<void> updateAddress(AddressModel address) async {
    try {
      isUpdateAddressLoading.value = true;
      await _client.post(ApiConstant.updateAddress, data: {
        'updateCustomerAddressRequestBean': {
          'addressId': address.id,
          'name': address.name,
          'phoneNumber': address.phone,
          'address': address.address,
          'provinceCode': address.province?.code,
          'districtCode': address.district?.code,
          'subDistrictCode': address.subDistrict?.code,
          'postalCode': address.postalCode
        }
      });
      final index = addresses.value.indexWhere((a) => a.id == address.id);
      if (index != -1) {
        addresses.value.removeAt(index);
        addresses.value.insert(index, address);
        addresses.trigger(addresses.value);
      }
      if (selectedAddressId.value == address.id) {
        selectedAddressId.trigger(address.id);
      }

      isUpdateAddressLoading.value = false;
    } on DioException {
      isUpdateAddressLoading.value = false;
    }
  }

  /// Deletes an address.
  Future<void> deleteAddress(int id) async {
    try {
      isDeleteAddressLoading.value = true;
      await _client.post(ApiConstant.deleteAddress, data: {
        'deleteCustomerAddressRequestBean': {'addressId': id}
      });
      final index = addresses.value.indexWhere((a) => a.id == id);
      if (index != -1) {
        addresses.value.removeAt(index);
        addresses.trigger(addresses.value);
      }
      if (selectedAddressId.value == id) {
        selectedAddressId.value = null;
      }

      isDeleteAddressLoading.value = false;
    } on DioException {
      isDeleteAddressLoading.value = false;
    }
  }
}
