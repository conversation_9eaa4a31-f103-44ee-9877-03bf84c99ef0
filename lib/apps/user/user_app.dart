import 'dart:convert';
import 'dart:io';

import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:datadog_flutter_plugin/datadog_flutter_plugin.dart';
import 'package:dio/dio.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:samitivej_flutter_app/apps/auth/auth_app.dart';
import 'package:samitivej_flutter_app/apps/guardian/guardian_controller.dart';
import 'package:samitivej_flutter_app/commons/di/app_injector.dart';
import 'package:samitivej_flutter_app/commons/error/exceptions.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/data/datasources/account/account_api_data_source.dart';
import 'package:samitivej_flutter_app/localization/app_translate_key.dart';
import 'package:samitivej_flutter_app/models/check_email_model.dart';
import 'package:samitivej_flutter_app/models/coin_model.dart';
import 'package:samitivej_flutter_app/models/customer_data_model.dart';
import 'package:samitivej_flutter_app/models/customer_model.dart';
import 'package:samitivej_flutter_app/models/image_info_model.dart';
import 'package:samitivej_flutter_app/models/info_model.dart';
import 'package:samitivej_flutter_app/models/insurance_company_card_model.dart';
import 'package:samitivej_flutter_app/models/login_method_enum.dart';
import 'package:samitivej_flutter_app/models/pace_url_model.dart';
import 'package:samitivej_flutter_app/models/plus_user_model.dart';
import 'package:samitivej_flutter_app/models/prompt_url_model.dart';
import 'package:samitivej_flutter_app/models/response_model.dart';
import 'package:samitivej_flutter_app/models/validation_model.dart';
import 'package:samitivej_flutter_app/screen/splash_screen.dart';
import 'package:samitivej_flutter_app/services/amplify_service.dart';
import 'package:samitivej_flutter_app/services/api_client.dart';
import 'package:samitivej_flutter_app/services/appointment/pace_service.dart';
import 'package:samitivej_flutter_app/services/appointment/prompt_service.dart';
import 'package:samitivej_flutter_app/utils/validation_helper.dart';

enum LinkMode { id, passport }

class UserApp extends GetxController {
  final ApiClient _client = ApiClient();
  final ApiClient _plusClient = ApiClient(baseUrl: ApiConstant.cognitoPlusUrl);
  final AuthApp authApp = Get.find<AuthApp>();
  RxString errorMessage = ''.obs;
  RxBool isLoading = false.obs;
  RxBool isHealthInfoLoading = false.obs;
  RxBool isTotalHealthCustomerLoading = false.obs;

  Rx<InfoModel>? userInfo;
  Rx<CoinModel>? coinData;
  Rx<CustomerDataModel> customerData =
      Rx<CustomerDataModel>(CustomerDataModel());

  RxBool isPaceLoading = RxBool(false);
  Rxn<PaceUrlModel> paceUrl = Rxn();

  bool get isPaceValid => paceUrl.value != null;

  RxBool isPromptLoading = RxBool(false);
  Rxn<PromptUrlModel> promptUrl = Rxn();

  bool get isPromptValid => promptUrl.value != null;

  Rxn<PlusUserModel> plusUserData = Rxn<PlusUserModel>();
  RxBool isPlusUserLoading = false.obs;

  RxString username = ''.obs;

  RxBool isTurnOnDeleteCard = false.obs;
  RxBool isHaveFrontIdCardImage = false.obs;
  RxBool isHaveBackIdCardImage = false.obs;

  RxList<InsuranceCompanyCardModel> insuranceImageFile =
      RxList<InsuranceCompanyCardModel>();

  RxString currentInsuranceMemberCard = ''.obs;
  Rx<ValidationModel> insuranceCompanyName = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> insuranceMemberNumber = Rx(ValidationModel(value: ''));
  Rx<String> frontInsuranceCardImageUrl = ''.obs;
  RxBool isHaveFrontInsuranceCardImage = false.obs;
  Rx<String> backInsuranceCardImageUrl = ''.obs;
  RxBool isHaveBackInsuranceCardImage = false.obs;

  Rx<AuthUser>? cognitoUserInfo;
  Rx<ImageInfoModel> userProfileImageUrl = Rx(ImageInfoModel(key: "", url: ""));
  RxnString displayName = RxnString();
  RxBool isUploadProfileImage = false.obs;
  Rxn<CheckEmailModel> checkEmail = Rxn();

  Rx<ValidationModel> firstNameThai = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> lastNameThai = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> firstNameEng = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> lastNameEng = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> phoneNumber = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> idCardNumber = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> dateOfBirth = Rx(ValidationModel(value: ''));
  Rx<TextEditingController> dateOfBirthController = Rx(TextEditingController());
  Rx<ValidationModel> countryCode = Rx(ValidationModel(value: '+66'));

  Rx<ValidationModel> phoneOTPCode = Rx(ValidationModel(value: ''));
  RxString phoneOTPRef = ''.obs;

  //create zendesk ticket for user can't connect hn
  final Rx<List<File>> idPassportImage = Rx([]);

  RxString deletingAccountReason = ''.obs;
  RxBool isDeletingAccountPasswordVisible = false.obs;
  Rx<ValidationModel> deletingAccountOtherReason =
      Rx(ValidationModel(value: ''));
  Rx<ValidationModel> deletingAccountPassword = Rx(ValidationModel(value: ''));

  List<String> get deletingAccountReasonList {
    return [
      AppTranslateKey.idontneeditanymore,
      AppTranslateKey.itstooexpensive,
      AppTranslateKey.imswitchingtosomeoneelse,
      // AppTranslateKey.other,
    ];
  }

  void changeCountryCode(String value) {
    countryCode.value = ValidationHelper.countryCodeValidate(value);
  }

  void changeDeletingAccountPassword(String value) {
    deletingAccountPassword.value = ValidationHelper.passwordValidate(value);
  }

  void changeDeletingAccountOtherReason(String value) {
    deletingAccountOtherReason.value = ValidationModel(value: value);
  }

  void changeFirstNameThai(String value) {
    firstNameThai.value = ValidationHelper.isNotEmptyValidate(value);
  }

  void changeLastNameThai(String value) {
    lastNameThai.value = ValidationHelper.isNotEmptyValidate(value);
  }

  void changeFirstNameEng(String value) {
    firstNameEng.value = ValidationHelper.isNotEmptyValidate(value);
  }

  void changeLastNameEng(String value) {
    lastNameEng.value = ValidationHelper.isNotEmptyValidate(value);
  }

  void changePhoneNumber(String value) {
    phoneNumber.value = ValidationHelper.phoneValidate(value.split("-").join());
  }

  void changeIdCardNumber(String value) {
    idCardNumber.value = ValidationHelper.idCardNumberValidate(value);
  }

  void changePassportNumber(String value) {
    idCardNumber.value = ValidationHelper.isNotEmptyValidate(value);
  }

  void changeDateOfBirth(String value) {
    dateOfBirth.value = ValidationHelper.isNotEmptyValidate(value);
  }

  void changePhoneOTP(String value) {
    phoneOTPCode.value = ValidationHelper.isNotEmptyValidate(value);
  }

  bool validateDeletingAccountPassword() {
    deletingAccountPassword =
        ValidationHelper.passwordValidate(deletingAccountPassword.value.value!)
            .obs;

    if (deletingAccountPassword.value.isError) {
      return false;
    } else {
      return true;
    }
  }

  bool validateLinkPatientProfileForm(LinkMode mode) {
    final ValidationModel phoneNumberValidated =
        ValidationHelper.isNotEmptyValidate(phoneNumber.value.value);
    final ValidationModel idCardNumberValidated = (mode == LinkMode.id)
        ? ValidationHelper.idCardNumberValidate(idCardNumber.value.value!)
        : ValidationHelper.isNotEmptyValidate(idCardNumber.value.value!);

    if (phoneNumberValidated.isError || idCardNumberValidated.isError) {
      return false;
    } else {
      return true;
    }
  }

  bool validatePhoneOTPForm() {
    phoneOTPCode.value =
        ValidationHelper.otpValidate(phoneOTPCode.value.value!);
    if (phoneOTPCode.value.isError) {
      return false;
    } else {
      return true;
    }
  }

  bool validateMoreInfoForm() {
    idCardNumber.value =
        ValidationHelper.idCardNumberValidate(idCardNumber.value.value!);
    dateOfBirth.value =
        ValidationHelper.isNotEmptyValidate(dateOfBirth.value.value);

    if (idCardNumber.value.isError || dateOfBirth.value.isError) {
      return false;
    } else {
      return true;
    }
  }

  void changeCompanyName(String value) {
    insuranceCompanyName.value = ValidationHelper.isNotEmptyValidate(value);
  }

  void changeMemberNumber(String value) {
    insuranceMemberNumber.value = ValidationHelper.isNotEmptyValidate(value);
  }

  bool validateInsuranceCardForm() {
    insuranceCompanyName.value =
        ValidationHelper.isNotEmptyValidate(insuranceCompanyName.value.value);
    insuranceMemberNumber.value =
        ValidationHelper.isNotEmptyValidate(insuranceMemberNumber.value.value);

    if (insuranceCompanyName.value.isError ||
        insuranceMemberNumber.value.isError) {
      return false;
    } else {
      return true;
    }
  }

  void resetField() {
    insuranceCompanyName.value = ValidationModel(value: '');
    insuranceMemberNumber.value = ValidationModel(value: '');
    frontInsuranceCardImageUrl.value = '';
    backInsuranceCardImageUrl.value = '';
    isHaveFrontInsuranceCardImage.value = false;
    isHaveBackInsuranceCardImage.value = false;
  }

  void resetLinkPatientProfileForm() {
    firstNameThai = Rx(ValidationModel(value: ''));
    lastNameThai = Rx(ValidationModel(value: ''));
    firstNameEng = Rx(ValidationModel(value: ''));
    lastNameEng = Rx(ValidationModel(value: ''));
    phoneNumber = Rx(ValidationModel(value: ''));
    idCardNumber = Rx(ValidationModel(value: ''));
    dateOfBirth = Rx(ValidationModel(value: ''));
    dateOfBirthController = Rx(TextEditingController());
    phoneOTPCode = Rx(ValidationModel(value: ''));
  }

  void resetLinkHNForm() {
    phoneNumber = Rx(ValidationModel(value: ''));
    idCardNumber = Rx(ValidationModel(value: ''));
  }

  void resetPhoneOTP() {
    phoneOTPCode = Rx(ValidationModel(value: ''));
  }

  LoginMethodEnum? userLoginProvider(String? message) {
    switch (message) {
      case "google":
        return LoginMethodEnum.google;
      case "facebook":
        return LoginMethodEnum.facebook;
      case "apple":
        return LoginMethodEnum.apple;
      case "email":
        return LoginMethodEnum.username;
      default:
        return null;
    }
  }

  Future<LoginMethodEnum?> getUserProviderFromEmail(String email) async {
    await checkMailWithTotalHealth(email);
    return userLoginProvider(checkEmail.value!.message);
  }

  Future<void> getAmplifyUserProfile() async {
    try {
      cognitoUserInfo = (await Amplify.Auth.getCurrentUser()).obs;
      username.value = await AmplifyService.getUserProfile();
    } catch (e) {
      final jwt = authApp.cognitoIdToken.value;
      // Split the JWT into 3 parts, separated by the '.' character
      List<String> jwtParts = jwt.split('.');
      // Decode the second part of the JWT (the payload) as JSON
      Map<String, dynamic> jwtPayload = json.decode(
              utf8.decode(base64Url.decode(base64Url.normalize(jwtParts[1]))))
          as Map<String, dynamic>;
      // Check if the 'exp' (expiration time) claim is present in the JWT payload
      if (jwtPayload.containsKey('email')) {
        // Get the expiration time as a Unix timestamp (the number of seconds since 1970)
        username.value = jwtPayload['exp'] as String;
      } else {
        rethrow;
      }
    }
  }

  Future<bool> emailAlreadyExist({required String email}) async {
    await checkMailWithTotalHealth(email);
    return !(checkEmail.value!.isFreeMail);
  }

  Future<void> checkMailWithTotalHealth(String email) async {
    try {
      final Map<String, dynamic> body = {"email": email};

      final ResponseModel response = await _client.post(
        ApiConstant.totalHealthCheckMail,
        options: Options(headers: {
          'ClientId': 2,
          'API-Key': "AF230215-A7F8-4623-ADAA-33FF23459FAE"
        }),
        data: body,
      );
      checkEmail.value = CheckEmailModel.fromJson(response.data!);
    } catch (e) {
      return;
    }
  }

  Future<void> deleteAccount() async {
    try {
      await _client.post(ApiConstant.totalHealthDeleteCustomer);
      await AmplifyService.deleteUser();
    } catch (e) {
      reportToZendesk("delete account from <${username.value}>",
          "Delete account log: ${e.toString()}");
      debugPrint("An error occur");
    }
  }

  Future<void> migrateUserToTotalHealth() async {
    await updateTotalHealthCustomerInfo(
      answerLinkTotalHealthPopup: true,
      pdpaAcceptation: true,
    );
  }

  Future<void> createUserTotalHealth({
    String? userEmail,
    String? firstName,
    required bool pdpaAcceptation,
  }) async {
    try {
      AuthApp authApp = Get.find<AuthApp>();
      final Map<String, String> token = await AmplifyService.fetchToken();

      final Map<String, dynamic> data = {
        "customerModelBean": {
          "name": {
            "given": firstName ?? username.value.split("@")[0],
          },
          "contactPoints": [
            {
              "contactCode": "email",
              "contactValue": userEmail ?? username.value,
            },
          ],
          "pdpaAcceptation": pdpaAcceptation,
        }
      };

      Options? options;
      if (authApp.cognitoIdToken.value.isNotEmpty) {
        options = Options(
          headers: {
            "Authorization": "Bearer ${authApp.cognitoIdToken.value}",
            "content-type": "application/json; charset=utf-8",
          },
        );
      } else {
        options = Options(
          headers: {
            "Authorization": "Bearer ${token['idToken']}}",
            "content-type": "application/json; charset=utf-8",
          },
        );
      }

      await _client.post(
        ApiConstant.totalHealthInsertCustomer,
        data: data,
        options: options,
      );
    } catch (e) {
      debugPrint("An error occur");
      rethrow;
    }
  }

  Future<void> updateUserInfoToTotalHealth({
    String? passportId,
    String? phoneNumber,
  }) async {
    try {
      final Map<String, dynamic> body = {
        "passportId": passportId,
        "phoneNumber": phoneNumber,
      };

      final ResponseModel response = await _client.post(
        ApiConstant.plusUpdateUser,
        data: body,
      );
      if (response.code == 203 && response.data != null) {
        errorMessage.value = response.data!["message"].toString();
        throw UnderageException();
      }
    } on DioException catch (e) {
      errorMessage.value = e.response!.data["message"].toString();
      debugPrint("An error occur");
      rethrow;
    }
  }

  Future<void> getHealthInfo() async {
    try {
      isHealthInfoLoading.value = true;

      await _client.get(
        ApiConstant.getHealthInfo2,
      );

      isHealthInfoLoading.value = false;
    } catch (e) {
      debugPrint("An error occur");
      isHealthInfoLoading.value = false;
    }
  }

  /// Returns the member ID.
  Future<int?> getMemberId() async {
    if (customerData.value.customer == null) {
      await getTotalHealthCustomerInfo();
    }
    return customerData.value.customer?.memberId;
  }

  Future<bool> getTotalHealthCustomerInfo() async {
    try {
      isTotalHealthCustomerLoading.value = true;

      final ResponseModel response = await _client.post(
        ApiConstant.totalHealthGetCustomer,
      );

      if (response.data != null) {
        customerData.value = CustomerDataModel.fromJson(response.data!);

        if (customerData.value.customer != null) {
          final customer = customerData.value.customer!;
          if (customer.profileImageUrl != null) {
            final imageUrl = (await AmplifyService.getUrl(
              key: customer.profileImageUrl!,
            ));
            userProfileImageUrl.value = imageUrl;
          } else {
            userProfileImageUrl.value = ImageInfoModel(key: "", url: "");
          }
          authApp.isHavePin.value = customerData.value.isHavePin;

          isTotalHealthCustomerLoading.value = false;

          updateDisplayName();
          await setFirebaseUserId(customer.memberId?.toString() ?? '-');
          await setDatadogUserInfo(customer);
          return true;
        }
      }

      return false;
    } on DioException catch (e) {
      if (e.response?.statusCode == 503) {
        throw const ServiceDownException();
      }
      return false;
    } catch (e) {
      debugPrint("An error occur");
      isTotalHealthCustomerLoading.value = false;
      return false;
    }
  }

  Future<void> updateTotalHealthCustomerInfo({
    String? userName,
    String? firstName,
    String? lastName,
    String? phoneNumber,
    DateTime? birthDate,
    String? gender,
    bool? answerLinkTotalHealthPopup,
    bool? pdpaAcceptation,
    String? profileImgUrl,
    bool? accountConfirmed,
    String? displayName,
    int? type,
  }) async {
    try {
      isTotalHealthCustomerLoading.value = true;

      final Map<String, dynamic> body = {
        "updateCustomerReqBean": {
          "profileImgUrl": profileImgUrl,
          "firstName": firstName,
          "lastName": lastName,
          "phoneNumber": phoneNumber, // **********
          "birthDate": birthDate != null
              ? DateFormat('yyyy-MM-dd').format(birthDate)
              : null, // 2022-05-11
          "gender": gender, // F=female, M=male
          "answerLinkTotalHealthPopup": answerLinkTotalHealthPopup,
          "pdpaAcceptation": pdpaAcceptation,
          "accountConfirmed": accountConfirmed,
          "displayName": displayName,
          "type": type,
        },
      };

      await _client.post(
        ApiConstant.totalHealthUpdateCustomer,
        data: body,
      );

      isTotalHealthCustomerLoading.value = false;

      await getTotalHealthCustomerInfo();
    } catch (e) {
      debugPrint("An error occur");
      isTotalHealthCustomerLoading.value = false;
    }
  }

  Future<void> checkCustomerHn() async {
    if (isHnConnected != false) {
      isPlusUserLoading.value = true;
      AccountApiDataSource apiDataSource =
          AppInjector.get<AccountApiDataSource>();
      try {
        final checkCustomerHnModel = await apiDataSource.checkCustomerHn();
        if (checkCustomerHnModel.isHashChange) {
          await getTotalHealthCustomerInfo();
          await getPlusUserInfo();
        }
      } on Exception catch (e) {
        debugPrint("An error occur: $e");
      }
      isPlusUserLoading.value = false;
    }
  }

  Future<void> getPlusUserInfo() async {
    try {
      isPlusUserLoading.value = true;
      final ResponseModel response = await _plusClient.post(
        ApiConstant.totalHealthGetCustomerHn,
      );

      if (response.data != null) {
        plusUserData.value =
            PlusUserModel.fromJson(response.data as Map<String, dynamic>);
        if (plusUserData.value!.customerHn.isNotEmpty) {
          Future.wait([
            checkCustomerHn(),
            getPaceUrl(),
            getPromptUrl(),
            GuardianController().fetchGuardianList()
          ]);
        } else {
          // TODO: clear data if hn empty
          GuardianController().clear();
        }
      }
    } catch (e) {
      plusUserData.value = null;
    }
    isPlusUserLoading.value = false;
    updateDisplayName();
  }

  bool? get isHnConnected {
    if (isPlusUserLoading.value) {
      return null;
    }
    PlusUserModel? customer = plusUserData.value;
    return ((customer != null && customer.customerHn.isNotEmpty));
  }

  void updateDisplayName() {
    if (isHnConnected == null || isTotalHealthCustomerLoading.value) {
      displayName.value = null;
    } else {
      String? firstName = customerData.value.customer?.firstName;
      firstName = firstName?.isEmpty == true ? null : firstName;
      String? usernameSplit = username.value.split("@").first;
      displayName.value = customerData.value.customer?.displayName ??
          firstName ??
          usernameSplit;
    }
  }

  Future<void> reportToZendesk(String title, String message) async {
    try {
      final Map<String, dynamic> body = {
        'title': "Error with $title",
        'email': username.value,
        'detail': message,
      };

      await _client.post(
        ApiConstant.totalHealthCreateSignupZendesk,
        data: body,
      );
    } catch (e) {
      return;
    }
  }

  Future<void> setFirebaseUserId(String userId) async {
    await FirebaseCrashlytics.instance.setUserIdentifier(userId);
    await FirebaseAnalytics.instance.setUserId(id: userId);
  }

  Future<void> setDatadogUserInfo(CustomerModel customer) async {
    final userId = customer.memberId?.toString() ?? '-';
    DatadogSdk.instance.setUserInfo(
      id: userId,
      // email: customer.email?.toString() ?? '-',
      // name: customer.displayName?.toString() ?? '-',
      // extraInfo: {
      //   'phoneNumber': customer.phoneNumber?.toString() ?? '-',
      // },
    );
  }

  Future<void> getPaceUrl() async {
    try {
      isPaceLoading.value = true;
      paceUrl.value = await PaceService.getPaceUrl();
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    } finally {
      isPaceLoading.value = false;
    }
  }

  Future<void> getPromptUrl() async {
    try {
      isPromptLoading.value = true;
      promptUrl.value = await PromptService.getPromptUrl();
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    } finally {
      isPromptLoading.value = false;
    }
  }
}
