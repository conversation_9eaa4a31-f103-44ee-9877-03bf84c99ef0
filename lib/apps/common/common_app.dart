import 'dart:io';

import 'package:camera/camera.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:samitivej_flutter_app/apps/auth/auth_app.dart';
import 'package:samitivej_flutter_app/apps/user/user_app.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/constants/app_asset.dart';
import 'package:samitivej_flutter_app/localization/app_localization.dart';
import 'package:samitivej_flutter_app/localization/app_translate_key.dart';
import 'package:samitivej_flutter_app/models/hospital_model.dart';
import 'package:samitivej_flutter_app/models/mood_item_model.dart';
import 'package:samitivej_flutter_app/models/response_model.dart';
import 'package:samitivej_flutter_app/screen/calendar/calendar_screen.dart';
import 'package:samitivej_flutter_app/screen/cart/cart_webview_w_scaffold.dart';
import 'package:samitivej_flutter_app/screen/home/<USER>';
import 'package:samitivej_flutter_app/screen/setting/main_setting_screen.dart';
import 'package:samitivej_flutter_app/services/api_client.dart';
import 'package:samitivej_flutter_app/services/notification_service.dart';
import 'package:samitivej_flutter_app/services/storage_service.dart';
import 'package:webview_flutter/webview_flutter.dart';

class CommonApp extends GetxController {
  int selectedNavBarItem = 0;

  Rx<bool> appLoading = Rx(false);

  RxString selectedMeal = ''.obs;
  RxString selectedCountry = ''.obs;
  RxString selectedFeedback = ''.obs;
  late List<CameraDescription> cameras;
  RxString selectedLanguage = 'th'.obs;
  RxList<int?> selectedFoodTaskId = RxList.empty();
  RxInt selectedBlogCategory = 0.obs;

  RxList<HospitalModel> hospitalList = RxList.empty();

  //RxList<File> feedbackImages = RxList.empty();
  RxBool isLoading = false.obs;
  RxList<WebViewCookie> cookies = RxList.empty();
  String totalHealthDomainName = ApiConstant.totalHealthWebDomain;
  String foodDomainName = '${ApiConstant.foodWebDomain}home/home';
  String foodQuestionDomainName = '${ApiConstant.foodWebDomain}questionaire';
  String fitDomainName = '${ApiConstant.fitWebDomain}home';
  String fitQuestionDomainName = '${ApiConstant.fitWebDomain}questionaire';
  String fitTestAgain = '${ApiConstant.fitWebDomain}questionaire?redo=true';
  String privacyNotice = '${ApiConstant.totalHealthWebDomain}privacy';
  String chatWellDomainName = '${ApiConstant.foodWebDomain}chat';

  RxString paymentQuery = RxString("");
  InAppWebViewController? paymentWebController;

  Future<void> initApp() async {
    final storage = StorageService();
    cameras = await availableCameras();
    String? lang = storage.read<String>("selectedlang");
    setLangauge(lang ?? Platform.localeName.split("_").first);
    if (lang == null) {
      await storage.write("selectedlang", Platform.localeName.split("_").first);
    }
    getHospital();
    paymentQuery.listen((query) async {
      if (Platform.isAndroid) {
        await paymentWebController?.clearHistory();
      }
      await paymentWebController?.loadUrl(
          urlRequest: URLRequest(
              url: concatQuery(ApiConstant.paymentWebDomain, query)));
    });
  }

  WebUri concatQuery(String base, String? query) {
    final baseUri = Uri.parse(base);
    return WebUri(Uri(
      scheme: baseUri.scheme,
      host: baseUri.host,
      path: baseUri.path,
      port: baseUri.port,
      query: query,
    ).toString());
  }

  void getHospital() {
    final lang = selectedLanguage.value;
    try {
      switch (lang) {
        case "th":
          final hospitalTH = [
            {
              "hospitalId": 1,
              "languageId": 2,
              "hospitalCode": "SVH",
              "hospitalCodePlus": 11,
              "hospitalName": "โรงพยาบาลสมิติเวช สุขุมวิท",
              "hospitalShortName": "สุขุมวิท",
              "hospitalTel": "02-022-2222",
              "hospitalAddress":
                  "133 สุขุมวิท 49 แขวงคลองตันเหนือ เขตวัฒนา กรุงเทพฯ 10110",
              "hospitalEmail": "<EMAIL>",
              "hospitalWeb": "https://www.samitivejhospitals.com",
              "hospitalAddressUrl": "https://goo.gl/maps/i3q6SBAyfLWqzGpz5"
            },
            {
              "hospitalId": 2,
              "languageId": 2,
              "hospitalCode": "SNH",
              "hospitalCodePlus": 12,
              "hospitalName": "โรงพยาบาลสมิติเวช ศรีนครินทร์",
              "hospitalShortName": "ศรีนครินทร์",
              "hospitalTel": "02-022-2222",
              "hospitalAddress":
                  "488 ถนนศรีนครินทร์ เขต/แขวงสวนหลวง กรุงเทพ 10250",
              "hospitalEmail": "<EMAIL>",
              "hospitalWeb": "https://www.samitivejhospitals.com",
              "hospitalAddressUrl": "https://goo.gl/maps/wC3BqwTpkFTD4eG3A"
            },
            {
              "hospitalId": 3,
              "languageId": 2,
              "hospitalCode": "SSH",
              "hospitalCodePlus": 18,
              "hospitalName": "โรงพยาบาลสมิติเวช ศรีราชา",
              "hospitalShortName": "ศรีราชา",
              "hospitalTel": "03-832-4111",
              "hospitalAddress":
                  "8 ซอย แหลมเกตุ ถนน เจิมจอมพล ตำบล ศรีราชา อำเภอ ศรีราชา จังหวัด ชลบุรี 20110",
              "hospitalEmail": "<EMAIL>",
              "hospitalWeb": "https://www.samitivejsriracha.com",
              "hospitalAddressUrl": "https://g.page/samitivejsriracha?share"
            },
            {
              "hospitalId": 4,
              "languageId": 2,
              "hospitalCode": "STH",
              "hospitalCodePlus": 45,
              "hospitalName": "โรงพยาบาลสมิติเวช ธนบุรี",
              "hospitalShortName": "ธนบุรี",
              "hospitalTel": "02-408-0191",
              "hospitalAddress":
                  "337 ถนน สมเด็จพระเจ้าตากสิน แขวง สำเหร่ เขตธนบุรี กรุงเทพมหานคร 10600",
              "hospitalEmail": "<EMAIL>",
              "hospitalWeb": "https://samitivejthonburi.com",
              "hospitalAddressUrl": "https://goo.gl/maps/YmzaFAaDh2qGXfrd9"
            },
            {
              "hospitalId": 5,
              "languageId": 2,
              "hospitalCode": "SCH",
              "hospitalCodePlus": 58,
              "hospitalName": "โรงพยาบาลสมิติเวช ชลบุรี",
              "hospitalShortName": "ชลบุรี",
              "hospitalTel": "03-303-8888",
              "hospitalAddress":
                  "888/88 หมู่ 3 ถนนสุขุมวิท ต.บ้านสวน อ.เมืองชลบุรี จ.ชลบุรี 20000",
              "hospitalEmail": "<EMAIL>",
              "hospitalWeb": "https://www.samitivejchonburi.com",
              "hospitalAddressUrl": "https://g.page/SamitivejChonburi?share"
            },
            {
              "hospitalId": 6,
              "languageId": 2,
              "hospitalCode": "SCT",
              "hospitalCodePlus": 46,
              "hospitalName": "โรงพยาบาลสมิติเวช ไชน่าทาวน์",
              "hospitalShortName": "ไชน่าทาวน์",
              "hospitalTel": "02-118-7888",
              "hospitalAddress":
                  "624 ถนนเยาวราช แขวงสัมพันธวงศ์ เขตสัมพันธวงศ์ กรุงเทพมหานคร 10100",
              "hospitalEmail": "<EMAIL>",
              "hospitalWeb": "https://www.samitivejchinatown.com",
              "hospitalAddressUrl": "https://goo.gl/maps/yEMTcfQz8sVET9M16"
            }
          ];
          hospitalList.value =
              hospitalTH.map((e) => HospitalModel.fromJson(e)).toList();
          break;
        case "en":
        default:
          final hospitalEN = [
            {
              "hospitalId": 1,
              "languageId": 1,
              "hospitalCode": "SVH",
              "hospitalCodePlus": 11,
              "hospitalName": "Samitivej Sukhumvit Hospital",
              "hospitalShortName": "Sukhumvit",
              "hospitalTel": "02-022-2222",
              "hospitalAddress":
                  "133 Sukhumvit 49, Klongtan Nua, Vadhana, Bangkok 10110",
              "hospitalEmail": "<EMAIL>",
              "hospitalWeb": "https://www.samitivejhospitals.com",
              "hospitalAddressUrl": "https://goo.gl/maps/i3q6SBAyfLWqzGpz5"
            },
            {
              "hospitalId": 2,
              "languageId": 1,
              "hospitalCode": "SNH",
              "hospitalCodePlus": 12,
              "hospitalName": "Samitivej Srinakarin Hospital",
              "hospitalShortName": "Srinakarin",
              "hospitalTel": "02-022-2222",
              "hospitalAddress": "488 Srinakarin Rd., Suanluang, Bangkok 10250",
              "hospitalEmail": "<EMAIL>",
              "hospitalWeb": "https://www.samitivejhospitals.com",
              "hospitalAddressUrl": "https://goo.gl/maps/wC3BqwTpkFTD4eG3A"
            },
            {
              "hospitalId": 3,
              "languageId": 1,
              "hospitalCode": "SSH",
              "hospitalCodePlus": 18,
              "hospitalName": "Samitivej Sriracha Hospital",
              "hospitalShortName": "Sriracha",
              "hospitalTel": "03-832-4111",
              "hospitalAddress":
                  "8 Soi Laemket, Jermjompol Rd., Sriracha, Chonburi 20110",
              "hospitalEmail": "<EMAIL>",
              "hospitalWeb": "https://www.samitivejsriracha.com",
              "hospitalAddressUrl": "https://g.page/samitivejsriracha?share"
            },
            {
              "hospitalId": 4,
              "languageId": 1,
              "hospitalCode": "STH",
              "hospitalCodePlus": 45,
              "hospitalName": "Samitivej Thonburi Hospital",
              "hospitalShortName": "Thonburi",
              "hospitalTel": "02-408-0191",
              "hospitalAddress":
                  "337, Somdet Phra Chao Tak Sin Rd, Khwaeng Samre, Khet Thon Buri, Krungthep Mahanakorn 10600, Thailand\n",
              "hospitalEmail": "<EMAIL>",
              "hospitalWeb": "https://samitivejthonburi.com",
              "hospitalAddressUrl": "https://goo.gl/maps/YmzaFAaDh2qGXfrd9"
            },
            {
              "hospitalId": 5,
              "languageId": 1,
              "hospitalCode": "SCH",
              "hospitalCodePlus": 58,
              "hospitalName": "Samitivej Chonburi Hospital",
              "hospitalShortName": "Chonburi",
              "hospitalTel": "03-303-8888",
              "hospitalAddress":
                  "888/88 Moo.3 Sukhumvit Rd., Ban Suan Mueang Chon Buri, Chon Buri, 20000",
              "hospitalEmail": "<EMAIL>",
              "hospitalWeb": "https://www.samitivejchonburi.com",
              "hospitalAddressUrl": "https://g.page/SamitivejChonburi?share"
            },
            {
              "hospitalId": 6,
              "languageId": 1,
              "hospitalCode": "SCT",
              "hospitalCodePlus": 46,
              "hospitalName": "Samitivej Chinatown Hospital",
              "hospitalShortName": "Chinatown",
              "hospitalTel": "02-118-7888",
              "hospitalAddress":
                  "624 Yaowaraj Road, Sumpuntawong Bangkok, 10100 Thailand.",
              "hospitalEmail": "<EMAIL>",
              "hospitalWeb": "https://www.samitivejchinatown.com",
              "hospitalAddressUrl": "https://goo.gl/maps/yEMTcfQz8sVET9M16"
            }
          ];
          hospitalList.value =
              hospitalEN.map((e) => HospitalModel.fromJson(e)).toList();
      }
    } catch (e) {
      return;
    }
  }

  Future<void> setLangauge(String lang) async {
    final storage = StorageService();
    selectedLanguage.value = lang;
    AppLocalization().changeLocale(lang);
    await storage.write("selectedlang", lang);
    if (paymentWebController != null) {
      refreshPaymentWebview();
    }
  }

  Future<void> refreshPaymentWebview() async {
    final CookieManager cookieManager = CookieManager.instance();
    final langCookie = await cookieManager.getCookie(
        url: WebUri(ApiConstant.paymentWebDomain), name: "lang");
    if (langCookie?.value != selectedLanguage.value) {
      await createCookie();
      for (var cookie in cookies) {
        await cookieManager.setCookie(
          url: WebUri(ApiConstant.paymentWebDomain),
          domain: cookie.domain,
          name: cookie.name,
          value: cookie.value,
        );
      }
    }
    paymentWebController?.reload();
    return;
  }

  int getLocaleYear(int christianYear) {
    return christianYear + (selectedLanguage.value == "th" ? 543 : 0);
  }

  final List<String> countryList = ['Thailand', 'Japan', 'China', 'Other'];

  List<String> get feedbackList => [
        AppTranslateKey.connectsamitivejhn,
        AppTranslateKey.healthtracker,
        AppTranslateKey.wellMember,
        AppTranslateKey.orderTracking,
        AppTranslateKey.appointmentWithDoctor,
        AppTranslateKey.samitivejVirtualHospital,
        AppTranslateKey.wellApplicationProblem,
        AppTranslateKey.other,
      ];

  final List<String> mealSelectionList = [
    'Select meal',
    'Before meal',
    'After meal',
    'Before bed',
    'Insulin',
    'None',
  ];

  final List<String> instruction = [
    "1. Make a list and prioritize your concerns.",
    "2. Take information with you to the doctor.",
    "3. Consider bringing a family member or friend to the doctor's visit.",
    "4. Keep your doctor up to date.",
    "5. Be sure you can see and hear as well as possible.",
    "6. Request an interpreter if you need one.",
  ];

  List<MoodItemModel> get moodItemList => [
        MoodItemModel(
          id: 1,
          name: AppTranslateKey.depressedMoodName,
          description: AppTranslateKey.depressedMoodDescription,
          image: AppAsset.depressedFeelIcon,
          alt: "Depressed face",
        ),
        MoodItemModel(
          id: 2,
          name: AppTranslateKey.sadMoodName,
          description: AppTranslateKey.sadMoodDescription,
          image: AppAsset.sadFeelIcon,
          alt: "Sad face",
        ),
        MoodItemModel(
          id: 3,
          name: AppTranslateKey.unhappyMoodName,
          description: AppTranslateKey.unhappyMoodDescription,
          image: AppAsset.unhappyFeelIcon,
          alt: "Unhappy face",
        ),
        MoodItemModel(
          id: 4,
          name: AppTranslateKey.happyMoodName,
          description: AppTranslateKey.happyMoodDescription,
          image: AppAsset.happyFeelIcon,
          alt: "Happy face",
        ),
        MoodItemModel(
          id: 5,
          name: AppTranslateKey.joyfulMoodName,
          description: AppTranslateKey.joyfulMoodDescription,
          image: AppAsset.joyfulFeelIcon,
          alt: "Joyful face",
        ),
      ];

  final List<Widget> pages = <Widget>[
    const HomeScreen(),
    const CalendarScreen(),
    const SizedBox.shrink(),
    const MainSettingScreen(),
  ];

  void onTapNavBarItem(int index) {
    if (index == 2) {
      CartWebviewWithScaffold.open();
      return;
    }
    if (selectedNavBarItem != index) {
      selectedNavBarItem = index;
      update();
    }
  }

  double changeNavBarPosition(double screen, int itemCount) {
    final double position = screen / itemCount;
    final double paddingSize = screen * 0.006;
    return (position * selectedNavBarItem) + paddingSize;
  }

  Future<bool> requestPermission(
    Permission permission, {
    bool forceOpenSetting = true,
  }) async {
    try {
      final serviceStatus = await permission.isGranted;

      if (serviceStatus) {
        return true;
      }

      final status = await permission.request();

      if (status == PermissionStatus.granted) {
        if (kDebugMode) {
          print('Permission Granted');
        }
        return true;
      } else if (status == PermissionStatus.denied) {
        if (kDebugMode) {
          print('Permission denied');
        }
        return false;
      } else if (status == PermissionStatus.permanentlyDenied) {
        if (kDebugMode) {
          print('Permission Permanently Denied');
        }
        if (forceOpenSetting) {
          await openAppSettings();
        }
        return false;
      }
      return false;
    } catch (e) {
      debugPrint("An error occur");
      rethrow;
    }
  }

  Future<void> createCookie() async {
    UserApp userApp = Get.find<UserApp>();
    AuthApp authApp = Get.find<AuthApp>();

    appLoading.value = true;

    await authApp.ensureCognitoIdToken();
    String? deviceId = await NotificationService.getDeviceId();

    cookies.clear();
    cookies.addAll([
      WebViewCookie(
        domain: '.samitivejhospitals.com',
        name: 'username',
        value: userApp.cognitoUserInfo!.value.userId,
      ),
      WebViewCookie(
        domain: '.samitivejhospitals.com',
        name: 'memberId',
        value: '${userApp.customerData.value.customer!.memberId}',
      ),
      WebViewCookie(
        domain: '.samitivejhospitals.com',
        name: 'deviceId',
        value: deviceId!,
      ),
      WebViewCookie(
        domain: '.samitivejhospitals.com',
        name: 'refresh_token',
        value: authApp.refreshToken!,
      ),
      WebViewCookie(
        domain: '.samitivejhospitals.com',
        name: 'access_token',
        value: authApp.cognitoIdToken.value,
      ),
      const WebViewCookie(
        domain: '.samitivejhospitals.com',
        name: 'channel',
        value: 'supperapp',
      ),
      WebViewCookie(
        domain: '.samitivejhospitals.com',
        name: 'lang',
        value: selectedLanguage.value,
      ),
    ]);

    if (kDebugMode) {
      for (WebViewCookie cookie in cookies) {
        print('${cookie.name} ==> ${cookie.value}');
      }
    }

    appLoading.value = false;
  }

  /// API client for the main services.
  final ApiClient _client = ApiClient(baseUrl: ApiConstant.totalHealthDomain);

  /// true if calling getS3Image fn.
  Rx<bool> isLoadingS3Image = Rx<bool>(false);

  Future<String> getS3Image(String fileName) async {
    String img = '';
    try {
      isLoadingS3Image.value = true;
      final ResponseModel response =
          await _client.get('${ApiConstant.getS3Image}?fileName=$fileName');
      img = response.data!['data'].toString();
    } on DioException catch (_) {
      rethrow;
    } finally {
      isLoadingS3Image.value = false;
    }
    return img;
  }
}
