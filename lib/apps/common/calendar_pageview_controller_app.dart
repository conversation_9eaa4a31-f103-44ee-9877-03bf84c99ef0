import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CalendarPageviewControllerApp extends GetxController {
  PageController pageController = PageController();
  RxDouble currentPageValue = 0.0.obs;

  Future<void> animateTo(
    int pageIndex, {
    int milliseconds = 200,
  }) async {
    await pageController.animateToPage(
      pageIndex,
      duration: Duration(milliseconds: milliseconds),
      curve: Curves.easeIn,
    );
    currentPageValue.value = pageController.page!;
  }
}
