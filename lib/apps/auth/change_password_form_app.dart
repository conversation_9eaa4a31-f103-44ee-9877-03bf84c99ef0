import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:get/get.dart';
import 'package:samitivej_flutter_app/localization/app_translate_key.dart';
import 'package:samitivej_flutter_app/models/validation_model.dart';
import 'package:samitivej_flutter_app/services/amplify_service.dart';
import 'package:samitivej_flutter_app/utils/validation_helper.dart';

class ChangePasswordFormApp extends GetxController {
  Rx<ValidationModel> email = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> forgotCode = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> oldPassword = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> newPassword = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> confirmNewPassword = Rx(ValidationModel(value: ''));

  RxBool isPasswordVisible = false.obs;
  RxBool isOldPasswordVisible = false.obs;
  RxBool isNewPasswordVisible = false.obs;
  RxBool isConfirmPasswordVisible = false.obs;
  RxBool isLoading = false.obs;

  RxInt isChannelReceivingCode = 0.obs;

  RxBool isDirty = false.obs;

  void changeEmail(String value) {
    email.value = ValidationHelper.emailValidate(value);
  }

  void changeChannelReceivingCode(int channel) {
    isChannelReceivingCode.value = channel;
  }

  void changeForgotCode(String value) {
    forgotCode.value = ValidationHelper.isNotEmptyValidate(value);
  }

  void changeOldPassword(String value) {
    oldPassword.value = ValidationHelper.passwordValidate(value);
  }

  void changeNewPassword(String value) {
    newPassword.value = ValidationHelper.passwordValidate(value);
    if (confirmNewPassword.value.value!.isNotEmpty) {
      confirmNewPassword.value = ValidationHelper.confirmPasswordValidate(
        newPassword.value.value!,
        confirmNewPassword.value.value!,
      );
    }

    isDirty.value = true;
  }

  void changeConfirmNewPassword(String value) {
    confirmNewPassword.value = ValidationHelper.confirmPasswordValidate(
      newPassword.value.value!,
      value,
    );

    isDirty.value = true;
  }

  ValidationModel emailValidate(String value) {
    final bool isNotEmail = ValidationHelper.emailValidate(value).isError;
    final bool isContainAtSign = value.contains('@');

    if (value.isEmpty) {
      return ValidationModel(
        value: '',
        error: AppTranslateKey.thisfieldisrequired,
        isError: true,
      );
    } else if (!isContainAtSign && isNotEmail) {
      return ValidationModel(
        value: '',
        error: AppTranslateKey.pleasecompleteyouremail,
        isError: true,
      );
    } else if (isNotEmail) {
      return ValidationModel(
        value: '',
        error: AppTranslateKey.invalidemailformat,
        isError: true,
      );
    } else {
      return ValidationModel(value: value);
    }
  }

  bool validateEmail() {
    final ValidationModel emailValidate =
        ValidationHelper.emailValidate(email.value.value!);
    if (emailValidate.isError) {
      return false;
    } else if (emailValidate.value == null) {
      return false;
    } else if (emailValidate.value!.isEmpty) {
      return false;
    } else {
      return true;
    }
  }

  bool validateForgotCodeForm() {
    forgotCode =
        ValidationHelper.sixDigitPinCodeValidate(forgotCode.value.value!).obs;
    if (forgotCode.value.isError) {
      return false;
    } else {
      return true;
    }
  }

  bool validateChangePasswordForm() {
    oldPassword.value =
        ValidationHelper.passwordValidate(oldPassword.value.value!);
    newPassword.value =
        ValidationHelper.passwordValidate(newPassword.value.value!);
    confirmNewPassword.value = ValidationHelper.confirmPasswordValidate(
      newPassword.value.value!,
      confirmNewPassword.value.value!,
    );

    if (oldPassword.value.isError ||
        newPassword.value.isError ||
        confirmNewPassword.value.isError) {
      return false;
    } else {
      return true;
    }
  }

  bool validateForgotPasswordForm() {
    final newPasswordResult =
        ValidationHelper.passwordValidate(newPassword.value.value!);
    final confirmNewPasswordResult = ValidationHelper.confirmPasswordValidate(
      newPassword.value.value!,
      confirmNewPassword.value.value!,
    );

    if (newPasswordResult.isError || confirmNewPasswordResult.isError) {
      return false;
    } else {
      return true;
    }
  }

  void resetForm() {
    email.value = ValidationModel(value: '');

    resetPasswordForm();
  }

  void resetPasswordForm() {
    oldPassword.value = ValidationModel(value: '');
    newPassword.value = ValidationModel(value: '');
    confirmNewPassword.value = ValidationModel(value: '');

    isPasswordVisible.value = false;
    isOldPasswordVisible.value = false;
    isNewPasswordVisible.value = false;
    isConfirmPasswordVisible.value = false;

    isDirty.value = false;
  }

  Future<bool> confirmResetPassword({
    required String email,
    required String newPassword,
    required String code,
  }) async {
    try {
      await AmplifyService.confirmResetPassword(
        email,
        newPassword,
        code,
      );
      return true;
    } on AmplifyException {
      return false;
    } catch (e) {
      return false;
    }
  }
}
