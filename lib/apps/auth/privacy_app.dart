import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:samitivej_flutter_app/apps/user/user_app.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/models/consent_form_model.dart';
import 'package:samitivej_flutter_app/services/api_client.dart';
import 'package:samitivej_flutter_app/services/consent_service.dart';

class ConsentHospital {
  const ConsentHospital({
    required this.hospitalNameEn,
    required this.hospitalNameTh,
    required this.hospitalNo,
    required this.hospitalSite,
  });

  final String hospitalNameTh;
  final String hospitalNameEn;
  final String hospitalNo;
  final String hospitalSite;
}

class CheckShowConsentResult {
  final List<ConsentHospital> consentHospital;
  final bool needShow;

  CheckShowConsentResult(
      {required this.consentHospital, required this.needShow});
}

class PrivacyApp extends GetxController {
  final ApiClient _client = ApiClient();
  final Rx<List<ConsentFormModel>> consentforms = Rx([]);
  final Rx<List<String>> acceptedHospital = Rx([]);
  final Rx<String?> selectedHospital = Rx(null);
  final Rx<List<ConsentHospital>> hospitalList = Rx([]);
  final RxnBool needShow = RxnBool();

  final List<int> requestAccepted = [1, 3];
  final Rx<bool> consentFormState = Rx<bool>(false);

  Future<bool> isAcceptRequireConsent() async {
    if (hospitalList.value.isEmpty) {
      await getHospitalList();
    }

    final Map<ConsentHospital, List<ConsentFormModel>> consentOfHospital = {};
    await Future.wait<void>([
      ...hospitalList.value.map((e) {
        return (() async {
          final consentForm =
              await ConsentService.getConsentFormByHospital(e.hospitalNo);
          consentOfHospital[e] = consentForm;
        })();
      })
    ]);

    List<ConsentHospital> hospitalShouldShow = [];
    for (final coh in consentOfHospital.entries) {
      final requiredAccepted = coh.value.where((e) {
        return requestAccepted.contains(e.id);
      }).toList();

      if (requiredAccepted.any((e) => e.isAccept != true)) {
        hospitalShouldShow.add(coh.key);
      }
    }

    hospitalList.value = [...hospitalShouldShow];
    return hospitalList.value.isNotEmpty;
  }

  Future<void> getConsentforms() async {
    try {
      final consents = await ConsentService.getConsents();
      consentforms.value = consents;
    } catch (_) {
      rethrow;
    }
  }

  Future<void> getConsentFormByHospital(String hospitalCode) async {
    try {
      final consents = await ConsentService.getConsentFormByHospital(
        hospitalCode,
      );
      consentforms.value = consents;
    } catch (_) {
      rethrow;
    }
  }

  Future<void> setConsent({required bool isUpdate}) async {
    if (isUpdate == true && selectedHospital.value == null) return;

    try {
      await ConsentService.setConsents(
        consentforms.value,
        isUpdate ? [selectedHospital.value!] : acceptedHospital.value,
      );
    } catch (_) {
      rethrow;
    }
  }

  Future<void> getHospitalList() async {
    try {
      if (hospitalList.value.isNotEmpty) return;
      final resp = await ConsentService.getConsentHospital();
      hospitalList.value = resp.consentHospital;
      needShow.value = resp.needShow;
    } catch (e) {
      if (e is DioException) {
        reportToZendesk(
            "Summary: Error when check-show\nDetail: ${e.response!.data ?? e.response}");
      } else {
        reportToZendesk(
            "Summary: Error when check-show\nDetail: ${e.toString()}");
      }
      rethrow;
    }
  }

  Future<void> reportToZendesk(String msg) async {
    try {
      UserApp userApp = Get.find<UserApp>();
      final Map<String, dynamic> body = {
        'title': "Error with connect hn from <${userApp.username.value}>",
        'detail': msg,
      };

      await _client.post(
        ApiConstant.totalHealthCreateZendesk,
        data: body,
      );
    } catch (e) {
      return;
    }
  }
}
