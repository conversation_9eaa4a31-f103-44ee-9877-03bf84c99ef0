// ignore_for_file: no_leading_underscores_for_local_identifiers

import 'dart:async';
import 'dart:convert';

import 'package:amplify_auth_cognito/amplify_auth_cognito.dart';
import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:app_badge_plus/app_badge_plus.dart';
import 'package:bcrypt/bcrypt.dart';
import 'package:dio/dio.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:samitivej_flutter_app/apps/common/common_app.dart';
import 'package:samitivej_flutter_app/apps/home/<USER>';
import 'package:samitivej_flutter_app/components/custom_alert_dialog.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/constants/app_constant.dart';
import 'package:samitivej_flutter_app/localization/app_translate_key.dart';
import 'package:samitivej_flutter_app/models/login_method_enum.dart';
import 'package:samitivej_flutter_app/models/migrate_plus_response.dart';
import 'package:samitivej_flutter_app/models/password_salt_response.dart';
import 'package:samitivej_flutter_app/models/pin_confirmation_response.dart';
import 'package:samitivej_flutter_app/models/plus_confirm_otp_response.dart';
import 'package:samitivej_flutter_app/models/plus_otp_response.dart';
import 'package:samitivej_flutter_app/models/response_model.dart';
import 'package:samitivej_flutter_app/models/validation_model.dart';
import 'package:samitivej_flutter_app/screen/auth/phone_number_verification_screen.dart';
import 'package:samitivej_flutter_app/screen/auth/term_and_condition_modal.dart';
import 'package:samitivej_flutter_app/services/amplify_service.dart';
import 'package:samitivej_flutter_app/services/api_client.dart';
import 'package:samitivej_flutter_app/services/biometric_service.dart';
import 'package:samitivej_flutter_app/services/notification_service.dart';
import 'package:samitivej_flutter_app/services/storage_service.dart';
import 'package:samitivej_flutter_app/utils/validation_helper.dart';

class AuthApp extends GetxController {
  final ApiClient _client = ApiClient();
  final StorageService _storage = StorageService();
  final BiometricService _biometricService = BiometricService();

  RxBool haveAuthenticated = RxBool(false);

  Rx<ValidationModel> fName = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> lName = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> username = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> email = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> countryCode = Rx(ValidationModel(value: '+66'));
  Rx<ValidationModel> phoneNumber = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> password = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> confirmPassword = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> currentPin = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> pin = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> confirmPin = Rx(ValidationModel(value: ''));
  RxString forgotCode = ''.obs;
  RxBool pdpaAcceptation = false.obs;
  RxBool pdpaError = false.obs;
  RxBool readTerms = false.obs;
  RxBool isFailed = false.obs;

  Rx<ValidationModel> phoneOTPCode = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> emailOTPCode = Rx(ValidationModel(value: ''));
  RxInt otpCountFrom = 60.obs;

  RxBool isPasswordVisible = false.obs;

  RxBool isSignUpPasswordVisible = false.obs;
  RxBool isSignUpConfirmPasswordVisible = false.obs;
  RxBool userLoginWithSocial = false.obs;

  Rx<LoginMethodEnum> loginMethod = LoginMethodEnum.username.obs;

  String? accessToken;
  String? refreshToken;

  PinConfirmationResponse? totalhealthToken;

  late String apiToken;
  RxString cognitoIdToken = ''.obs;

  RxBool isLoading = false.obs;
  RxString errorMessage = ''.obs;

  RxList<String> acceptingDataSharingWithHospitalBranch = RxList.empty();
  RxString selectedPrivacyHospitalBranch = ''.obs;

  RxnBool privacyNo1 = RxnBool();
  RxnBool privacyNo2_1 = RxnBool();
  RxnBool privacyNo2_2 = RxnBool();
  RxnBool privacyNo2_3 = RxnBool();

  // TODO: Add another currentPinCode variable for settingsPinCode changing stuff
  RxList<int> currentPinCode =
      RxList.from(<int>[10, 10, 10, 10, 10, 10], growable: false);
  RxList<int> pinCode =
      RxList.from(<int>[10, 10, 10, 10, 10, 10], growable: false);
  RxList<int> confirmPinCode =
      RxList.from(<int>[10, 10, 10, 10, 10, 10], growable: false);

  RxnBool isHavePin = RxnBool();
  Rx<Duration> otpCountdown = Rx(const Duration(seconds: 120));

  bool validatePinCode() {
    for (int i = 0; i < pinCode.length; i++) {
      if (pinCode[i] == 10) {
        return false;
      }
    }
    return true;
  }

  bool validateCurrentPinCode() {
    for (int i = 0; i < currentPinCode.length; i++) {
      if (currentPinCode[i] == 10) {
        return false;
      }
    }
    return true;
  }

  bool pinHasData(int index) {
    assert(index < 6);
    if (pinCode[index] < 10) {
      return true;
    }
    return false;
  }

  bool currentPinHasData(int index) {
    assert(index < 6);
    if (currentPinCode[index] < 10) {
      return true;
    }
    return false;
  }

  void deletePin() {
    for (int i = pinCode.length - 1; i >= 0; i--) {
      if (pinCode[i] != 10) {
        pinCode[i] = 10;
        break;
      }
    }
  }

  void deleteCurrentPin() {
    for (int i = currentPinCode.length - 1; i >= 0; i--) {
      if (currentPinCode[i] != 10) {
        currentPinCode[i] = 10;
        break;
      }
    }
  }

  void clearPin() {
    for (int i = pinCode.length - 1; i >= 0; i--) {
      pinCode[i] = 10;
    }
  }

  void clearCurrentPin() {
    for (int i = currentPinCode.length - 1; i >= 0; i--) {
      currentPinCode[i] = 10;
    }
  }

  void addPin(int value) {
    for (int i = 0; i < pinCode.length; i++) {
      if (pinCode[i] == 10) {
        pinCode[i] = value;
        break;
      }
    }
  }

  void addCurrentPin(int value) {
    for (int i = 0; i < currentPinCode.length; i++) {
      if (currentPinCode[i] == 10) {
        currentPinCode[i] = value;
        break;
      }
    }
  }

  bool validateConfirmPinCode() {
    for (int i = 0; i < confirmPinCode.length; i++) {
      if (confirmPinCode[i] == 10) {
        return false;
      }
    }
    return true;
  }

  bool confirmPinHasData(int index) {
    assert(index < 6);
    if (confirmPinCode[index] < 10) {
      return true;
    }
    return false;
  }

  void deleteConfirmPin() {
    for (int i = confirmPinCode.length - 1; i >= 0; i--) {
      if (confirmPinCode[i] != 10) {
        confirmPinCode[i] = 10;
        break;
      }
    }
  }

  void addConfirmPin(int value) {
    for (int i = 0; i < confirmPinCode.length; i++) {
      if (confirmPinCode[i] == 10) {
        confirmPinCode[i] = value;
        break;
      }
    }
  }

  void clearConfirmPin() {
    for (int i = 0; i < confirmPinCode.length; i++) {
      confirmPinCode[i] = 10;
    }
  }

  Future<void> clearConfirmPinCountdown() async {
    int i = confirmPinCode.length - 1;
    confirmPinCode[i] = 10;
    for (int i = confirmPinCode.length - 1; i >= 0; i--) {
      await Future.delayed(const Duration(milliseconds: 50), () {
        confirmPinCode[i] = 10;
      });
    }
  }

  Future<void> savePinCodeToTotalHealth(String pinCode) async {
    try {
      final Map<String, dynamic> body = {
        "pinCode": pinCode,
      };

      await _client.post(
        ApiConstant.totalHealthCreatePin,
        data: body,
      );
    } catch (e) {
      debugPrint("An error occur");
      rethrow;
    }
  }

  Future<bool> verifyPinCode(
      {required String inputPinCode, required bool bio}) async {
    try {
      final Map<String, dynamic> body = {
        "pinCode": inputPinCode,
        "deviceId": await NotificationService.getDeviceId() ?? "",
        "bio": bio
      };

      final ResponseModel response = await _client.post(
        ApiConstant.totalHealthCheckPin,
        data: body,
      );

      totalhealthToken = PinConfirmationResponse.fromJson(response.data!);

      return totalhealthToken!.success && totalhealthToken!.hasToken;
    } catch (e) {
      if (e is DioException) {
        final body = e.response?.data as Map<String, dynamic>;
        totalhealthToken = PinConfirmationResponse.fromJson(body);
      } else {
        totalhealthToken =
            PinConfirmationResponse(success: false, message: "unknown error");
      }
      return false;
    }
  }

  Future<bool> registerDeviceBio() async {
    try {
      final Map<String, dynamic> body = {
        "deviceId": await NotificationService.getDeviceId() ?? "",
      };

      await _client.post(
        ApiConstant.totalHealthRegisterDeviceBio,
        data: body,
      );

      return true;
    } catch (_) {
      return false;
    }
  }

  Future<void> revokePinToken() async {
    try {
      final Map<String, dynamic> body = {
        "deviceId": await NotificationService.getDeviceId() ?? "",
      };

      await _client.post(
        ApiConstant.totalHealthRevokePinToken,
        data: body,
      );

      return;
    } catch (_) {
      return;
    }
  }

  Future<void> updatePinCodeToTotalHealth(
    String oldPinCode,
    String newPinCode,
  ) async {
    try {
      final Map<String, dynamic> body = {
        "oldPinCode": oldPinCode,
        "newPinCode": newPinCode,
      };

      await _client.post(
        ApiConstant.totalHealthUpdatePin,
        data: body,
      );
    } catch (e) {
      debugPrint("An error occur");
      rethrow;
    }
  }

  Future<void> resetPinCodeToTotalHealth() async {
    try {
      await _client.post(
        ApiConstant.totalHealthResetPin,
      );
    } catch (e) {
      debugPrint("An error occur");
      rethrow;
    }
  }

  bool hasPinCode() {
    // Replace check from total health
    return isHavePin.value ?? false;
  }

  Future<void> removePinCode() async {
    await _storage.remove(AppConstant.pinCodeKey);
  }

  void resetPin() {
    pinCode = RxList.from(<int>[10, 10, 10, 10, 10, 10]);
  }

  void resetCurrentPin() {
    currentPinCode = RxList.from(<int>[10, 10, 10, 10, 10, 10]);
  }

  void resetPinCode() {
    pinCode = RxList.from(<int>[10, 10, 10, 10, 10, 10]);
    confirmPinCode = RxList.from(<int>[10, 10, 10, 10, 10, 10]);
  }

  void resetLoginForm() {
    email.value = ValidationModel(value: '');
    password.value = ValidationModel(value: '');
    phoneNumber.value = ValidationModel(value: '');
    isPasswordVisible.value = false;
  }

  void resetSignUpForm() {
    username.value = ValidationModel(value: '');
    email.value = ValidationModel(value: '');
    password.value = ValidationModel(value: '');
    confirmPassword.value = ValidationModel(value: '');
    pdpaAcceptation = false.obs;
    readTerms = false.obs;
    setPDPAError(false);
  }

  void resetEmailOTP() {
    emailOTPCode = Rx(ValidationModel(value: ''));
  }

  void toggleLoading() {
    isLoading.value = !isLoading.value;
  }

  void setLoading(bool value) {
    isLoading.value = value;
  }

  void acceptPdpa() {
    pdpaAcceptation.value = true;
  }

  String get formattedPhoneNumber {
    String _phoneNumber = phoneNumber.value.value!;
    if (_phoneNumber.startsWith('0')) {
      _phoneNumber = _phoneNumber.substring(1);
    }
    return '${countryCode.value.value}$_phoneNumber';
  }

  bool validateSignUpForm() {
    changeEmail(email.value.value!);
    changePassword(password.value.value!);
    changeConfirmPassword(confirmPassword.value.value!);

    if (pdpaAcceptation.value == false) {
      pdpaError.value = true;
    }

    return isSignUpFormValid();
  }

  bool isSignUpFormValid() {
    if (email.value.isError ||
        password.value.isError ||
        confirmPassword.value.isError ||
        password.value.value != confirmPassword.value.value ||
        pdpaAcceptation.value == false) {
      return false;
    } else {
      return true;
    }
  }

  bool validateResetPasswordForm() {
    final ValidationModel passwordValidated =
        ValidationHelper.isNotEmptyValidate(password.value.value!);
    final ValidationModel confirmPasswordValidated =
        ValidationHelper.isNotEmptyValidate(confirmPassword.value.value!);

    if (passwordValidated.isError ||
        confirmPasswordValidated.isError ||
        passwordValidated.value != confirmPasswordValidated.value) {
      return false;
    } else {
      return true;
    }
  }

  bool validateFirstForm() {
    fName.value = ValidationHelper.isNotEmptyValidate(fName.value.value);
    lName.value = ValidationHelper.isNotEmptyValidate(lName.value.value);
    email.value = ValidationHelper.isNotEmptyValidate(email.value.value);
    password.value = ValidationHelper.isNotEmptyValidate(password.value.value);
    confirmPassword.value =
        ValidationHelper.isNotEmptyValidate(confirmPassword.value.value);

    if (fName.value.isError ||
        lName.value.isError ||
        email.value.isError ||
        password.value.isError ||
        confirmPassword.value.isError) {
      return false;
    } else {
      return true;
    }
  }

  bool validatePhoneForm() {
    countryCode.value =
        ValidationHelper.isNotEmptyValidate(countryCode.value.value);
    changePhoneNumber(phoneNumber.value.value ?? "");
    if (countryCode.value.isError || phoneNumber.value.isError) {
      return false;
    } else {
      return true;
    }
  }

  bool validatePhoneOTPForm() {
    phoneOTPCode.value =
        ValidationHelper.isNotEmptyValidate(phoneOTPCode.value.value);
    if (phoneOTPCode.value.isError) {
      return false;
    } else {
      return true;
    }
  }

  bool validateEmailOTPForm() {
    emailOTPCode = ValidationHelper.otpValidate(emailOTPCode.value.value!).obs;
    if (emailOTPCode.value.isError) {
      return false;
    } else {
      return true;
    }
  }

  bool validateForgotPasswordForm() {
    email.value = ValidationHelper.emailValidate(email.value.value);
    if (email.value.isError) {
      return false;
    } else {
      return true;
    }
  }

  bool validateSignInForm() {
    email.value = ValidationHelper.emailValidate(email.value.value);
    password.value = ValidationHelper.isNotEmptyValidate(password.value.value!);
    if (email.value.isError || password.value.isError) {
      return false;
    } else {
      return true;
    }
  }

  Stream<int> countDownOTPConfirm(int countFrom) {
    return Stream<int>.periodic(const Duration(seconds: 1), (x) {
      if (countFrom - x >= 0) {
        otpCountFrom.value = countFrom - x;
      }
      return otpCountFrom.value;
    });
  }

  Stream<int> counter1(int maxCount) {
    otpCountFrom.value = maxCount;
    StreamController<int> countOTPStream = StreamController<int>();
    Timer.periodic(const Duration(seconds: 1), (Timer timer) {
      if (maxCount - timer.tick >= 0) {
        otpCountFrom.value = maxCount - timer.tick;
        countOTPStream.add(otpCountFrom.value);
      }
      if (otpCountFrom.value == 0) {
        timer.cancel();
        countOTPStream.close();
      }
    });

    return countOTPStream.stream;
  }

  void changeFName(String value) {
    fName.value = ValidationHelper.isNotEmptyValidate(value);
  }

  void changeLName(String value) {
    lName.value = ValidationHelper.isNotEmptyValidate(value);
  }

  void changeUsername(String value) {
    username.value = ValidationHelper.nameValidate(value);
  }

  void changeLoginEmail(String value) {
    email.value = ValidationHelper.emailValidate(value);
  }

  void changeEmail(String value) {
    email.value = ValidationHelper.registerEmailValidate(value);
  }

  void changeCountryCode(String value) {
    countryCode.value = ValidationHelper.isNotEmptyValidate(value);
  }

  void changePhoneNumber(String value) {
    phoneNumber.value = ValidationHelper.phoneValidate(value.split("-").join());
  }

  void changeLoginPassword(String value) {
    password.value = ValidationHelper.isNotEmptyValidate(value);
  }

  void changePassword(String value) {
    password.value = ValidationHelper.passwordValidate(value.trim());
    revalidateConfirmPassword(confirmPassword.value.value!);
  }

  void revalidateConfirmPassword(String value) {
    if (confirmPassword.value.isDirty == true) {
      confirmPassword.value = ValidationHelper.confirmPasswordValidate(
        password.value.value!,
        value.trim(),
      );
    }
  }

  void changeConfirmPassword(String value) {
    confirmPassword.value = ValidationHelper.confirmPasswordValidate(
      password.value.value!,
      value.trim(),
    );
  }

  void setPDPAError(bool value) {
    pdpaError.value = value;
  }

  void changeConfirmPin(String value) {
    confirmPin.value = ValidationHelper.confirmPinValidate(
      pin.value.value!,
      value,
    );
  }

  void changePhoneOTP(String value) {
    phoneOTPCode.value =
        ValidationHelper.isNotEmptyValidate(phoneOTPCode.value.value);
  }

  void changeEmailOTP(String value) {
    emailOTPCode.value = ValidationHelper.otpValidate(value);
  }

  Future<void> saveToken({
    required String accessTokenString,
    required String refreshTokenString,
    required String idToken,
  }) async {
    try {
      accessToken = accessTokenString;
      refreshToken = refreshTokenString;
      cognitoIdToken.value = idToken;
      await Future.wait([
        _storage.write(AppConstant.accessTokenKey, accessToken),
        _storage.write(AppConstant.refreshTokenKey, refreshToken),
        _storage.write(AppConstant.idTokenKey, cognitoIdToken.value),
      ]);
    } on AuthException catch (e) {
      errorMessage.value = e.message;
    }
  }

  Future<void> removeToken() async {
    try {
      accessToken = '';
      refreshToken = '';
      cognitoIdToken.value = '';
      await _storage.remove(AppConstant.accessTokenKey);
      await _storage.remove(AppConstant.refreshTokenKey);
    } on AuthException catch (e) {
      errorMessage.value = e.message;
    }
  }

  Future<bool> signUp() async {
    final String _email = email.value.value!.trim();
    final String _password = password.value.value!.trim();

    try {
      await AmplifyService.signUpWithEmail(
        _email,
        _password,
        _email,
      );
      return true;
    } on AuthException catch (e) {
      errorMessage.value = e.message;
      await AmplifyService.resendConfirmSignUpCode(username: _email);
      return true;
    }
  }

  Future<void> generateEmailPassword(String phoneNumber) async {
    final String email = 'well_$<EMAIL>';
    try {
      final getSaltResult = await getPasswordSaltOfPhoneAuth(phoneNumber);
      final hash = BCrypt.hashpw(
          'Well$phoneNumber', r'$2b$10$' + getSaltResult.salt);

      final String password = hash.replaceFirst(r'$2b$10$', '');
      this.email.value = ValidationModel(value: email);
      username.value = ValidationModel(value: phoneNumber);
      this.password.value = ValidationModel(value: password);
    } catch (e) {
      debugPrint("An error occur: $e");
      throw AuthException.fromException(
          Exception('ERR-101 : Generate not success'));
    }
  }

  Future<void> signUpWithPhoneNumber() async {
    try {
      await generateEmailPassword(formattedPhoneNumber);
      SignUpResult signUpResult = await AmplifyService.signUpWithPhoneNumber(
        fullPhoneNumber: formattedPhoneNumber,
        email: email.value.value!,
        username: email.value.value!,
        password: password.value.value!,
      );
      //Check next step to confirm phone number
      if (signUpResult.nextStep.signUpStep == AuthSignUpStep.confirmSignUp) {
        setLoading(false);
        await Get.to<void>(PhoneNumberVerificationScreen(
          verifyMode: PhoneVerificationMode.signUp,
          phoneNumber: formattedPhoneNumber,
          username: email.value.value!,
        ));
      }
    } on AuthException catch (e) {
      errorMessage.value = e.message;
      if (e is UsernameExistsException) {
        signInWithPhoneNumber();
      }
    }
  }

  Future<bool> signUpWithUsername() async {
    try {
      final String _username = fName.value.value!.trim();
      final String _password = password.value.value!.trim();
      final String _email = email.value.value!.trim();
      return await AmplifyService.signUpWithEmail(
        _username,
        _password,
        _email,
      );
    } on AuthException catch (e) {
      errorMessage.value = e.message;
      return false;
    }
  }

  Options otpOptionAPI() {
    return Options(
      headers: {
        'content-type': 'application/json; charset=utf-8',
        'Authorization': "Bearer $apiToken",
      },
    );
  }

  String formatPlusPhoneNumber(String phoneNumber) {
    final format = phoneNumber.split("-").join();
    if (format.startsWith("0")) {
      return format.substring(1);
    } else {
      return format;
    }
  }

  List<PlusOTPRequest> otpRequested = List.empty(growable: true);

  Future<PlusOTPResponse> requestPlusOTP(
      {required String countryCode,
      required String phoneNumber,
      bool useCache = false}) async {
    if (useCache) {
      PlusOTPRequest? lastTimeOTP = otpRequested
          .firstWhereOrNull((element) => element.phonenumber == phoneNumber);
      if (lastTimeOTP != null) {
        int cacheAge =
            secondsBetween(lastTimeOTP.cache.issueAt, DateTime.now());
        if (cacheAge < 120) {
          return lastTimeOTP.cache;
        }
      }
    }
    try {
      final response = await _client.post(
        ApiConstant.sendOTP,
        data: {
          "countryCode": countryCode,
          "phoneNumber": formatPlusPhoneNumber(phoneNumber),
        },
      );
      final result = PlusOTPResponse.fromJson(response.data!);
      updateOTPRequest(phoneNumber, result);
      otpCountFrom.value = 60;
      update();
      return result;
    } on DioException catch (e) {
      errorMessage.value = e.message ?? e.type.name;
      rethrow;
    } catch (e) {
      errorMessage.value = "error";
      rethrow;
    }
  }

  int secondsBetween(DateTime from, DateTime to) {
    return to.difference(from).inSeconds;
  }

  void updateOTPRequest(String phoneNumber, PlusOTPResponse response) {
    PlusOTPRequest newRecord =
        PlusOTPRequest(phonenumber: phoneNumber, cache: response);
    int index = otpRequested
        .indexWhere((element) => element.phonenumber == phoneNumber);
    if (index == -1) {
      otpRequested.add(newRecord);
    } else {
      otpRequested[index] = newRecord;
    }
  }

  Future<void> resendEmailOTP({
    required String email,
  }) async {
    try {
      await AmplifyService.resendOTP(email);
    } catch (e) {
      debugPrint("An error occur");
    }
  }

  Future<bool> confirmPlusOTP({
    required String countryCode,
    required String phoneNumber,
    required String otp,
    required String ref,
  }) async {
    try {
      final ResponseModel response = await _client.post(
        ApiConstant.confirmOTP,
        data: {
          "countryCode": countryCode,
          "phoneNumber": formatPlusPhoneNumber(phoneNumber),
          "ref": ref,
          "otp": otp
        },
      );
      final result = PlusConfirmOTPResponse.fromJson(response.data!);
      if (result.verify) {
        otpRequested
            .removeWhere((element) => element.phonenumber == phoneNumber);
      }
      return result.verify;
    } on DioException {
      errorMessage.value = 'Phone Confirmation OTP is not correct.';
      rethrow;
    }
  }

  Future<bool> confirmEmailOTP(
    String email,
  ) async {
    try {
      final bool result = await AmplifyService.confirmOTP(
        email,
        emailOTPCode.value.value!,
      );
      return result;
    } on AmplifyException catch (e) {
      errorMessage.value = e.message;
      return false;
    }
  }

  Future<void> resendOtpSignUpPhoneVerification({
    required String username,
  }) async {
    try {
      await AmplifyService.resendConfirmSignUpCode(
        username: username,
      );
    } catch (e) {
      debugPrint("An error occur");
    }
  }

  Future<void> resendOtpSignInPhoneVerification({
    required String username,
  }) async {
    try {
      await AmplifyService.resendMfaSignInCode(
        username: username,
        password: password.value.value.toString(),
      );
    } catch (e) {
      debugPrint("An error occur");
    }
  }

  Future<bool> confirmSignUpPhoneVerification({
    required String username,
    required String otpCode,
  }) async {
    try {
      final result = await Amplify.Auth.confirmSignUp(
        username: username,
        confirmationCode: otpCode,
      );
      return result.isSignUpComplete &&
          result.nextStep.signUpStep == AuthSignUpStep.done;
    } on AmplifyException catch (e) {
      errorMessage.value = e.message;
      return false;
    }
  }

  Future<bool> confirmSignInPhoneVerification({
    required String otpCode,
  }) async {
    try {
      final result = await Amplify.Auth.confirmSignIn(
        confirmationValue: otpCode,
      );
      return result.isSignedIn /* && result.nextStep?.signInStep == 'DONE'*/;
    } on AmplifyException catch (e) {
      errorMessage.value = e.message;
      return false;
    }
  }

  Future<bool> signInWithEmail() async {
    try {
      final bool signInResult = await AmplifyService.signInWithEmail(
        email.value.value!,
        password.value.value!,
      );
      if (signInResult) {
        await setupCognitoIdToken();
      }
      await FirebaseAnalytics.instance.logLogin(loginMethod: 'Email');
      return signInResult;
    } on AmplifyException catch (e) {
      debugPrint("An error occur");
      errorMessage.value = e.message;
      return false;
    }
  }

  Future<bool?> signInWithPhoneNumber() async {
    bool? isSignedIn;
    try {
      setLoading(true);
      await generateEmailPassword(formattedPhoneNumber);
      final signInResult = await AmplifyService.signInWithPhoneNumber(
          email.value.value.toString(), password.value.value.toString());
      if (signInResult.isSignedIn) {
        await setupCognitoIdToken();
        isSignedIn = signInResult.isSignedIn;
      } else if (signInResult.nextStep.signInStep ==
          AuthSignInStep.confirmSignInWithSmsMfaCode) {
        setLoading(false);
        final isConfirmSuccess =
            await Get.to<bool>(PhoneNumberVerificationScreen(
          verifyMode: PhoneVerificationMode.signIn,
          phoneNumber: formattedPhoneNumber,
          username: email.value.value!,
        ));
        isSignedIn = isConfirmSuccess;
      } else if (signInResult.nextStep.signInStep ==
          AuthSignInStep.confirmSignUp) {
        await resendOtpSignUpPhoneVerification(
          username: email.value.value.toString(),
        );
        setLoading(false);
        await Get.to<void>(PhoneNumberVerificationScreen(
          verifyMode: PhoneVerificationMode.signUp,
          phoneNumber: formattedPhoneNumber,
          username: email.value.value!,
        ));
      } else {
        setLoading(false);
        isSignedIn = false;
        //Show default error dialog
        await Get.dialog<void>(
          CustomAlertDialog(
              title: AppTranslateKey.somethingwentwrong,
              description: AppTranslateKey.pleasetryagainlater),
        );
        FirebaseCrashlytics.instance.recordError(
          Exception("Sign In with Phone Number exception"),
          StackTrace.current, // you should pass stackTrace in here
          reason: signInResult.toString(),
          fatal: false,
        );
      }
    } on AmplifyException catch (e) {
      errorMessage.value = e.message;
      if (e is UserNotConfirmedException) {
        await resendOtpSignUpPhoneVerification(
          username: email.value.value.toString(),
        );
        setLoading(false);
        await Get.to<void>(PhoneNumberVerificationScreen(
          verifyMode: PhoneVerificationMode.signUp,
          phoneNumber: formattedPhoneNumber,
          username: email.value.value!,
        ));
      } else if (e is UserNotFoundException) {
        pdpaAcceptation.value = false;
        readTerms.value = false;
        await Get.dialog<void>(
          PopScope(
            canPop: false,
            onPopInvoked: (disPop) async => false,
            child: const TermAndConditionModal(),
          ),
          barrierDismissible: false,
        );
        await signUpWithPhoneNumber();
      } else if (e.message.contains("WAF block")) {
        setLoading(false);
        isSignedIn = false;
        email.value = ValidationModel(value: '');
        await Get.dialog<void>(
          CustomAlertDialog(
              title: AppTranslateKey.loginfail,
              description: AppTranslateKey.warningVPN),
        );
        FirebaseCrashlytics.instance.recordError(
          e,
          StackTrace.current,
          reason: e.message,
          fatal: false,
        );
      } else {
        setLoading(false);
        isSignedIn = false;
        email.value = ValidationModel(value: '');
        await Get.dialog<void>(
          CustomAlertDialog(
              title: AppTranslateKey.somethingwentwrong,
              description: AppTranslateKey.pleasetryagainlater),
        );
        FirebaseCrashlytics.instance.recordError(
          e,
          StackTrace.current,
          reason: e.message,
          fatal: false,
        );
      }
    }
    await FirebaseAnalytics.instance.logLogin(loginMethod: 'Phone');
    return isSignedIn;
  }

  Future<bool> signInWithFacebook() async {
    try {
      final bool signInResult = await AmplifyService.signInWithFacebook();
      if (signInResult) {
        await setupCognitoIdToken();
      }
      await FirebaseAnalytics.instance.logLogin(loginMethod: 'Facebook');
      return signInResult;
    } on AmplifyException catch (e) {
      errorMessage.value = e.message;
      return false;
    }
  }

  Future<bool> signInWithGoogle() async {
    try {
      final bool signInResult = await AmplifyService.signInWithGoogle();
      if (signInResult) {
        await setupCognitoIdToken();
      }
      await FirebaseAnalytics.instance.logLogin(loginMethod: 'Google');
      return signInResult;
    } on AmplifyException catch (e) {
      errorMessage.value = e.message;
      return false;
    }
  }

  Future<bool> signInWithApple() async {
    try {
      final bool signInResult = await AmplifyService.signInWithApple();
      if (signInResult) {
        await setupCognitoIdToken();
      }
      await FirebaseAnalytics.instance.logLogin(loginMethod: 'Apple');
      return signInResult;
    } on AmplifyException catch (e) {
      errorMessage.value = e.message;
      return false;
    }
  }

  Future<void> setupCognitoIdToken() async {
    try {
      final Map<String, String> token = await AmplifyService.fetchToken();
      await saveToken(
        accessTokenString: token['accessToken']!,
        refreshTokenString: token['refreshToken']!,
        idToken: token['idToken']!,
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<void> ensureCognitoIdToken() async {
    try {
      if (tokenNotValid) {
        await setupCognitoIdToken();
        return;
      }
    } catch (e) {
      rethrow;
    }
  }

  bool get tokenNotValid =>
      cognitoIdToken.value.isEmpty || isJwtExpired(cognitoIdToken.value);

  bool get tokenValid => !tokenNotValid;

  bool isJwtExpired(String jwt) {
    // Split the JWT into 3 parts, separated by the '.' character
    List<String> jwtParts = jwt.split('.');
    // Decode the second part of the JWT (the payload) as JSON
    Map<String, dynamic> jwtPayload = json.decode(
            utf8.decode(base64Url.decode(base64Url.normalize(jwtParts[1]))))
        as Map<String, dynamic>;
    // Check if the 'exp' (expiration time) claim is present in the JWT payload
    if (jwtPayload.containsKey('exp')) {
      // Get the expiration time as a Unix timestamp (the number of seconds since 1970)
      int expirationTime = jwtPayload['exp'] as int;

      // Get 12-hour earlier of the current time as a Unix timestamp
      int currentTime = (DateTime.now()
              .add(const Duration(hours: 12))
              .millisecondsSinceEpoch ~/
          1000);

      // Check if the JWT has expired
      return (currentTime > expirationTime);
    } else {
      // If the 'exp' claim is not present, the JWT does not have an expiration time
      return true;
    }
  }

  Future<bool> authenticated() async {
    try {
      final bool isAuthenticated = await AmplifyService.authenticated();
      if (isAuthenticated) {
        accessToken = _storage.read<String>('accessToken');
        refreshToken = _storage.read<String>('refreshToken');
      }
      haveAuthenticated.value = isAuthenticated;
      return isAuthenticated;
    } on AmplifyException catch (e) {
      errorMessage.value = e.message;
      return false;
    }
  }

  Future<bool> signOut({
    bool globalSignOut = false,
  }) async {
    try {
      isLoading.value = true;
      CommonApp commonApp = Get.find<CommonApp>();
      final bool signOutResult = await AmplifyService.signOut(
        globalSignOut: globalSignOut,
      );

      await Future.wait([
        NotificationService.unsetNotificationTokenwithCognito(
            cognitoIdToken.value),
        AppBadgePlus.updateBadge(0),
      ]).catchError((e) {
        if (kDebugMode) print("Error notification");
        return List.empty();
      });

      await Future.wait([
        revokePinToken(),
        removePinCode(),
        _biometricService.savePreference(false),
        HomeApp.postponeConnectHnBar(snoozeFor: const Duration(days: 0))
      ]);
      commonApp.onTapNavBarItem(0);
      if (signOutResult) {
        await removeToken();
      }
      haveAuthenticated.value = false;
      isLoading.value = false;
      return signOutResult;
    } on AmplifyException catch (e) {
      errorMessage.value = e.message;
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> updatePassword(
    String oldPassword,
    String newPassword,
  ) async {
    try {
      await AmplifyService.updatePassword(
        oldPassword,
        newPassword,
      );
    } on AmplifyException catch (e) {
      errorMessage.value = e.message;
    }
  }

  Future<bool> validateUpdatePassword(
    String oldPassword,
    String newPassword,
  ) async {
    try {
      await AmplifyService.updatePassword(
        oldPassword,
        newPassword,
      );
      return true;
    } on AmplifyException catch (e) {
      errorMessage.value = e.message;
      return false;
    }
  }

  Future<bool> requestResetPassword(
    String username,
  ) async {
    try {
      await AmplifyService.requestResetPassword(
        username,
      );
      return true;
    } on AmplifyException catch (e) {
      errorMessage.value = e.message;
      // print('====> $e');
      rethrow;
    } catch (e) {
      // print('====> $e');
      return false;
    }
  }

  Future<void> confirmResetPassword(
    String username,
    String newPassword,
    String code,
  ) async {
    try {
      await AmplifyService.confirmResetPassword(
        username,
        newPassword,
        code,
      );
    } on AmplifyException catch (e) {
      errorMessage.value = e.message;
    }
  }

  bool validatePrivacy() {
    /// TODO: validate data
    /// - hospital branch should select al least one branch
    /// - answer all privacy question
    return (privacyNo1.value != null) &&
        (privacyNo2_1.value != null) &&
        (privacyNo2_2.value != null) &&
        (privacyNo2_3.value != null) &&
        acceptingDataSharingWithHospitalBranch.isNotEmpty;
  }

  Future<MigratePlusResponse> checkMigrateUserFromPlus() async {
    try {
      final response = await _client.post(
        ApiConstant.checkMigrateUserPlus,
      );
      final result = MigratePlusResponse.fromJson(response.data!);
      return result;
    } on DioException catch (e) {
      errorMessage.value = e.message ?? e.type.name;
      return MigratePlusResponse(status: "error");
    } catch (e) {
      errorMessage.value = "error";
      rethrow;
    }
  }

  Future<PlusOTPResponse> requestMigrateUserFromPlus() async {
    try {
      final response = await _client.post(
        ApiConstant.requestMigrateUserPlus,
      );
      final result = PlusOTPResponse.fromJson(response.data!);
      return result;
    } on DioException catch (e) {
      errorMessage.value = e.message ?? e.type.name;
      rethrow;
    } catch (e) {
      errorMessage.value = "error";
      rethrow;
    }
  }

  Future<MigratePlusResponse> confirmMigrateUserFromPlus({
    required String otp,
    required String ref,
  }) async {
    try {
      final response = await _client.post(
        ApiConstant.confirmMigrateUserPlus,
        data: {
          "otp": otp,
          "ref": ref,
        },
      );
      final result = MigratePlusResponse.fromJson(response.data!);
      return result;
    } on DioException catch (e) {
      errorMessage.value = e.message ?? e.type.name;
      rethrow;
    } catch (e) {
      errorMessage.value = "error";
      rethrow;
    }
  }

  Future<void> setActiveMultiFactorAuth() async {
    try {
      Map<String, dynamic>? headers = {"accessToken": accessToken};
      await _client.post(
        ApiConstant.totalHealthAuthActiveMfa,
        options: Options(
          headers: headers,
        ),
      );
    } catch (e) {
      debugPrint("An error occur: $e");
    }
  }

  ///Get salt for password generate in phone number authentication
  /// [phoneNumber] String like '**********'
  Future<PasswordSaltResponse> getPasswordSaltOfPhoneAuth(
      String phoneNumber) async {
    try {
      Map<String, dynamic> headers = {"data": phoneNumber};
      final response = await _client.post(
        ApiConstant.totalHealthPasswordSalt,
        options: Options(
          headers: headers,
        ),
      );
      final result = PasswordSaltResponse.fromJson(response.data!);
      return result;
    } catch (e) {
      debugPrint("An error occur: $e");
      rethrow;
    }
  }
}

class PlusOTPRequest {
  final String phonenumber;
  final PlusOTPResponse cache;

  PlusOTPRequest({
    required this.phonenumber,
    required this.cache,
  });
}
