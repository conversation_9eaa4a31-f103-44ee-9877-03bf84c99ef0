import 'package:get/get.dart';
import 'package:samitivej_flutter_app/models/chat_message_model.dart';
import 'package:samitivej_flutter_app/screen/chat/components/chat_message_cell.dart';

class ChatApp extends GetxController {
  ChatApp({required this.room});

  final String room;

  final Rx<List<ChatMessageModel>> messages = Rx([]);

  void createMessage(
    String message,
    ChatMessageCellType type,
  ) {
    final newMessage = ChatMessageModel(
      message: message,
      type: type,
      isUser: true,
    );

    messages.update((e) => e!.add(newMessage));
  }
}
