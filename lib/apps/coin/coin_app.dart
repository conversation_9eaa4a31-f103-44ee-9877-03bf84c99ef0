import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:samitivej_flutter_app/apps/user/user_app.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/models/coin_record_model.dart';
import 'package:samitivej_flutter_app/models/coin_status_model.dart';
import 'package:samitivej_flutter_app/models/response_model.dart';
import 'package:samitivej_flutter_app/services/api_client.dart';

class CoinApp extends GetxController {
  /// API client for the fit services.
  final ApiClient _client = ApiClient(baseUrl: ApiConstant.homeAppDomain);

  /// Whether loading total coins.
  RxBool isGetTotalCoinsLoading = false.obs;

  /// Total coins.
  Rx<int?> totalCoins = Rx(0);

  /// Whether the loading of status of daily coin of the current user is in progress or not.
  RxBool isGetDailyCoinStatusLoading = false.obs;

  /// The daily coin status list of the current user.
  Rx<CoinStatusModel> dailyCoinStatus = Rx(CoinStatusModel());

  /// Whether the loading of coin history of the current user is in progress or not.
  RxBool isGetCoinHistoryLoading = false.obs;

  /// The current page of the coin history loaded.
  int _currentCoinHistoryPage = 0;

  /// Whether the last page of coin history has been reached or not.
  RxBool isCoinHistoryLastPageReached = false.obs;

  /// The number of coin record loaded at a time.
  final _coinHistoryPageSize = 7;

  /// The coin record list of the current user.
  Rx<List<CoinRecordModel>> coinRecords = Rx([]);

  /// Whether the loading of used coin history of the current user is in progress or not.
  RxBool isGetUsedCoinHistoryLoading = false.obs;

  /// The current page of the used coin history loaded.
  int _currentUsedCoinHistoryPage = 0;

  /// Whether the last page of used coin history has been reached or not.
  RxBool isUsedCoinHistoryLastPageReached = false.obs;

  /// The number of used coin record loaded at a time.
  final _usedCoinHistoryPageSize = 7;

  /// The used coin record list of the current user.
  Rx<List<CoinRecordModel>> usedCoinRecords = Rx([]);

  /// Whether the loading of earned coin history of the current user is in progress or not.
  RxBool isGetEarnedCoinHistoryLoading = false.obs;

  /// The current page of the earned coin history loaded.
  int _currentEarnedCoinHistoryPage = 0;

  /// Whether the last page of earned coin history has been reached or not.
  RxBool isEarnedCoinHistoryLastPageReached = false.obs;

  /// The number of earned coin record loaded at a time.
  final _earnedCoinHistoryPageSize = 7;

  /// The earned coin record list of the current user.
  Rx<List<CoinRecordModel>> earnedCoinRecords = Rx([]);

  /// Whether the loading of claiming daily coins of the current user is in progress or not.
  RxBool isClaimDailyCoinsLoading = false.obs;

  /// Gets total coins of the current user.
  Future<void> getTotalCoins() async {
    try {
      isGetTotalCoinsLoading.value = true;

      final ResponseModel response = await _client.get(
        ApiConstant.getTotalCoins,
      );
      totalCoins.value = (response.data?['data'] as int?) ?? 0;

      isGetTotalCoinsLoading.value = false;
    } on DioException catch (e) {
      reportToZendesk(e.toString());
      isGetTotalCoinsLoading.value = false;
    } finally {
      isGetTotalCoinsLoading.value = false;
    }
  }

  /// Gets daily coin status of the current user on [date].
  Future<void> getDailyCoinStatus() async {
    try {
      isGetDailyCoinStatusLoading.value = true;
      final ResponseModel response =
          await _client.get(ApiConstant.getDailyCoinStatus);
      dailyCoinStatus.value = CoinStatusModel.fromJson(response.data ?? {});
      isGetDailyCoinStatusLoading.value = false;
    } on DioException catch (e) {
      reportToZendesk(e.toString());
      isGetDailyCoinStatusLoading.value = false;
    }
  }

  /// Gets coin history of the current user on [date].
  /// If [reset] is true, it will start loading from page 1.
  Future<void> getCoinHistory({bool reset = false}) async {
    if (reset) {
      _currentCoinHistoryPage = 0;
      isCoinHistoryLastPageReached.value = false;
      coinRecords.value = [];
    }
    if (isCoinHistoryLastPageReached.value) {
      return;
    }
    try {
      isGetCoinHistoryLoading.value = true;
      final ResponseModel response = await _client.post(
        ApiConstant.getCoinHistory,
        data: {
          'page': ++_currentCoinHistoryPage,
          'pageSize': _coinHistoryPageSize,
        },
      );
      final records = (response.data?['listData'] as List)
          .map((task) => CoinRecordModel.fromJson(task as Map<String, dynamic>))
          .toList(growable: false);
      if (records.isNotEmpty) {
        if (reset) {
          coinRecords.value = records;
        } else {
          coinRecords.value = coinRecords.value + records;
        }
        isCoinHistoryLastPageReached.value =
            records.length < _coinHistoryPageSize;
      } else {
        isCoinHistoryLastPageReached.value = true;
      }

      isGetCoinHistoryLoading.value = false;
    } on DioException catch (e) {
      reportToZendesk(e.toString());
      coinRecords.value = [];
      isGetCoinHistoryLoading.value = false;
    } on Error catch (e) {
      reportToZendesk(e.toString());
      coinRecords.value = [];
      isGetCoinHistoryLoading.value = false;
    }
  }

  /// Gets used coin history of the current user on [date].
  /// If [reset] is true, it will start loading from page 1.
  Future<void> getUsedCoinHistory({bool reset = false}) async {
    if (reset) {
      _currentUsedCoinHistoryPage = 0;
      isUsedCoinHistoryLastPageReached.value = false;
      usedCoinRecords.value = [];
    }
    if (isUsedCoinHistoryLastPageReached.value) {
      return;
    }
    try {
      isGetUsedCoinHistoryLoading.value = true;
      final ResponseModel response = await _client.post(
        ApiConstant.getUsedCoinHistory,
        data: {
          'page': ++_currentUsedCoinHistoryPage,
          'pageSize': _usedCoinHistoryPageSize,
        },
      );
      final records = (response.data?['listData'] as List)
          .map((task) => CoinRecordModel.fromJson(task as Map<String, dynamic>))
          .toList(growable: false);
      if (records.isNotEmpty) {
        if (reset) {
          usedCoinRecords.value = records;
        } else {
          usedCoinRecords.value = usedCoinRecords.value + records;
        }
        isUsedCoinHistoryLastPageReached.value =
            records.length < _usedCoinHistoryPageSize;
      } else {
        isUsedCoinHistoryLastPageReached.value = true;
      }

      isGetUsedCoinHistoryLoading.value = false;
    } on DioException {
      usedCoinRecords.value = [];
      isGetUsedCoinHistoryLoading.value = false;
    } on Error {
      usedCoinRecords.value = [];
      isGetUsedCoinHistoryLoading.value = false;
    }
  }

  /// Gets earned coin history of the current user on [date].
  /// If [reset] is true, it will start loading from page 1.
  Future<void> getEarnedCoinHistory({bool reset = false}) async {
    if (reset) {
      _currentEarnedCoinHistoryPage = 0;
      isEarnedCoinHistoryLastPageReached.value = false;
      earnedCoinRecords.value = [];
    }
    if (isEarnedCoinHistoryLastPageReached.value) {
      return;
    }
    try {
      isGetEarnedCoinHistoryLoading.value = true;
      final ResponseModel response = await _client.post(
        ApiConstant.getEarnedCoinHistory,
        data: {
          'page': ++_currentEarnedCoinHistoryPage,
          'pageSize': _earnedCoinHistoryPageSize,
        },
      );
      final records = (response.data?['listData'] as List)
          .map((task) => CoinRecordModel.fromJson(task as Map<String, dynamic>))
          .toList(growable: false);
      if (records.isNotEmpty) {
        if (reset) {
          earnedCoinRecords.value = records;
        } else {
          earnedCoinRecords.value = earnedCoinRecords.value + records;
        }
        isEarnedCoinHistoryLastPageReached.value =
            records.length < _earnedCoinHistoryPageSize;
      } else {
        isEarnedCoinHistoryLastPageReached.value = true;
      }

      isGetEarnedCoinHistoryLoading.value = false;
    } on DioException {
      earnedCoinRecords.value = [];
      isGetEarnedCoinHistoryLoading.value = false;
    } on Error {
      earnedCoinRecords.value = [];
      isGetEarnedCoinHistoryLoading.value = false;
    }
  }

  /// Claims daily coins.
  Future<void> claimDailyCoins() async {
    try {
      isClaimDailyCoinsLoading.value = true;
      final ResponseModel response =
          await _client.post(ApiConstant.claimDailyCoins);
      dailyCoinStatus.value = CoinStatusModel.fromJson(response.data ?? {});

      // Reload total coins and coin history.
      await getTotalCoins();
      await getCoinHistory(reset: true);
      await getEarnedCoinHistory(reset: true);
      await getUsedCoinHistory(reset: true);

      isClaimDailyCoinsLoading.value = false;
    } on DioException catch (e) {
      reportToZendesk(e.toString());
      isClaimDailyCoinsLoading.value = false;
    }
  }

  Future<void> reportToZendesk(String msg) async {
    try {
      final ApiClient client = ApiClient();
      UserApp userApp = Get.find<UserApp>();
      final Map<String, dynamic> body = {
        'title': "Error with coin from <${userApp.username.value}>",
        'detail': msg,
      };

      await client.post(
        ApiConstant.totalHealthCreateZendesk,
        data: body,
      );
    } catch (e) {
      return;
    }
  }
}
