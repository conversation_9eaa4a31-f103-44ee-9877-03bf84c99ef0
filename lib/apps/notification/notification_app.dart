import 'dart:async';
import 'dart:ui';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:app_badge_plus/app_badge_plus.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:samitivej_flutter_app/apps/common/common_app.dart';
import 'package:samitivej_flutter_app/apps/queue/queue_app.dart';
import 'package:samitivej_flutter_app/constants/app_asset.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/models/notification_model.dart';
import 'package:samitivej_flutter_app/screen/home/<USER>/queue_welcome_dialog.dart';
import 'package:samitivej_flutter_app/screen/queue/queue_feedback_screen.dart';
import 'package:samitivej_flutter_app/screen/stack_bar/home_stack_bar.dart';
import 'package:samitivej_flutter_app/services/notification_service.dart';
import 'package:samitivej_flutter_app/services/well_navigate_service.dart';

class NotificationApp extends GetxController {
  final Rx<List<NotificationModel>> notifications = Rx([]);
  final Rx<int> badge = Rx(0);
  final Rx<int> chat = Rx(0);
  final Rx<String> deviceId = Rx('');

  StreamSubscription<RemoteMessage>? backgroudStream;
  StreamSubscription<RemoteMessage>? inAppStream;

  static NotificationCellInfo getNotificationCellInfo(String? type) {
    if (type == null) {
      return NotificationCellInfo(
        icon: AppAsset.notiDefaultIcon,
        color: AppColor.notiDefaultColor,
      );
    }

    switch (type) {
      case 'food':
        return NotificationCellInfo(
          icon: AppAsset.notiFoodIcon,
          color: AppColor.notiFoodColor,
        );
      case 'fit':
      case 'burned cal goal':
      case 'distance goal':
      case 'morning fit':
      case 'evening fit':
        return NotificationCellInfo(
          icon: AppAsset.notiFitIcon,
          color: AppColor.notiFitColor,
        );
      case 'medrefill':
        return NotificationCellInfo(
          icon: AppAsset.medRefillIcon,
          color: const Color(0xFF64D296),
        );
      case 'custom':
        return NotificationCellInfo(
          icon: AppAsset.notiCustomIcon,
          color: AppColor.notiCustomColor,
        );
      case 'doctor appointment':
      case 'package_appointment':
      case 'doctor_appointment':
      case 'his_doctor_appointment':
        return NotificationCellInfo(
          icon: AppAsset.notiAppointmentIcon,
          color: AppColor.notiAppointmentColor,
        );
      case 'hn':
        return NotificationCellInfo(
          icon: AppAsset.samitivejServiceIcon,
          color: AppColor.coin,
        );
      case 'istatus':
        return NotificationCellInfo(
          icon: AppAsset.notiQueueIcon,
          color: AppColor.green1000,
        );
      default:
        return NotificationCellInfo(
          icon: AppAsset.notiDefaultIcon,
          color: AppColor.notiDefaultColor,
        );
    }
  }

  static Color getNotiCellMedrefillStatus(String? topic) {
    if (topic == null) {
      return const Color(0xFF00974F);
    }

    switch (topic) {
      case 'SentMedOrder':
      case 'ContractMedReceive':
        return const Color(0xFF00974F);
      case 'WaitMedOrder':
      case 'WaitMedDeliver':
        return const Color(0xFFEA8F07);
      case 'WaitMedDischarge':
        return AppColor.red300;
      case 'CancelMedOrderAdmin':
      case 'CancelMedOrderUser':
      case 'CancelMedOrderTimeOut':
      case 'DeliverMedFail':
      case 'PaymentMedFail':
        return const Color(0xFFFF5B5B);
      case 'DeliverMedStatus':
      case 'DeliverMedOrder':
        return const Color(0xFF2A7ECF);
      case 'DeliverMedSuccess':
      case 'PaymentMedSuccess':
        return AppColor.notiFitColor;
      default:
        return const Color(0xFF00974F);
    }
  }

  Future<void> getNotifications() async {
    try {
      final results = await NotificationService.getNotificationList();
      final queueApp = Get.find<QueueApp>();

      notifications.value.sort(((a, b) {
        final formator = DateFormat('yyyy-MM-ddThh:mm:ss');
        final timeA =
            a.createAt != null ? formator.parse(a.createAt!) : DateTime.now();
        final timeB =
            b.createAt != null ? formator.parse(b.createAt!) : DateTime.now();
        return timeB.compareTo(timeA);
      }));

      notifications.value = results;
      chat.value = notifications.value.where((e) => e.isChatUnRead).length;
      for (int i = 0; i < notifications.value.length; i++) {
        if (notifications.value[i].messageType != null) {
          if (notifications.value[i].messageType!.contains("chat")) {
            notifications.value.remove(notifications.value[i]);
          }
        }
      }
      badge.value = notifications.value.where((e) => e.isUnRead).length;

      await queueApp.getCurrentQueueStatus();
      await FirebaseMessaging.instance.getNotificationSettings();
      AppBadgePlus.updateBadge(badge.value + chat.value);
    } catch (_) {
      rethrow;
    }
  }

  Future<void> setNotificationToken() async {
    try {
      await NotificationService.insertNotificationToken();
    } catch (_) {
      rethrow;
    }
  }

  Future<void> updateBadgeNotification({int? notificationId}) async {
    try {
      await NotificationService.updateBadgeNotification(
        notificationId: notificationId,
      );

      if (notificationId != null) {
        final updateIndex = notifications.value.indexWhere(
          (e) => e.id == notificationId,
        );
        notifications.update((noti) {
          noti?[updateIndex].data['badge'] = '0';
        });
      } else {
        notifications.update((noti) {
          for (final e in noti!) {
            e.data['badge'] = '0';
          }
        });
      }

      badge.value = notifications.value.where((e) => e.isUnRead).length;
      chat.value = notifications.value.where((e) => e.isChatUnRead).length;
      AppBadgePlus.updateBadge(badge.value + chat.value);
    } catch (_) {
      rethrow;
    }
  }

  Future<void> updateChatNotification() async {
    try {
      await NotificationService.updateChatNotification();
      notifications.update((noti) {
        for (final e in noti!) {
          e.data['chat_badge'] = '0';
        }
      });

      badge.value = notifications.value.where((e) => e.isUnRead).length;
      chat.value = notifications.value.where((e) => e.isChatUnRead).length;
      AppBadgePlus.updateBadge(badge.value + chat.value);
    } catch (_) {
      rethrow;
    }
  }

  Future<void> notificationListener() async {
    await Future.delayed(Duration.zero);
    await getNotifications();

    // Get notification unread
    final initMessage = await FirebaseMessaging.instance.getInitialMessage();
    if (initMessage != null) {
      // Handle message when open app
      await notificationHandle(initMessage.data);
    }

    backgroudStream ??= FirebaseMessaging.onMessageOpenedApp.listen(
      (RemoteMessage message) async {
        // Handle message when background
        await Future.wait([
          getNotifications(),
          notificationHandle(message.data),
        ]);
      },
      cancelOnError: false,
    );

    inAppStream ??= FirebaseMessaging.onMessage.listen(
      (message) async {
        // Handle message when inapp
        await getNotifications();
      },
      cancelOnError: false,
    );
  }

  Future<void> notificationHandle(Map<String, dynamic> data) async {
    if (data['type'] != null) {
      final type = data['type'] as String;

      switch (type) {
        case 'food':
          await onFoodNotification();
          break;
        case 'fit':
        case 'burned cal goal':
        case 'distance goal':
          await onFitNotification();
          break;
        case 'morning fit':
        case 'evening fit':
          var workoutType = data['workoutType'] as String;
          var workoutName = data['workoutName'] as String;
          await onWorkoutNotification(workoutType, workoutName);
          break;
        case 'doctor appointment':
          var appointmentType = data['appointmentType'] as String;
          await onDoctorAppointmentNotification(appointmentType);
          break;
        case 'medrefill':
          var billNo = data['billNo'] as String;
          await onMedrefillNotification(billNo);
          break;
        case 'custom':
        case 'package_appointment':
        case 'his_doctor_appointment':
        case 'doctor_appointment':
          Get.back<void>();
          break;
        case 'hn':
          await WellNavigationService.openSamitivej();
          break;
        case 'chat':
          await WellNavigationService.openChat();
          break;
        case 'istatus':
          var site = data['site'] as String;
          var status = data['title'] as String;
          onQueueNotification(status, site);
          break;
        case 'third_party':
          final redirectUrl = data['redirectUrl'] as String;
          if (redirectUrl.isNotEmpty && WebUri(redirectUrl).isValidUri) {
            final haveHandle = WellNavigationService.redirectHandler(
              redirectUrl: redirectUrl,
              showAppBar: false,
              isInApp: false,
            );
            if (!haveHandle) {
              Get.back<void>();
            }
          }
          break;
        default:
          Get.back<void>();
          break;
      }
    }
  }

  Future<void> onFoodNotification() async {
    await WellNavigationService.openFood(queryString: null);
  }

  Future<void> onFitNotification() async {
    await WellNavigationService.openFit();
  }

  Future<void> onWorkoutNotification(String type, String name) async {
    await WellNavigationService.openFitWorkout(type, name);
  }

  Future<void> onQueueNotification(String title, String? site) async {
    final queueApp = Get.find<QueueApp>();

    queueApp.getCurrentQueueStatus();
    queueApp.getQueueList();
    if (title.contains("Check-in")) {
      queueApp.isScroll.value = true;
      queueApp.isHighlight.value = true;
      Get.offAll<void>(
        const HomeStackBar(),
        transition: Transition.noTransition,
      );
      await Get.dialog<bool?>(
        QueueWelcomeDialog(site: site),
      );
    } else if (title.contains("ขอบคุณ") || title.contains("Thank you")) {
      Get.to<void>(() => QueueFeedbackScreen(episodeNo: site));
    } else {
      Get.back<void>();
    }
  }

  Future<void> onDoctorAppointmentNotification(String appointmentType) async {
    final commonApp = Get.find<CommonApp>();
    commonApp.appLoading.value = true;

    int tabIndex = 0;

    if (appointmentType.contains('Confirm') ||
        appointmentType.contains('Change')) {
      tabIndex = 1;
    } else if (appointmentType.contains('Cancel')) {
      tabIndex = 3;
    }

    WellNavigationService.openMyAppointment(tabIndex);
  }

  Future<void> onMedrefillNotification(String billNo) async {
    await WellNavigationService.openMedRefill(
        billNo: billNo, queryString: null);
  }
}

class NotificationCellInfo {
  final String icon;
  final Color color;

  NotificationCellInfo({required this.icon, required this.color});
}
