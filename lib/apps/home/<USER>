import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:samitivej_flutter_app/apps/calendar/calendar_app.dart';
import 'package:samitivej_flutter_app/apps/coin/coin_app.dart';
import 'package:samitivej_flutter_app/apps/common/common_app.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/constants/app_asset.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/constants/app_constant.dart';
import 'package:samitivej_flutter_app/models/activity_item_model.dart';
import 'package:samitivej_flutter_app/models/activity_model.dart';
import 'package:samitivej_flutter_app/models/appointment_model.dart';
import 'package:samitivej_flutter_app/models/banner_item_model.dart';
import 'package:samitivej_flutter_app/models/daily_fasting_task_model.dart';
import 'package:samitivej_flutter_app/models/daily_task_item_model.dart';
import 'package:samitivej_flutter_app/models/discover_item_model.dart';
import 'package:samitivej_flutter_app/models/food_task_model.dart';
import 'package:samitivej_flutter_app/models/health_item_model.dart';
import 'package:samitivej_flutter_app/models/health_pressure_model.dart';
import 'package:samitivej_flutter_app/models/home_service_item_model.dart';
import 'package:samitivej_flutter_app/models/membership_home_model.dart';
import 'package:samitivej_flutter_app/models/mood_item_model.dart';
import 'package:samitivej_flutter_app/models/popup_model.dart';
import 'package:samitivej_flutter_app/models/product_feed_model.dart';
import 'package:samitivej_flutter_app/models/recent_menu_model.dart';
import 'package:samitivej_flutter_app/models/response_model.dart';
import 'package:samitivej_flutter_app/models/routine_data_model.dart';
import 'package:samitivej_flutter_app/models/session_activity_model.dart';
import 'package:samitivej_flutter_app/services/api_client.dart';
import 'package:samitivej_flutter_app/services/storage_service.dart';
import 'package:samitivej_flutter_app/services/well_navigate_service.dart';

class HomeApp extends GetxController {
  final ApiClient _client = ApiClient();

  /// The coin controller.
  final CoinApp _coinApp = Get.find<CoinApp>();

  /// The calendar controller.
  final CalendarApp _calendarApp = Get.find<CalendarApp>();

  /// The user controller.
  final CommonApp _commonApp = Get.find<CommonApp>();

  RxString errorMessage = ''.obs;

  RxBool isLoading = false.obs;
  RxBool isBannerLoading = false.obs;
  RxBool isSessionActivityLoading = true.obs;
  RxBool isDiscoverLoading = false.obs;
  RxBool isProductFeedLoading = false.obs;
  RxBool isMoodLoading = false.obs;
  RxBool isDailyTaskLoading = false.obs;
  RxBool isFitPlanLoading = false.obs;

  RxBool isRecentMenuLoading = false.obs;
  RxBool isActivityLoading = false.obs;
  RxBool isHealthInfoLoading = false.obs;
  RxBool isAppointmentLoading = false.obs;
  RxBool shouldShowConnectHnBar = false.obs;

  Rx<SessionActivityModel> sessionActivityData = Rx(SessionActivityModel());

  Rx<RecentMenuModel> recentMenuData = Rx(RecentMenuModel());
  RxList<BannerItemModel> bannerList = RxList.empty();
  RxList<DiscoverItemModel> discoverList = RxList.empty();
  Rxn<ProductFeedModel> productFeed = Rxn();
  RxnInt selectedProductTag = RxnInt();

  Rx<HealthItemModel> pulseHealthInfo = Rx(HealthItemModel());
  Rx<HealthItemModel> glucoseHealthInfo = Rx(HealthItemModel());
  Rx<HealthItemModel> bmiHealthInfo = Rx(HealthItemModel());
  Rx<HealthPressureModel> pressureHealthInfo = Rx(HealthPressureModel());

  Rx<ActivityModel> activityData = Rx(ActivityModel());
  Rx<AppointmentModel> appointmentData = Rx(AppointmentModel());

  ActivityItemModel moveActivity = ActivityItemModel();
  ActivityItemModel eatActivity = ActivityItemModel();
  ActivityItemModel waterActivity = ActivityItemModel();

  Rx<DailyTaskItemModel> workoutTaskData = Rx(DailyTaskItemModel());
  Rx<DailyFastingTaskModel> fastingTaskData = Rx(DailyFastingTaskModel());
  Rx<DailyTaskItemModel> foodTaskData = Rx(DailyTaskItemModel());
  Rx<DailyTaskItemModel> waterTaskData = Rx(DailyTaskItemModel());

  Rx<MoodItemModel?> moodData = Rx<MoodItemModel?>(null);

  RxList<MembershipHomeModel> membershipHomeBadge =
      RxList<MembershipHomeModel>();

  RxList<RoutineDataModel> dailyFitTask = RxList<RoutineDataModel>.empty();

  /// Whether the loading of daily food tasks of the current user is in progress or not.
  RxBool isDailyFoodTasksLoading = false.obs;

  /// Whether mood of the current user is updated.
  RxBool isMoodUpdated = false.obs;

  RxBool isUserHaveFoodPlan = false.obs;
  RxBool isUserHaveFitPlan = false.obs;

  /// The daily food tasks of the current user.
  Rx<List<FoodTaskModel>> dailyFoodTasks = Rx([]);

  /// Whether the updating of daily food tasks of the current user is in progress or not.
  RxBool isUpdateDailyFoodTasksLoading = false.obs;

  /// The ID updated daily food task
  RxInt updatedDailyFoodTaskId = 0.obs;

  /// Whether the loading of daily fit tasks of the current user is in progress or not.
  RxBool isDailyFitTasksLoading = false.obs;

  Map<String, dynamic> rawDataFit = {};

  Future<void> getDataHome({
    bool banner = true,
    bool discover = true,
    bool mood = true,
    bool regFoodPlan = true,
    bool foodTask = true,
    bool regFitPlan = true,
    bool fitTask = true,
    bool productFeed = true,
    bool membership = true,
  }) async {
    if (banner) {
      getBanner();
    }
    if (discover) {
      getDiscover();
    }
    if (mood) {
      getMood();
    }
    if (regFoodPlan) {
      getRegisteredFoodPlan();
    }
    if (foodTask) {
      getDailyFoodTasks();
    }
    if (regFitPlan) {
      getRegisteredFitPlan();
    }
    if (fitTask) {
      getFitDailyTask(
        // memberId: await _userApp.getMemberId(),
        startDate: DateTime(
            DateTime.now().year, DateTime.now().month, DateTime.now().day),
        endDate: DateTime(
            DateTime.now().year, DateTime.now().month, DateTime.now().day),
      );
    }
    if (productFeed) {
      getProductFeed();
    }
    if (membership) {
      getMembershipHome();
    }
  }

  final List<HomeServiceItemModel> serviceList = [
    HomeServiceItemModel(
      key: WellServiceKey.samitivej.name,
      name: 'Samitivej',
      icon: AppAsset.samitivejServiceIcon,
      color: 0xFFC2AB52,
    ),
    HomeServiceItemModel(
      key: WellServiceKey.food.name,
      name: 'Food',
      icon: AppAsset.foodServiceIcon,
      color: 0xFFAECC54,
    ),
    HomeServiceItemModel(
      key: WellServiceKey.fit.name,
      name: 'Fit',
      icon: AppAsset.fitServiceIcon,
      color: 0xFF5BAA5F,
    ),
    HomeServiceItemModel(
      key: WellServiceKey.health.name,
      name: 'Health Packages',
      icon: AppAsset.healthCheckupIcon,
      color: 0xFF2E338D,
    ),
    HomeServiceItemModel(
      key: WellServiceKey.gene.name,
      name: 'Gene Tests',
      icon: AppAsset.geneTestIcon,
      color: 0xFF55C1BB,
    ),
    HomeServiceItemModel(
      key: WellServiceKey.mom.name,
      name: 'Mom',
      icon: AppAsset.momServiceIcon,
      color: 0xFF32D0CF,
    ),
    HomeServiceItemModel(
      key: WellServiceKey.medrefill.name,
      name: 'Medication Refills',
      icon: AppAsset.medRefillIcon,
      color: 0xFF64D296,
    ),
    HomeServiceItemModel(
      key: WellServiceKey.shop.name,
      name: 'Shop',
      icon: AppAsset.shopServiceIcon,
      color: 0xFF0F5BA5,
    ),
    HomeServiceItemModel(
      key: WellServiceKey.kid.name,
      name: 'Kid',
      icon: AppAsset.kidServiceIcon,
      color: 0xFF69D39A,
    ),
    HomeServiceItemModel(
      key: WellServiceKey.engage.name,
      name: 'Engage',
      icon: AppAsset.engageCareIcon,
      color: 0xFF55C1BB,
    ),
    HomeServiceItemModel(
      key: WellServiceKey.vaccine.name,
      name: 'Vaccines',
      icon: AppAsset.vaccineIcon,
      color: AppColor.homeVaccineButton.value,
    ),
    HomeServiceItemModel(
      key: WellServiceKey.aidoctor.name,
      name: 'AI Doctor',
      icon: AppAsset.aidoctorIcon,
      color: 0xFF57B43C,
    ),
    HomeServiceItemModel(
      key: WellServiceKey.wearable.name,
      name: 'Wearable Clinic',
      icon: AppAsset.wearableIcon,
      color: 0xFF66CFCF,
    ),
    HomeServiceItemModel(
      key: WellServiceKey.corp.name,
      name: 'Corporate Benefits',
      icon: AppAsset.corpIcon,
      color: 0xFF107CB8,
    ),
  ];

  HomeServiceItemModel? getServiceData(String? key) {
    if (key == null) return null;
    return serviceList.firstWhereOrNull((element) => element.key == key);
  }

  Future<void> getBanner() async {
    try {
      isBannerLoading.value = true;
      final ResponseModel response = await _client.get(
        ApiConstant.getBanner,
        options: Options(
          headers: {"Accept-Language": _commonApp.selectedLanguage.value},
        ),
      );

      bannerList.clear();
      final List listData = response.data!['listData'] as List;
      bannerList.addAll(listData
          .map((e) => BannerItemModel.fromJson(e as Map<String, dynamic>))
          .toList());
      isBannerLoading.value = false;
    } on DioException catch (e) {
      errorMessage.value = e.message ?? e.type.name;
    } catch (e) {
      if (kDebugMode) {
        debugPrint("An error occur");
      }
    } finally {
      isBannerLoading.value = false;
    }
  }

  Future<String> getCoupon() async {
    try {
      final ResponseModel response = await _client.get(
        ApiConstant.getCouponUrl,
        options: Options(
          headers: {"Accept-Language": _commonApp.selectedLanguage.value},
        ),
      );

      final String couponUrl = response.data!['couponUrl'] as String;
      return couponUrl;
    } on DioException catch (e) {
      errorMessage.value = e.message ?? e.type.name;
      rethrow;
    } catch (e) {
      if (kDebugMode) {
        debugPrint("An error occur");
      }
      rethrow;
    }
  }

  Future<void> getDiscover() async {
    try {
      isDiscoverLoading.value = true;
      final ResponseModel response = await _client.get(ApiConstant.getDiscover);

      final List listResponse = response.data!['listData'] as List;
      discoverList.clear();
      discoverList.addAll(
        listResponse
            .map((e) => DiscoverItemModel.fromJson(e as Map<String, dynamic>))
            .toList(),
      );
      isDiscoverLoading.value = false;
    } on DioException catch (e) {
      errorMessage.value = e.message ?? e.type.name;
    } catch (e) {
      debugPrint("An error occur");
    } finally {
      isDiscoverLoading.value = false;
    }
  }

  Future<void> getMood() async {
    try {
      isMoodLoading.value = true;
      final Map<String, dynamic> body = {
        "date": DateFormat('y-MM-dd').format(DateTime.now()),
      };

      final ResponseModel response = await _client.post(
        ApiConstant.getMood,
        data: body,
      );

      if (response.data != null && response.data!.isNotEmpty) {
        moodData.value =
            MoodItemModel.fromJson(response.data as Map<String, dynamic>);

        if (moodData.value?.id == null) {
          moodData.value = null;
        }
      } else {
        moodData.value = null;
      }
    } catch (e) {
      debugPrint("An error occur");
    } finally {
      isMoodLoading.value = false;
    }
  }

  Future<void> getMembershipHome() async {
    try {
      final ResponseModel response = await _client.get(
        ApiConstant.getMembershipHome,
        options: Options(
          headers: {"Accept-Language": _commonApp.selectedLanguage.value},
        ),
      );

      final List listResponse = response.data!['listData'] as List;
      membershipHomeBadge.clear();
      membershipHomeBadge.addAll(
        (listResponse)
            .map((e) => MembershipHomeModel.fromJson(e as Map<String, dynamic>))
            .toList(),
      );
      return;
    } on DioException catch (e) {
      errorMessage.value = e.message ?? e.type.name;
      rethrow;
    } catch (e) {
      if (kDebugMode) {
        debugPrint("An error occur");
      }
      rethrow;
    }
  }

  Future<void> getProductFeed({int? filterTag}) async {
    try {
      isProductFeedLoading.value = true;
      final ResponseModel response = await _client.get(
        ApiConstant.productFeed,
        queryParameters: {
          "filterTag": filterTag ?? 0,
        },
      );
      productFeed.value = ProductFeedModel.fromJson(response.data!);
      if (productFeed.value != null) {
        if (productFeed.value!.productFeedList.isNotEmpty) {
          final firstTag = productFeed.value?.tagList.first;
          selectedProductTag.value = firstTag?.tagId;
        } else {
          // If productfeedlist is empty, hide productfeed.
          productFeed.value = null;
          selectedProductTag.value = null;
        }
      } else {
        selectedProductTag.value = null;
      }
      isProductFeedLoading.value = false;
    } on DioException catch (e) {
      productFeed.value = null;
      selectedProductTag.value = null;
      errorMessage.value = e.message ?? e.type.name;
    } catch (e) {
      productFeed.value = null;
      selectedProductTag.value = null;
      debugPrint("An error occur");
    } finally {
      isProductFeedLoading.value = false;
    }
  }

  Future<void> logProductFeed(int? productFeedId) async {
    try {
      await _client.post(
        ApiConstant.productFeed,
        data: {
          "productFeedId": productFeedId,
        },
      );
    } on DioException catch (e) {
      errorMessage.value = e.message ?? e.type.name;
    } catch (e) {
      debugPrint("An error occur");
    } finally {}
  }

  Future<void> insertMood({
    required MoodItemModel moodItem,
    DateTime? date,
  }) async {
    try {
      isMoodLoading.value = true;
      final today = DateTime.now();
      final Map<String, dynamic> body = {
        "moodInfoId": moodItem.id,
        "date": DateFormat('y-MM-dd').format(date ?? today),
      };

      final ResponseModel response = await _client.post(
        ApiConstant.insertMood,
        data: body,
      );

      if (response.data != null && response.data!.isNotEmpty) {
        if (DateUtils.isSameDay(date ?? today, today)) {
          moodData.value = MoodItemModel.fromJson(
            response.data as Map<String, dynamic>,
          );
        }
        isMoodUpdated.trigger(true);
      }
    } catch (e) {
      debugPrint("An error occur");
    } finally {
      isMoodLoading.value = false;
    }
  }

  Future<void> getRegisteredFitPlan() async {
    try {
      isDailyTaskLoading.value = true;
      isFitPlanLoading.value = true;
      await _client.post(ApiConstant.getFitPlan);
      isUserHaveFitPlan.value = true;
      isDailyTaskLoading.value = false;
      isFitPlanLoading.value = false;
    } on DioException {
      isUserHaveFitPlan.value = false;
      isFitPlanLoading.value = false;
    } finally {
      isDailyTaskLoading.value = false;
      isFitPlanLoading.value = false;
    }
  }

  Future<void> getFitDailyTask({
    // required int? memberId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    dailyFitTask.clear();
    try {
      isDailyTaskLoading.value = true;
      // if (memberId == null) throw Error;
      final Map<String, dynamic> body = {
        "getFitTaskRequestBean": {
          "start_date": DateFormat('yyyy-MM-ddTHH:mm:ss').format(startDate),
          "end_date": DateFormat('yyyy-MM-ddTHH:mm:ss').format(endDate),
        }
      };

      final ResponseModel response =
          await _client.post(ApiConstant.getFitDailyTask,
              data: body,
              options: Options(
                headers: {
                  "Accept-Language": _commonApp.selectedLanguage.value,
                },
              ));

      rawDataFit = response.data ?? {};

      final List listResponse = response.data!['listData'] as List;
      List<RoutineDataModel> routineData = <RoutineDataModel>[];

      routineData.addAll(
        listResponse
            .map(
              (e) => RoutineDataModel.fromJson(e as Map<String, dynamic>),
            )
            .toList(),
      );

      routineData.shuffle();
      dailyFitTask.value = routineData.take(3).toList();

      isDailyTaskLoading.value = false;
    } catch (e) {
      debugPrint("An error occur");
      isDailyTaskLoading.value = false;
    }
  }

  /// Gets daily food tasks of the current user on today.
  Future<void> getRegisteredFoodPlan() async {
    try {
      isDailyFoodTasksLoading.value = true;
      await _client.post(
        ApiConstant.getRegisteredFoodPlan,
      );
      isUserHaveFoodPlan.value = true;
    } on DioException {
      isUserHaveFoodPlan.value = false;
    } finally {
      isDailyFoodTasksLoading.value = false;
    }
  }

  /// Gets daily food tasks of the current user on today.
  Future<void> getDailyFoodTasks() async {
    try {
      isDailyFoodTasksLoading.value = true;
      final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
      final ResponseModel response = await _client.post(
        ApiConstant.getDailyFoodTasks,
        data: {'date': today},
      );
      final tasks = (response.data?['listData'] as List)
          .map((task) => FoodTaskModel.fromJson(task as Map<String, dynamic>))
          .toList(growable: false);
      tasks.sort((a, b) => (a.typeId ?? 0).compareTo(b.typeId ?? 0));
      dailyFoodTasks.value = tasks;
      isDailyFoodTasksLoading.value = false;
    } on DioException {
      dailyFoodTasks.value = [];
      isDailyFoodTasksLoading.value = false;
    }
  }

  /// Updates a daily food task with [id].
  Future<void> updateDailyFoodTask(int id) async {
    try {
      isUpdateDailyFoodTasksLoading.value = true;
      await _client.post(
        ApiConstant.updateDailyFoodTask,
        data: {'dailyTaskId': id},
      );
      updatedDailyFoodTaskId.value = id;
      isUpdateDailyFoodTasksLoading.value = false;

      // Reload total coins.
      _coinApp.getTotalCoins();

      // Reload data in calendar screen.
      _calendarApp.getFoodPlan();
      _calendarApp.getFitPlan();
    } on DioException {
      isUpdateDailyFoodTasksLoading.value = false;
    }
  }

  Future<List<PopupModel>> getPopup() async {
    try {
      final result = await _client.get(ApiConstant.getPopup);
      final rawResp = result.data!['listData'] as List;
      if (rawResp.isEmpty) return [];
      final response = rawResp.map((e) => PopupModel.fromJson(e)).toList();
      return response;
    } on DioException {
      rethrow;
    }
  }

  static Future<void> postponeConnectHnBar({
    Duration snoozeFor = const Duration(days: 14),
  }) async {
    final storage = StorageService();
    final DateTime now = DateTime.now();
    final nextAlert = now.add(snoozeFor);
    await storage.write(
        AppConstant.connectHnBarTimestamp, nextAlert.millisecondsSinceEpoch);
  }
}
