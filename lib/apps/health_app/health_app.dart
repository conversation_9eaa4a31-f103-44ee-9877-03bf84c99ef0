// import 'dart:collection';
// import 'dart:convert';
// import 'dart:io';

// import 'package:appcheck/appcheck.dart';
// import 'package:dio/dio.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter_dotenv/flutter_dotenv.dart';
// import 'package:get/get.dart';
// import 'package:health/health.dart';
// import 'package:intl/intl.dart';
// import 'package:permission_handler/permission_handler.dart';
// import 'package:samitivej_flutter_app/apps/auth/auth_app.dart';
// import 'package:samitivej_flutter_app/apps/user/user_app.dart';
// import 'package:samitivej_flutter_app/constants/app_api.dart';
// import 'package:samitivej_flutter_app/models/device_info_model.dart';
// import 'package:samitivej_flutter_app/models/health_item_model.dart';
// import 'package:samitivej_flutter_app/models/response_model.dart';
// import 'package:samitivej_flutter_app/services/api_client.dart';
// import 'package:samitivej_flutter_app/services/storage_service.dart';

// class HealthApp extends GetxController {
//   Health health = Health();
//   bool isFitInstalled = false;
//   List<HealthDataPoint> _pulseDataList = [];
//   List<HealthDataPoint> _stepDataList = [];
//   List<HealthDataPoint> _burnCaloriesSubDataList1 = [];
//   List<HealthDataPoint> _burnCaloriesSubDataList2 = [];
//   List<HealthDataPoint> _workoutDataList = [];

//   final ApiClient _client = ApiClient(baseUrl: ApiConstant.fitDomain);
//   final UserApp _userApp = Get.find<UserApp>();
//   final AuthApp authApp = Get.find<AuthApp>();
//   final storage = StorageService();

//   Rx<HealthItemModel> pulseDataPoint = Rx(HealthItemModel());
//   Rx<HealthItemModel> stepDataPoint = Rx(HealthItemModel());
//   Rx<HealthItemModel> burnedCaloriesDataPoint = Rx(HealthItemModel());
//   Rx<HealthItemModel> sleepDataPoint = Rx(HealthItemModel());
//   Rx<HealthItemModel> bmiDataPoint = Rx(HealthItemModel());
//   Rx<HealthItemModel> heightDataPoint = Rx(HealthItemModel());
//   Rx<HealthItemModel> weightDataPoint = Rx(HealthItemModel());
//   RxString pulseDataGraph = ''.obs;
//   RxString stepDataGraph = ''.obs;
//   RxString burnDataGraph = ''.obs;
//   RxString sleepDataGraph = ''.obs;

//   RxString errorMessage = ''.obs;

//   RxBool isDeviceLoading = false.obs;
//   RxBool isDeviceConnected = false.obs;
//   RxnBool isGrantPermission = RxnBool();
//   RxBool isLoadingHealthData = false.obs;
//   RxBool askPermissionFirstTime = RxBool(true);
//   RxBool hasPermission = false.obs;

//   RxList<DeviceInfoModel> deviceInfo = RxList.empty();
//   Rx<DeviceInfoModel?> deviceInfos = Rx(null);
//   RxString deviceStatus = ''.obs;
//   RxString oauthToken = ''.obs;
//   RxString oauthTokenSecret = ''.obs;
//   RxString oauthVerifier = ''.obs;
//   RxInt count = 0.obs;

//   @override
//   void onInit() {
//     isGrantPermission.value = storage.read<bool>('granthealthapp');
//     checkApp();
//     super.onInit();
//   }

//   Future<void> setUserPermission(bool value) async {
//     await storage.write('granthealthapp', value);
//     askPermissionFirstTime.value = false;
//     isGrantPermission.value = value;
//   }

//   bool get needUserPermission {
//     bool? grantPermission = isGrantPermission.value;
//     return deviceStatus.value == "apple" &&
//         Platform.isIOS &&
//         (grantPermission == null || !grantPermission);
//   }

//   Future<void> checkApp() async {
//     final appCheck = AppCheck();
//     bool installedApps = false;

//     if (Platform.isAndroid) {
//       const package = "com.google.android.apps.fitness";
//       try {
//         // You need to now make an instance of the class to use the methods, instead of using static methods. Checkout the example in the README for more info.
//         // https://pub.dev/packages/appcheck/changelog#152
//         installedApps = await appCheck.isAppInstalled(package);
//         if (installedApps) {
//           isFitInstalled = true;
//         }
//       } catch (_) {
//         isFitInstalled = false;
//       }
//     }
//   }

//   Future<void> upsertDeviceInfo(
//       {String? device,
//       String? deviceId,
//       String? deviceAccessToken,
//       String? deviceAccessTokenSecret,
//       String? deviceRefreshToken}) async {
//     try {
//       authApp;
//       isDeviceLoading.value = true;
//       await _client.post(
//         ApiConstant.upsertFitUser,
//         data: {
//           "createFitUserRequestBean": {
//             'birthDate': '',
//             'height': 0,
//             'weight': 0,
//             'pace': 0,
//             'device': device,
//             'deviceId': deviceId,
//             'deviceAccessToken': deviceAccessToken,
//             'deviceAccessTokenSecret': deviceAccessTokenSecret,
//             'deviceRefreshToken': deviceRefreshToken
//           }
//         },
//       );
//       isDeviceLoading.value = false;
//     } on DioException catch (e) {
//       errorMessage.value = e.message ?? e.type.name;
//     }
//   }

//   Future<void> updateDeviceInfo(
//       {String? device,
//       String? deviceId,
//       String? deviceAccessToken,
//       String? deviceAccessTokenSecret,
//       String? deviceRefreshToken}) async {
//     try {
//       authApp;
//       isDeviceLoading.value = true;
//       await _client.post(
//         ApiConstant.updateFitDevice,
//         data: {
//           "updateDeviceInfoRequestBean": {
//             'device': device,
//             'deviceId': deviceId,
//             'deviceAccessToken': deviceAccessToken,
//             'deviceAccessTokenSecret': deviceAccessTokenSecret,
//             'deviceRefreshToken': deviceRefreshToken
//           }
//         },
//       );
//       isDeviceLoading.value = false;
//     } on DioException catch (e) {
//       errorMessage.value = e.message ?? e.type.name;
//     }
//   }

//   Future<void> connectStrava({String? code}) async {
//     try {
//       authApp;
//       isDeviceLoading.value = true;
//       await _client
//           .post(ApiConstant.connectStrava, queryParameters: {'code': code});
//       isDeviceLoading.value = false;
//     } on DioException catch (e) {
//       errorMessage.value = e.message ?? e.type.name;
//     }
//   }

//   Future<void> deconnectStrava({String? accessToken}) async {
//     try {
//       authApp;
//       isDeviceLoading.value = true;
//       await _client.post(ApiConstant.deconnectStrava,
//           queryParameters: {'accessToken': accessToken});
//       isDeviceLoading.value = false;
//     } on DioException catch (e) {
//       errorMessage.value = e.message ?? e.type.name;
//     }
//   }

//   Future<void> getDeviceInfo({int retry = 1}) async {
//     try {
//       isDeviceLoading.value = true;
//       final ResponseModel response = await _client.post(
//         ApiConstant.getDeviceInfo,
//       );
//       deviceInfos.value = DeviceInfoModel.fromJson(response.data!);
//       if (deviceInfos.value!.device != null) {
//         isDeviceConnected.value = deviceInfos.value!.device!.isNotEmpty;
//         deviceStatus.value = deviceInfos.value!.device!;
//         isDeviceLoading.value = false;
//       } else {
//         isDeviceConnected.value = false;
//         isDeviceLoading.value = false;
//       }
//     } on DioException catch (e) {
//       deviceInfo.value = RxList.empty();
//       isDeviceConnected.value = false;
//       errorMessage.value = e.message ?? e.type.name;
//     } catch (e) {
//       if (deviceInfos.value!.device == null && retry > 0) {
//         await upsertDeviceInfo();
//         await getDeviceInfo(retry: retry - 1);
//       }
//     }
//   }

//   Future<void> fetchBMIDatapoint() async {
//     Map<String, dynamic> data1 = {};
//     Map<String, dynamic> data2 = {};
//     Map<String, dynamic> data3 = {};
//     data1['type'] = 'bmi';
//     data2['type'] = 'height';
//     data3['type'] = 'weight';
//     data2['unit'] = 'cm';
//     data3['unit'] = 'kg';
//     bmiDataPoint.value = HealthItemModel.fromJson(data1);
//     heightDataPoint.value = HealthItemModel.fromJson(data2);
//     weightDataPoint.value = HealthItemModel.fromJson(data3);
//     try {
//       isDeviceLoading.value = true;
//       final ResponseModel response = await _client.post(ApiConstant.getBMI);
//       response.data!.forEach((key, value) {
//         if (key == 'height') {
//           data2['value'] = value;
//         }
//         if (key == 'weight') {
//           data3['value'] = value;
//         }
//         if (key == 'bmi') {
//           data1['value'] = value;
//         }
//         if (key == 'date') {
//           if (value != null) {
//             final format = DateFormat('yyyy-MM-ddTHH:mm:ss');
//             int time = format.parse(value.toString()).millisecondsSinceEpoch;
//             data1['last_update_time'] = time;
//             data2['last_update_time'] = time;
//             data3['last_update_time'] = time;
//           }
//         }
//       });
//       bmiDataPoint.value = HealthItemModel.fromJson(data1);
//       heightDataPoint.value = HealthItemModel.fromJson(data2);
//       weightDataPoint.value = HealthItemModel.fromJson(data3);
//       isDeviceLoading.value = false;
//     } on DioException catch (e) {
//       errorMessage.value = e.message ?? e.type.name;
//     }
//   }

//   Future<void> updateBMIData(int questionId, String answer) async {
//     try {
//       //questionId 1 = height
//       //questionId 2 = weight
//       //questionId 3 = bmi
//       isDeviceLoading.value = true;
//       await _client.post(ApiConstant.updateQuestionnaireData, data: {
//         "createHealthAssessmentReqBean": {
//           "questionId": questionId,
//           "answer": answer
//         }
//       });
//       isDeviceLoading.value = false;
//     } on DioException catch (e) {
//       errorMessage.value = e.message ?? e.type.name;
//     }
//   }

//   Future<void> updateHealthData(
//       String date, int step, int pulse, int burn) async {
//     try {
//       isDeviceLoading.value = true;
//       await _client.post(ApiConstant.upsertHealthData, data: {
//         "healthRequestBean": {
//           "date": date,
//           "step": step,
//           "pulse": pulse,
//           "burnedCalorie": burn,
//           "stress": null,
//           "sleep": null
//         }
//       });
//       isDeviceLoading.value = false;
//     } on DioException catch (e) {
//       errorMessage.value = e.message ?? e.type.name;
//     }
//   }

// ///////---------------------Apple HealthKit---------------------//////
//   Future<void> fetchGoogleFitHeartRateGraph() async {
//     Map<String, dynamic> data = {};
//     data['type'] = 'heart rate';
//     data['unit'] = 'bpm';
//     data['status'] = 'too high/too low/normal';
//     pulseDataPoint.value = HealthItemModel.fromJson(data);

//     final types = [HealthDataType.HEART_RATE];

//     final now = DateTime.now();
//     final midnight = DateTime(now.year, now.month, now.day);

//     try {
//       // fetch health data
//       List<HealthDataPoint> healthData =
//           await health.getHealthDataFromTypes(midnight, now, types);

//       _pulseDataList.addAll((healthData.length < 1000)
//           ? healthData
//           : healthData.sublist(0, 1000));
//     } catch (error) {
//       debugPrint("Exception in getHealthDataFromTypes: $error");
//     }

//     _pulseDataList = HealthFactory.removeDuplicates(_pulseDataList);

//     Map<String, dynamic> datalist = {};
//     var beforeDate = 0;
//     if (_pulseDataList.isNotEmpty) {
//       if (_pulseDataList.first.dateFrom.millisecondsSinceEpoch >
//           _pulseDataList.last.dateFrom.millisecondsSinceEpoch) {
//         _pulseDataList = _pulseDataList.reversed.toList();
//       }

//       for (int i = 0; i < _pulseDataList.length; i++) {
//         num sum = (_pulseDataList[i].value as NumericHealthValue).numericValue;
//         int value = sum.toInt();
//         String format = DateFormat('yyyy-MM-ddTHH:mm:ss.SSS')
//             .format(_pulseDataList[i].dateTo);
//         if (_pulseDataList[i].dateFrom.millisecondsSinceEpoch > beforeDate) {
//           if (Platform.isIOS) {
//             if (_pulseDataList[i].sourceId.contains("com.apple.health") ||
//                 !_pulseDataList[i].sourceName.contains("Watch")) {
//               datalist[jsonEncode(format)] = value;
//               beforeDate = _pulseDataList[i].dateTo.millisecondsSinceEpoch;
//             }
//           }
//           if (Platform.isAndroid) {
//             datalist[jsonEncode(format)] = value;
//             beforeDate = _pulseDataList[i].dateTo.millisecondsSinceEpoch;
//           }
//         } else {
//           if (Platform.isIOS) {
//             if (!_pulseDataList[i].sourceName.contains("Watch")) {
//               datalist[jsonEncode(format)] = value;
//               beforeDate = _pulseDataList[i].dateTo.millisecondsSinceEpoch;
//               if (i > 0) {
//                 if (!_pulseDataList[i - 1].sourceName.contains("Watch")) {
//                   datalist.remove(DateFormat('yyyy-MM-ddTHH:mm:ss.SSS')
//                       .format(_pulseDataList[i - 1].dateTo));
//                 }
//               }
//             }
//           } else {
//             if (_pulseDataList[i].dateFrom.millisecondsSinceEpoch ==
//                 beforeDate) {
//               datalist[jsonEncode(format)] = value;
//               beforeDate = _pulseDataList[i].dateTo.millisecondsSinceEpoch;
//             }
//           }
//         }
//       }
//     }
//     var sorted =
//         SplayTreeMap<String, dynamic>.from(datalist, (a, b) => a.compareTo(b));
//     if (sorted.isNotEmpty) {
//       data['value'] = sorted.entries.last.value;
//       data['last_update_time'] = DateTime.parse(sorted.entries.last.key
//               .substring(1, sorted.entries.last.key.length - 1))
//           .millisecondsSinceEpoch;
//     }
//     pulseDataPoint.value = HealthItemModel.fromJson(data);
//     pulseDataGraph.value = json.encode(sorted);
//   }

//   Future<void> fetchGoogleFitBurnedCaloriesGraph() async {
//     Map<String, dynamic> data = {};
//     data['type'] = 'burn';
//     data['unit'] = 'cal';
//     data['status'] = '';
//     burnedCaloriesDataPoint.value = HealthItemModel.fromJson(data);

//     final type1 = [HealthDataType.ACTIVE_ENERGY_BURNED];

//     final now = DateTime.now();
//     final midnight = DateTime(now.year, now.month, now.day);

//     try {
//       List<HealthDataPoint> healthData1 =
//           await health.getHealthDataFromTypes(midnight, now, type1);

//       _burnCaloriesSubDataList1.addAll((healthData1.length < 1000)
//           ? healthData1
//           : healthData1.sublist(0, 1000));
//     } catch (error) {
//       debugPrint("Exception in getHealthDataFromTypes: $error");
//     }

//     _burnCaloriesSubDataList1 =
//         HealthFactory.removeDuplicates(_burnCaloriesSubDataList1);

//     var beforeDate = 0;

//     Map<String, dynamic> datalist = {};
//     if (_burnCaloriesSubDataList1.isNotEmpty) {
//       if (_burnCaloriesSubDataList1.first.dateFrom.millisecondsSinceEpoch >
//           _burnCaloriesSubDataList1.last.dateFrom.millisecondsSinceEpoch) {
//         _burnCaloriesSubDataList1 = _burnCaloriesSubDataList1.reversed.toList();
//       }

//       for (int i = 0; i < _burnCaloriesSubDataList1.length; i++) {
//         num sum = (_burnCaloriesSubDataList1[i].value as NumericHealthValue)
//             .numericValue;
//         double value = sum.toDouble();
//         String format = DateFormat('yyyy-MM-ddTHH:mm:ss.SSS')
//             .format(_burnCaloriesSubDataList1[i].dateTo);
//         if (_burnCaloriesSubDataList1[i].dateFrom.millisecondsSinceEpoch >
//             beforeDate) {
//           if (Platform.isIOS) {
//             if (_burnCaloriesSubDataList1[i]
//                     .sourceId
//                     .contains("com.apple.health") ||
//                 _burnCaloriesSubDataList1[i]
//                     .sourceId
//                     .contains("com.apple.Health")) {
//               if (datalist.containsKey(jsonEncode(format))) {
//                 datalist[jsonEncode(format)] += value;
//               } else {
//                 datalist[jsonEncode(format)] = value;
//               }
//               beforeDate =
//                   _burnCaloriesSubDataList1[i].dateTo.millisecondsSinceEpoch;
//             }
//           }
//           if (Platform.isAndroid) {
//             if (datalist.containsKey(jsonEncode(format))) {
//               datalist[jsonEncode(format)] += value;
//             } else {
//               datalist[jsonEncode(format)] = value;
//             }
//             beforeDate =
//                 _burnCaloriesSubDataList1[i].dateTo.millisecondsSinceEpoch;
//           }
//         } else {
//           if (_burnCaloriesSubDataList1[i].dateFrom.millisecondsSinceEpoch ==
//               beforeDate) {
//             if (datalist.containsKey(jsonEncode(format))) {
//               datalist[jsonEncode(format)] += value;
//             } else {
//               datalist[jsonEncode(format)] = value;
//             }
//             beforeDate =
//                 _burnCaloriesSubDataList1[i].dateTo.millisecondsSinceEpoch;
//           }
//         }
//       }
//     }
//     var sorted =
//         SplayTreeMap<String, dynamic>.from(datalist, (a, b) => a.compareTo(b));
//     if (sorted.isNotEmpty) {
//       data['last_update_time'] = DateTime.parse(sorted.entries.last.key
//               .substring(1, sorted.entries.last.key.length - 1))
//           .millisecondsSinceEpoch;
//       double datapoint = 0.00;
//       sorted.forEach((key, value) {
//         if (value != null) {
//           datapoint += double.parse(value.toString());
//         }
//       });
//       if (datapoint == 0.00) {
//         data['value'] = null;
//       } else {
//         data['value'] = datapoint.toPrecision(2);
//       }
//     }
//     burnedCaloriesDataPoint.value = HealthItemModel.fromJson(data);
//     burnDataGraph.value = json.encode(sorted);
//   }

//   Future<void> fetchGoogleFitStepGraph() async {
//     Map<String, dynamic> data = {};
//     data['type'] = 'step';
//     data['unit'] = 'steps';
//     data['status'] = '';
//     stepDataPoint.value = HealthItemModel.fromJson(data);

//     final types = [HealthDataType.STEPS];

//     final now = DateTime.now();
//     final midnight = DateTime(now.year, now.month, now.day);

//     try {
//       // fetch health data
//       List<HealthDataPoint> healthData =
//           await health.getHealthDataFromTypes(midnight, now, types);

//       _stepDataList.addAll((healthData.length < 1000)
//           ? healthData
//           : healthData.sublist(0, 1000));
//     } catch (error) {
//       debugPrint("Exception in getHealthDataFromTypes: $error");
//     }

//     _stepDataList = HealthFactory.removeDuplicates(_stepDataList);

//     Map<String, dynamic> datalist = {};
//     var beforeDate = 0;

//     if (_stepDataList.isNotEmpty) {
//       if (_stepDataList.first.dateFrom.millisecondsSinceEpoch >
//           _stepDataList.last.dateFrom.millisecondsSinceEpoch) {
//         _stepDataList = _stepDataList.reversed.toList();
//       }

//       for (int i = 0; i < _stepDataList.length; i++) {
//         num sum = (_stepDataList[i].value as NumericHealthValue).numericValue;
//         int value = sum.toInt();
//         String format = DateFormat('yyyy-MM-ddTHH:mm:ss.SSS')
//             .format(_stepDataList[i].dateTo);
//         if (_stepDataList[i].dateFrom.millisecondsSinceEpoch > beforeDate) {
//           if (Platform.isIOS) {
//             if (_stepDataList[i].sourceId.contains("com.apple.health")) {
//               datalist[jsonEncode(format)] = value;
//               beforeDate = _stepDataList[i].dateTo.millisecondsSinceEpoch;
//             }
//           }
//           if (Platform.isAndroid) {
//             datalist[jsonEncode(format)] = value;
//             beforeDate = _stepDataList[i].dateTo.millisecondsSinceEpoch;
//           }
//         } else {
//           if (Platform.isIOS) {
//             if (_stepDataList[i].dateFrom.millisecondsSinceEpoch ==
//                 beforeDate) {
//               datalist[jsonEncode(format)] = value;
//               beforeDate = _stepDataList[i].dateTo.millisecondsSinceEpoch;
//             } else {
//               if (!_stepDataList[i].sourceName.contains("Watch")) {
//                 datalist[jsonEncode(format)] = value;
//                 beforeDate = _stepDataList[i].dateTo.millisecondsSinceEpoch;
//                 if (i > 0) {
//                   if (!_stepDataList[i - 1].sourceName.contains("Watch")) {
//                     datalist.remove(DateFormat('yyyy-MM-ddTHH:mm:ss.SSS')
//                         .format(_stepDataList[i - 1].dateTo));
//                   }
//                 }
//               }
//             }
//           } else {
//             if (_stepDataList[i].dateFrom.millisecondsSinceEpoch ==
//                 beforeDate) {
//               datalist[jsonEncode(format)] = value;
//               beforeDate = _stepDataList[i].dateTo.millisecondsSinceEpoch;
//             }
//           }
//         }
//       }
//     }

//     var sorted =
//         SplayTreeMap<String, dynamic>.from(datalist, (a, b) => a.compareTo(b));
//     int datapoint = 0;
//     if (sorted.isNotEmpty) {
//       sorted.forEach((key, value) {
//         if (value != null) {
//           datapoint += int.parse(value.toString());
//         }
//       });
//       if (datapoint == 0) {
//         data['value'] = null;
//       } else {
//         data['value'] = datapoint;
//       }
//       data['last_update_time'] = DateTime.parse(sorted.entries.last.key
//               .substring(1, sorted.entries.last.key.length - 1))
//           .millisecondsSinceEpoch;
//     }
//     stepDataPoint.value = HealthItemModel.fromJson(data);
//     stepDataGraph.value = json.encode(sorted);
//   }

//   Future<List<HealthDataPoint>?> fetchGoogleFitWorkoutDataTest() async {
//     final type = [HealthDataType.WORKOUT];

//     final now = DateTime.now();
//     final midnight = DateTime(now.year, now.month, now.day);

//     try {
//       List<HealthDataPoint> healthData =
//           await health.getHealthDataFromTypes(midnight, now, type);
//       _workoutDataList.addAll(
//           (healthData.length < 100) ? healthData : healthData.sublist(0, 100));
//     } catch (error) {
//       debugPrint("Exception in getHealthDataFromTypes: $error");
//       return null;
//     }
//     _workoutDataList = HealthFactory.removeDuplicates(_workoutDataList);
//     return _workoutDataList;
//   }

//   Future<void> fetchHeartRateGraph() async {
//     Map<String, dynamic> data = {};
//     data['type'] = 'heart rate';
//     data['unit'] = 'bpm';
//     data['status'] = 'too high/too low/normal';
//     pulseDataPoint.value = HealthItemModel.fromJson(data);

//     final types = [HealthDataType.HEART_RATE];
//     final permissions = [HealthDataAccess.READ];

//     final now = DateTime.now();
//     final midnight = DateTime(now.year, now.month, now.day);

//     bool requested =
//         await health.requestAuthorization(types, permissions: permissions);

//     if (requested) {
//       try {
//         // fetch health data
//         List<HealthDataPoint> healthData =
//             await health.getHealthDataFromTypes(midnight, now, types);

//         _pulseDataList.addAll((healthData.length < 1000)
//             ? healthData
//             : healthData.sublist(0, 1000));
//       } catch (error) {
//         debugPrint("Exception in getHealthDataFromTypes: $error");
//       }

//       _pulseDataList = HealthFactory.removeDuplicates(_pulseDataList);

//       Map<String, dynamic> datalist = {};
//       var beforeDate = 0;
//       if (_pulseDataList.isNotEmpty) {
//         if (_pulseDataList.first.dateFrom.millisecondsSinceEpoch >
//             _pulseDataList.last.dateFrom.millisecondsSinceEpoch) {
//           _pulseDataList = _pulseDataList.reversed.toList();
//         }

//         for (int i = 0; i < _pulseDataList.length; i++) {
//           num sum =
//               (_pulseDataList[i].value as NumericHealthValue).numericValue;
//           int value = sum.toInt();
//           String format = DateFormat('yyyy-MM-ddTHH:mm:ss.SSS')
//               .format(_pulseDataList[i].dateTo);
//           if (_pulseDataList[i].dateFrom.millisecondsSinceEpoch > beforeDate) {
//             if (Platform.isIOS) {
//               if (_pulseDataList[i].sourceId.contains("com.apple.health") ||
//                   !_pulseDataList[i].sourceName.contains("Watch")) {
//                 datalist[jsonEncode(format)] = value;
//                 beforeDate = _pulseDataList[i].dateTo.millisecondsSinceEpoch;
//               }
//             }
//             if (Platform.isAndroid) {
//               datalist[jsonEncode(format)] = value;
//               beforeDate = _pulseDataList[i].dateTo.millisecondsSinceEpoch;
//             }
//           } else {
//             if (Platform.isIOS) {
//               if (!_pulseDataList[i].sourceName.contains("Watch")) {
//                 datalist[jsonEncode(format)] = value;
//                 beforeDate = _pulseDataList[i].dateTo.millisecondsSinceEpoch;
//                 if (i > 0) {
//                   if (!_pulseDataList[i - 1].sourceName.contains("Watch")) {
//                     datalist.remove(DateFormat('yyyy-MM-ddTHH:mm:ss.SSS')
//                         .format(_pulseDataList[i - 1].dateTo));
//                   }
//                 }
//               }
//             } else {
//               if (_pulseDataList[i].dateFrom.millisecondsSinceEpoch ==
//                   beforeDate) {
//                 datalist[jsonEncode(format)] = value;
//                 beforeDate = _pulseDataList[i].dateTo.millisecondsSinceEpoch;
//               }
//             }
//           }
//         }
//       }
//       var sorted = SplayTreeMap<String, dynamic>.from(
//           datalist, (a, b) => a.compareTo(b));
//       if (sorted.isNotEmpty) {
//         data['value'] = sorted.entries.last.value;
//         data['last_update_time'] = DateTime.parse(sorted.entries.last.key
//                 .substring(1, sorted.entries.last.key.length - 1))
//             .millisecondsSinceEpoch;
//       }
//       pulseDataPoint.value = HealthItemModel.fromJson(data);
//       pulseDataGraph.value = json.encode(sorted);
//     } else {
//       Map<String, dynamic> datalist = {};
//       String format =
//           DateFormat('yyyy-MM-ddTHH:mm:ss.SSS').format(DateTime.now());
//       datalist[jsonEncode(format)] = null;
//       pulseDataGraph.value = json.encode(datalist);
//       data['value'] = pulseDataPoint.value.value;
//       pulseDataPoint.value = HealthItemModel.fromJson(data);
//       debugPrint("Authorization not granted");
//     }
//   }

//   Future<void> fetchBurnedCaloriesGraph() async {
//     Map<String, dynamic> data = {};
//     data['type'] = 'burn';
//     data['unit'] = 'cal';
//     data['status'] = '';
//     burnedCaloriesDataPoint.value = HealthItemModel.fromJson(data);

//     final type1 = [HealthDataType.ACTIVE_ENERGY_BURNED];
//     final type2 = [HealthDataType.BASAL_ENERGY_BURNED];

//     final permissions = [HealthDataAccess.READ];

//     final now = DateTime.now();
//     final midnight = DateTime(now.year, now.month, now.day);

//     bool requestedType1 =
//         await health.requestAuthorization(type1, permissions: permissions);
//     bool requestedType2 = false;
//     if (Platform.isIOS) {
//       requestedType2 =
//           await health.requestAuthorization(type2, permissions: permissions);
//     } else {
//       requestedType2 = true;
//     }

//     if (requestedType1 & requestedType2) {
//       try {
//         List<HealthDataPoint> healthData1 =
//             await health.getHealthDataFromTypes(midnight, now, type1);
//         List<HealthDataPoint> healthData2 = List<HealthDataPoint>.empty();
//         if (Platform.isIOS) {
//           healthData2 =
//               await health.getHealthDataFromTypes(midnight, now, type2);
//         }

//         _burnCaloriesSubDataList1.addAll((healthData1.length < 1000)
//             ? healthData1
//             : healthData1.sublist(0, 1000));

//         _burnCaloriesSubDataList2.addAll((healthData2.length < 1000)
//             ? healthData2
//             : healthData2.sublist(0, 1000));
//       } catch (error) {
//         debugPrint("Exception in getHealthDataFromTypes: $error");
//       }

//       _burnCaloriesSubDataList1 =
//           HealthFactory.removeDuplicates(_burnCaloriesSubDataList1);
//       _burnCaloriesSubDataList2 =
//           HealthFactory.removeDuplicates(_burnCaloriesSubDataList2);

//       var beforeDate = 0;
//       var beforeDate2 = 0;

//       Map<String, dynamic> datalist = {};
//       Map<String, dynamic> datalist2 = {};
//       if (_burnCaloriesSubDataList1.isNotEmpty) {
//         if (_burnCaloriesSubDataList1.first.dateFrom.millisecondsSinceEpoch >
//             _burnCaloriesSubDataList1.last.dateFrom.millisecondsSinceEpoch) {
//           _burnCaloriesSubDataList1 =
//               _burnCaloriesSubDataList1.reversed.toList();
//         }

//         for (int i = 0; i < _burnCaloriesSubDataList1.length; i++) {
//           num sum = (_burnCaloriesSubDataList1[i].value as NumericHealthValue)
//               .numericValue;
//           double value = sum.toDouble();
//           String format = DateFormat('yyyy-MM-ddTHH:mm:ss.SSS')
//               .format(_burnCaloriesSubDataList1[i].dateTo);
//           if (_burnCaloriesSubDataList1[i].dateFrom.millisecondsSinceEpoch >
//               beforeDate) {
//             if (Platform.isIOS) {
//               if (_burnCaloriesSubDataList1[i]
//                       .sourceId
//                       .contains("com.apple.health") ||
//                   _burnCaloriesSubDataList1[i]
//                       .sourceId
//                       .contains("com.apple.Health")) {
//                 if (datalist.containsKey(jsonEncode(format))) {
//                   datalist[jsonEncode(format)] += value;
//                 } else {
//                   datalist[jsonEncode(format)] = value;
//                 }
//                 beforeDate =
//                     _burnCaloriesSubDataList1[i].dateTo.millisecondsSinceEpoch;
//               }
//             }
//             if (Platform.isAndroid) {
//               if (datalist.containsKey(jsonEncode(format))) {
//                 datalist[jsonEncode(format)] += value;
//               } else {
//                 datalist[jsonEncode(format)] = value;
//               }
//               beforeDate =
//                   _burnCaloriesSubDataList1[i].dateTo.millisecondsSinceEpoch;
//             }
//           } else {
//             if (Platform.isIOS) {
//               if (_burnCaloriesSubDataList1[i]
//                       .dateFrom
//                       .millisecondsSinceEpoch ==
//                   beforeDate) {
//                 if (datalist2.containsKey(jsonEncode(format))) {
//                   datalist2[jsonEncode(format)] += value;
//                 } else {
//                   datalist2[jsonEncode(format)] = value;
//                 }
//               } else {
//                 if (!_burnCaloriesSubDataList1[i]
//                     .sourceName
//                     .contains("Watch")) {
//                   if (datalist.containsKey(jsonEncode(format))) {
//                     datalist[jsonEncode(format)] += value;
//                   } else {
//                     datalist[jsonEncode(format)] = value;
//                   }
//                   beforeDate = _burnCaloriesSubDataList1[i]
//                       .dateTo
//                       .millisecondsSinceEpoch;
//                   if (i > 0) {
//                     if (!_burnCaloriesSubDataList1[i - 1]
//                         .sourceName
//                         .contains("Watch")) {
//                       datalist.remove(DateFormat('yyyy-MM-ddTHH:mm:ss.SSS')
//                           .format(_burnCaloriesSubDataList1[i - 1].dateTo));
//                     }
//                   }
//                 }
//               }
//             } else {
//               if (_burnCaloriesSubDataList1[i]
//                       .dateFrom
//                       .millisecondsSinceEpoch ==
//                   beforeDate) {
//                 if (datalist.containsKey(jsonEncode(format))) {
//                   datalist[jsonEncode(format)] += value;
//                 } else {
//                   datalist[jsonEncode(format)] = value;
//                 }
//                 beforeDate =
//                     _burnCaloriesSubDataList1[i].dateTo.millisecondsSinceEpoch;
//               }
//             }
//           }
//         }
//       }
//       if (_burnCaloriesSubDataList2.isNotEmpty) {
//         if (_burnCaloriesSubDataList2.first.dateFrom.millisecondsSinceEpoch >
//             _burnCaloriesSubDataList2.last.dateFrom.millisecondsSinceEpoch) {
//           _burnCaloriesSubDataList2 =
//               _burnCaloriesSubDataList2.reversed.toList();
//         }

//         for (int i = 0; i < _burnCaloriesSubDataList2.length; i++) {
//           num sum = (_burnCaloriesSubDataList2[i].value as NumericHealthValue)
//               .numericValue;
//           double value = sum.toDouble();
//           String format = DateFormat('yyyy-MM-ddTHH:mm:ss.SSS')
//               .format(_burnCaloriesSubDataList2[i].dateTo);
//           if (_burnCaloriesSubDataList2[i].dateFrom.millisecondsSinceEpoch >
//               beforeDate2) {
//             if (Platform.isIOS) {
//               if (_burnCaloriesSubDataList2[i]
//                       .sourceId
//                       .contains("com.apple.health") ||
//                   _burnCaloriesSubDataList2[i]
//                       .sourceId
//                       .contains("com.apple.Health")) {
//                 if (datalist2.containsKey(jsonEncode(format))) {
//                   datalist2[jsonEncode(format)] += value;
//                 } else {
//                   datalist2[jsonEncode(format)] = value;
//                 }
//                 beforeDate2 =
//                     _burnCaloriesSubDataList2[i].dateTo.millisecondsSinceEpoch;
//               }
//             }
//             if (Platform.isAndroid) {
//               if (datalist2.containsKey(jsonEncode(format))) {
//                 datalist2[jsonEncode(format)] += value;
//               } else {
//                 datalist2[jsonEncode(format)] = value;
//               }
//               beforeDate2 =
//                   _burnCaloriesSubDataList2[i].dateTo.millisecondsSinceEpoch;
//             }
//           } else {
//             if (Platform.isIOS) {
//               if (_burnCaloriesSubDataList2[i]
//                       .dateFrom
//                       .millisecondsSinceEpoch ==
//                   beforeDate2) {
//                 if (datalist2.containsKey(jsonEncode(format))) {
//                   datalist2[jsonEncode(format)] += value;
//                 } else {
//                   datalist2[jsonEncode(format)] = value;
//                 }
//               } else {
//                 if (!_burnCaloriesSubDataList2[i]
//                     .sourceName
//                     .contains("Watch")) {
//                   if (datalist2.containsKey(jsonEncode(format))) {
//                     datalist2[jsonEncode(format)] += value;
//                   } else {
//                     datalist2[jsonEncode(format)] = value;
//                   }
//                   beforeDate2 = _burnCaloriesSubDataList2[i]
//                       .dateTo
//                       .millisecondsSinceEpoch;
//                   if (i > 0) {
//                     if (!_burnCaloriesSubDataList2[i - 1]
//                         .sourceName
//                         .contains("Watch")) {
//                       datalist2.remove(DateFormat('yyyy-MM-ddTHH:mm:ss.SSS')
//                           .format(_burnCaloriesSubDataList2[i - 1].dateTo));
//                     }
//                   }
//                 }
//               }
//             } else {
//               if (_burnCaloriesSubDataList2[i]
//                       .dateFrom
//                       .millisecondsSinceEpoch ==
//                   beforeDate2) {
//                 if (datalist2.containsKey(jsonEncode(format))) {
//                   datalist2[jsonEncode(format)] += value;
//                 } else {
//                   datalist2[jsonEncode(format)] = value;
//                 }
//                 beforeDate2 =
//                     _burnCaloriesSubDataList2[i].dateTo.millisecondsSinceEpoch;
//               }
//             }
//           }
//         }
//       }
//       if (datalist2.isNotEmpty) {
//         datalist2.forEach((key, value) {
//           if (datalist.containsKey(key)) {
//             datalist[key] = (double.parse(datalist[key].toString()) +
//                 double.parse(value.toString()));
//           } else {
//             datalist[key] = value;
//           }
//         });
//       }
//       var sorted = SplayTreeMap<String, dynamic>.from(
//           datalist, (a, b) => a.compareTo(b));
//       if (sorted.isNotEmpty) {
//         data['last_update_time'] = DateTime.parse(sorted.entries.last.key
//                 .substring(1, sorted.entries.last.key.length - 1))
//             .millisecondsSinceEpoch;
//         double datapoint = 0.00;
//         sorted.forEach((key, value) {
//           if (value != null) {
//             datapoint += double.parse(value.toString());
//           }
//         });
//         if (datapoint == 0.00) {
//           data['value'] = null;
//         } else {
//           data['value'] = datapoint.toPrecision(2);
//         }
//       }
//       burnedCaloriesDataPoint.value = HealthItemModel.fromJson(data);
//       burnDataGraph.value = json.encode(sorted);
//     } else {
//       Map<String, dynamic> datalist = {};
//       String format =
//           DateFormat('yyyy-MM-ddTHH:mm:ss.SSS').format(DateTime.now());
//       datalist[jsonEncode(format)] = null;
//       burnDataGraph.value = json.encode(datalist);
//       data['value'] = burnedCaloriesDataPoint.value.value;
//       stepDataPoint.value = HealthItemModel.fromJson(data);
//       debugPrint("Authorization not granted");
//     }
//   }

//   Future<void> fetchStepGraph() async {
//     Map<String, dynamic> data = {};
//     data['type'] = 'step';
//     data['unit'] = 'steps';
//     data['status'] = '';
//     stepDataPoint.value = HealthItemModel.fromJson(data);

//     final types = [HealthDataType.STEPS];
//     final permissions = [HealthDataAccess.READ];

//     final now = DateTime.now();
//     final midnight = DateTime(now.year, now.month, now.day);

//     bool requested =
//         await health.requestAuthorization(types, permissions: permissions);

//     if (requested) {
//       try {
//         // fetch health data
//         List<HealthDataPoint> healthData =
//             await health.getHealthDataFromTypes(midnight, now, types);

//         _stepDataList.addAll((healthData.length < 1000)
//             ? healthData
//             : healthData.sublist(0, 1000));
//       } catch (error) {
//         debugPrint("Exception in getHealthDataFromTypes: $error");
//       }

//       _stepDataList = HealthFactory.removeDuplicates(_stepDataList);

//       Map<String, dynamic> datalist = {};
//       var beforeDate = 0;

//       if (_stepDataList.isNotEmpty) {
//         if (_stepDataList.first.dateFrom.millisecondsSinceEpoch >
//             _stepDataList.last.dateFrom.millisecondsSinceEpoch) {
//           _stepDataList = _stepDataList.reversed.toList();
//         }

//         for (int i = 0; i < _stepDataList.length; i++) {
//           num sum = (_stepDataList[i].value as NumericHealthValue).numericValue;
//           int value = sum.toInt();
//           String format = DateFormat('yyyy-MM-ddTHH:mm:ss.SSS')
//               .format(_stepDataList[i].dateTo);
//           if (_stepDataList[i].dateFrom.millisecondsSinceEpoch > beforeDate) {
//             if (Platform.isIOS) {
//               if (_stepDataList[i].sourceId.contains("com.apple.health")) {
//                 datalist[jsonEncode(format)] = value;
//                 beforeDate = _stepDataList[i].dateTo.millisecondsSinceEpoch;
//               }
//             }
//             if (Platform.isAndroid) {
//               datalist[jsonEncode(format)] = value;
//               beforeDate = _stepDataList[i].dateTo.millisecondsSinceEpoch;
//             }
//           } else {
//             if (Platform.isIOS) {
//               if (_stepDataList[i].dateFrom.millisecondsSinceEpoch ==
//                   beforeDate) {
//                 datalist[jsonEncode(format)] = value;
//                 beforeDate = _stepDataList[i].dateTo.millisecondsSinceEpoch;
//               } else {
//                 if (!_stepDataList[i].sourceName.contains("Watch")) {
//                   datalist[jsonEncode(format)] = value;
//                   beforeDate = _stepDataList[i].dateTo.millisecondsSinceEpoch;
//                   if (i > 0) {
//                     if (!_stepDataList[i - 1].sourceName.contains("Watch")) {
//                       datalist.remove(DateFormat('yyyy-MM-ddTHH:mm:ss.SSS')
//                           .format(_stepDataList[i - 1].dateTo));
//                     }
//                   }
//                 }
//               }
//             } else {
//               if (_stepDataList[i].dateFrom.millisecondsSinceEpoch ==
//                   beforeDate) {
//                 datalist[jsonEncode(format)] = value;
//                 beforeDate = _stepDataList[i].dateTo.millisecondsSinceEpoch;
//               }
//             }
//           }
//         }
//       }

//       var sorted = SplayTreeMap<String, dynamic>.from(
//           datalist, (a, b) => a.compareTo(b));
//       int datapoint = 0;
//       if (sorted.isNotEmpty) {
//         sorted.forEach((key, value) {
//           if (value != null) {
//             datapoint += int.parse(value.toString());
//           }
//         });
//         if (datapoint == 0) {
//           data['value'] = null;
//         } else {
//           data['value'] = datapoint;
//         }
//         data['last_update_time'] = DateTime.parse(sorted.entries.last.key
//                 .substring(1, sorted.entries.last.key.length - 1))
//             .millisecondsSinceEpoch;
//       }
//       stepDataPoint.value = HealthItemModel.fromJson(data);
//       stepDataGraph.value = json.encode(sorted);
//     } else {
//       Map<String, dynamic> datalist = {};
//       String format =
//           DateFormat('yyyy-MM-ddTHH:mm:ss.SSS').format(DateTime.now());
//       datalist[jsonEncode(format)] = null;
//       stepDataGraph.value = json.encode(datalist);
//       data['value'] = stepDataPoint.value.value;
//       stepDataPoint.value = HealthItemModel.fromJson(data);
//       debugPrint("Authorization not granted");
//     }
//   }

//   Future<List<HealthDataPoint>?> fetchWorkoutDataTest() async {
//     final type = [HealthDataType.WORKOUT];
//     final permissions = [HealthDataAccess.READ];

//     final now = DateTime.now();
//     final midnight = DateTime(now.year, now.month, now.day);

//     bool requestedType1 =
//         await health.requestAuthorization(type, permissions: permissions);
//     debugPrint('CHECK HealthData, requestedType1:: $requestedType1');
//     if (requestedType1) {
//       try {
//         List<HealthDataPoint> healthData =
//             await health.getHealthDataFromTypes(midnight, now, type);
//         _workoutDataList.addAll((healthData.length < 100)
//             ? healthData
//             : healthData.sublist(0, 100));
//       } catch (error) {
//         debugPrint("Exception in getHealthDataFromTypes: $error");
//       }
//       _workoutDataList = HealthFactory.removeDuplicates(_workoutDataList);
//       return _workoutDataList;
//     } else {
//       debugPrint("Authorization not granted");
//     }
//     return null;
//   }

//   Future<bool> requestAppAuthorization() async {
//     bool isAuthorized = false;
//     List<HealthDataType> types = List<HealthDataType>.empty(growable: true);
//     List<HealthDataAccess> permissions =
//         List<HealthDataAccess>.empty(growable: true);
//     await Permission.activityRecognition.request();
//     await Permission.location.request();
//     if (Platform.isIOS) {
//       types.addAll([
//         HealthDataType.BLOOD_GLUCOSE,
//         HealthDataType.BODY_MASS_INDEX,
//         HealthDataType.HEART_RATE,
//         HealthDataType.BLOOD_PRESSURE_DIASTOLIC,
//         HealthDataType.BLOOD_PRESSURE_SYSTOLIC,
//         HealthDataType.STEPS,
//         HealthDataType.SLEEP_ASLEEP,
//         HealthDataType.SLEEP_AWAKE,
//         HealthDataType.SLEEP_IN_BED,
//         HealthDataType.ACTIVE_ENERGY_BURNED,
//         HealthDataType.BASAL_ENERGY_BURNED,
//         HealthDataType.WORKOUT
//       ]);
//       permissions.addAll([
//         HealthDataAccess.READ,
//         HealthDataAccess.READ,
//         HealthDataAccess.READ,
//         HealthDataAccess.READ,
//         HealthDataAccess.READ,
//         HealthDataAccess.READ,
//         HealthDataAccess.READ,
//         HealthDataAccess.READ,
//         HealthDataAccess.READ,
//         HealthDataAccess.READ,
//         HealthDataAccess.READ,
//         HealthDataAccess.READ
//       ]);
//     } else {
//       types.addAll([
//         HealthDataType.BLOOD_GLUCOSE,
//         HealthDataType.BODY_MASS_INDEX,
//         HealthDataType.HEART_RATE,
//         HealthDataType.BLOOD_PRESSURE_DIASTOLIC,
//         HealthDataType.BLOOD_PRESSURE_SYSTOLIC,
//         HealthDataType.STEPS,
//         HealthDataType.SLEEP_ASLEEP,
//         HealthDataType.SLEEP_AWAKE,
//         HealthDataType.SLEEP_IN_BED,
//         HealthDataType.ACTIVE_ENERGY_BURNED,
//         HealthDataType.WORKOUT
//       ]);
//       permissions.addAll([
//         HealthDataAccess.READ,
//         HealthDataAccess.READ,
//         HealthDataAccess.READ,
//         HealthDataAccess.READ,
//         HealthDataAccess.READ,
//         HealthDataAccess.READ,
//         HealthDataAccess.READ,
//         HealthDataAccess.READ,
//         HealthDataAccess.READ,
//         HealthDataAccess.READ,
//         HealthDataAccess.READ
//       ]);
//     }

//     isAuthorized =
//         await health.requestAuthorization(types, permissions: permissions);

//     return isAuthorized;
//   }

// ///////---------------------Garmin---------------------//////
//   Future<void> requestGarminToken() async {
//     try {
//       isDeviceLoading.value = true;
//       final ResponseModel response = await _client.post(
//         ApiConstant.getGarminRequestToken,
//       );
//       Map<String, dynamic> data = response.data!;
//       oauthToken.value = data[oauthToken].toString();
//       data.forEach((key, value) {
//         if (key == "oauthToken") {
//           oauthToken.value = value.toString();
//         }
//         if (key == "oauthTokenSecret") {
//           oauthTokenSecret.value = value.toString();
//         }
//       });
//       isDeviceLoading.value = false;
//     } on DioException catch (e) {
//       errorMessage.value = e.message ?? e.type.name;
//     }
//   }

//   Future<void> getGarminAccessToken() async {
//     try {
//       var memberId = await _userApp.getMemberId();
//       isDeviceLoading.value = true;
//       await _client.post(
//         ApiConstant.getGarminAccessToken,
//         queryParameters: {
//           'memberId': memberId,
//           'oauth_token': oauthToken,
//           'oauth_token_secret': oauthTokenSecret,
//           'oauth_verifier': oauthVerifier,
//         },
//       );
//       isDeviceLoading.value = false;
//     } on DioException catch (e) {
//       errorMessage.value = e.message ?? e.type.name;
//     }
//   }

//   Future<void> getGarminId() async {
//     try {
//       var memberId = await _userApp.getMemberId();
//       isDeviceLoading.value = true;
//       await _client.get(
//         ApiConstant.getGarminUserId,
//         queryParameters: {'memberId': memberId},
//       );
//       isDeviceLoading.value = false;
//     } on DioException catch (e) {
//       errorMessage.value = e.message ?? e.type.name;
//     }
//   }

//   Future<void> getStravaId() async {
//     try {
//       isDeviceLoading.value = true;
//       final ResponseModel response = await _client.post(
//         "https://www.strava.com/api/v3/oauth/token",
//         queryParameters: {
//           'client_id': dotenv.env['STRAVA_CLIENTID'],
//           'client_secret': dotenv.env['STRAVA_CLIENT_SECRET'],
//           'code': oauthVerifier.value,
//           'grant_type': 'authorization_code'
//         },
//       );
//       if (response.data!.isNotEmpty) {
//         String refresh = "";
//         String access = "";
//         String id = "";
//         response.data!.forEach((key, value) {
//           if (key == 'refresh_token') {
//             refresh = value.toString();
//           }
//           if (key == 'access_token') {
//             access = value.toString();
//           }
//           if (key == 'athlete') {
//             id = value.toString().substring(5, 14);
//           }
//         });
//         upsertDeviceInfo(
//             device: 'strava',
//             deviceId: id,
//             deviceAccessToken: access,
//             deviceRefreshToken: refresh);
//       }
//       isDeviceLoading.value = false;
//     } on DioException catch (e) {
//       errorMessage.value = e.message ?? 'An error occur';
//     }
//   }

//   Future<void> getGarminDataPoint() async {
//     Map<String, dynamic> pulseData = {};
//     Map<String, dynamic> burnData = {};
//     Map<String, dynamic> stepData = {};
//     pulseData['type'] = 'heart rate';
//     pulseData['unit'] = 'bpm';
//     pulseData['status'] = '';
//     burnData['type'] = 'burn';
//     burnData['unit'] = 'cal';
//     burnData['status'] = '';
//     stepData['type'] = 'step';
//     stepData['unit'] = 'steps';
//     stepData['status'] = '';
//     pulseDataPoint.value = HealthItemModel.fromJson(pulseData);
//     burnedCaloriesDataPoint.value = HealthItemModel.fromJson(burnData);
//     stepDataPoint.value = HealthItemModel.fromJson(stepData);
//     try {
//       final now = DateTime.now();
//       String start = DateFormat('yyyy-MM-ddTHH:mm:ss')
//           .format(DateTime(now.year, now.month, now.day));
//       String end = DateFormat('yyyy-MM-ddTHH:mm:ss')
//           .format(DateTime(now.year, now.month, now.day + 1, 0, 0, 0, 0, 0));

//       isDeviceLoading.value = true;
//       final ResponseModel response = await _client.get(
//         ApiConstant.getGarminDailyInfo,
//         queryParameters: {
//           'memberId': await _userApp.getMemberId(),
//           'start_datetime': start,
//           'end_datetime': end,
//         },
//       );
//       int? burnVal = 0;
//       int? basalVal = 0;
//       int? stepVal = 0;
//       int? heartVal = 0;
//       if (response.data!.isNotEmpty) {
//         response.data!.forEach((key, value) {
//           if (value != null) {
//             for (var item in value) {
//               burnVal = int.tryParse(item['activeKilocalories'].toString());
//               basalVal = int.tryParse(item['bmrKilocalories'].toString());
//               stepVal = int.tryParse(item['steps'].toString());
//               heartVal = int.tryParse(
//                   item['averageHeartRateInBeatsPerMinute'].toString());
//               var epoch = int.tryParse(item['startTimeInSeconds'].toString());
//               var duration = int.tryParse(item['durationInSeconds'].toString());
//               String date = DateFormat('yyyy-MM-ddTHH:mm:ss').format(
//                   DateTime.fromMillisecondsSinceEpoch(
//                           (epoch! + duration!) * 1000)
//                       .toLocal());
//               pulseData['last_update_time'] =
//                   DateTime.parse(date).millisecondsSinceEpoch;
//               burnData['last_update_time'] =
//                   DateTime.parse(date).millisecondsSinceEpoch;
//               stepData['last_update_time'] =
//                   DateTime.parse(date).millisecondsSinceEpoch;
//               pulseData['value'] = heartVal;
//               burnData['value'] = burnVal! + basalVal!;
//               stepData['value'] = stepVal;
//             }
//           }
//         });
//       }
//       pulseDataPoint.value = HealthItemModel.fromJson(pulseData);
//       burnedCaloriesDataPoint.value = HealthItemModel.fromJson(burnData);
//       stepDataPoint.value = HealthItemModel.fromJson(stepData);
//     } on DioException catch (e) {
//       errorMessage.value = e.message ?? e.type.name;
//     }
//   }

//   Future<void> getGarminPulseData() async {
//     try {
//       final now = DateTime.now();
//       String start = DateFormat('yyyy-MM-ddTHH:mm:ss')
//           .format(DateTime(now.year, now.month, now.day));
//       String end = DateFormat('yyyy-MM-ddTHH:mm:ss')
//           .format(DateTime(now.year, now.month, now.day + 1, 0, 0, 0, 0, 0));

//       isDeviceLoading.value = true;
//       final ResponseModel response = await _client.get(
//         ApiConstant.getGarminPulseInfo,
//         queryParameters: {
//           'memberId': await _userApp.getMemberId(),
//           'start_datetime': start,
//           'end_datetime': end,
//         },
//       );

//       Map<String, dynamic> map = {};
//       if (response.data!.isNotEmpty) {
//         response.data!.forEach((key, value) {
//           String date = DateFormat('yyyy-MM-ddTHH:mm:ss')
//               .format(DateTime.parse(key).toUtc());
//           map.addAll({date: value});
//         });
//       }
//       pulseDataGraph.value = json.encode(map);
//     } on DioException catch (e) {
//       errorMessage.value = e.message ?? e.type.name;
//     }
//   }

//   Future<void> getGarminStepData() async {
//     try {
//       final now = DateTime.now();
//       String start = DateFormat('yyyy-MM-ddTHH:mm:ss')
//           .format(DateTime(now.year, now.month, now.day));
//       String end = DateFormat('yyyy-MM-ddTHH:mm:ss')
//           .format(DateTime(now.year, now.month, now.day + 1, 0, 0, 0, 0, 0));

//       isDeviceLoading.value = true;
//       final ResponseModel response = await _client.get(
//         ApiConstant.getGarminStepInfo,
//         queryParameters: {
//           'memberId': await _userApp.getMemberId(),
//           'start_datetime': start,
//           'end_datetime': end
//         },
//       );

//       Map<String, dynamic> map = {};
//       if (response.data!.isNotEmpty) {
//         response.data!.forEach((key, value) {
//           String date = DateFormat('yyyy-MM-ddTHH:mm:ss')
//               .format(DateTime.parse(key).toLocal());
//           map.addAll({date: value});
//         });
//       }
//       stepDataGraph.value = json.encode(map);
//     } on DioException catch (e) {
//       errorMessage.value = e.message ?? e.type.name;
//     }
//   }

//   Future<void> getGarminBurnData() async {
//     try {
//       final now = DateTime.now();
//       String start = DateFormat('yyyy-MM-ddTHH:mm:ss')
//           .format(DateTime(now.year, now.month, now.day));
//       String end = DateFormat('yyyy-MM-ddTHH:mm:ss')
//           .format(DateTime(now.year, now.month, now.day + 1, 0, 0, 0, 0, 0));

//       isDeviceLoading.value = true;
//       final ResponseModel response = await _client.get(
//         ApiConstant.getGarminBurnInfo,
//         options: Options(contentType: Headers.formUrlEncodedContentType),
//         queryParameters: {
//           'memberId': await _userApp.getMemberId(),
//           'start_datetime': start,
//           'end_datetime': end,
//         },
//       );
//       Map<String, dynamic> map = {};
//       if (response.data!.isNotEmpty) {
//         response.data!.forEach((key, value) {
//           map.addAll({
//             DateFormat('yyyy-MM-ddTHH:mm')
//                 .format(DateTime.parse(key).toLocal()): value
//           });
//         });
//       }
//       burnDataGraph.value = json.encode(map);
//     } on DioException catch (e) {
//       errorMessage.value = e.message ?? e.type.name;
//     }
//   }

//   Future<void> getGarminSleepData() async {
//     Map<String, dynamic> data = {};
//     data['type'] = 'sleep';
//     data['unit'] = 'hours';
//     data['status'] = '';
//     sleepDataPoint.value = HealthItemModel.fromJson(data);
//     try {
//       final now = DateTime.now();
//       String start = DateFormat('yyyy-MM-ddTHH:mm:ss')
//           .format(DateTime(now.year, now.month, now.day));
//       String end = DateFormat('yyyy-MM-ddTHH:mm:ss')
//           .format(DateTime(now.year, now.month, now.day + 1, 0, 0, 0, 0, 0));

//       isDeviceLoading.value = true;
//       final ResponseModel response = await _client.get(
//         ApiConstant.getGarminSleepInfo,
//         queryParameters: {
//           'memberId': await _userApp.getMemberId(),
//           'start_datetime': start,
//           'end_datetime': end,
//         },
//       );
//       if (response.data!.isNotEmpty) {
//         response.data!.forEach((key, value) {
//           data['last_update_time'] = DateTime.parse(key).millisecondsSinceEpoch;
//           data['value'] = value;
//         });
//       }
//       sleepDataPoint.value = HealthItemModel.fromJson(data);
//       sleepDataGraph.value = json.encode(response.data);
//     } on DioException catch (e) {
//       errorMessage.value = e.message ?? e.type.name;
//     }
//   }

//   Future<void> updateDailyData() async {
//     //String date, int step, int pulse, int burn
//     if (deviceInfos.value!.device == 'apple') {
//       await fetchHeartRateGraph();
//       await fetchStepGraph();
//       await fetchBurnedCaloriesGraph();
//     } else if (deviceInfos.value!.device == 'garmin') {
//       await getGarminBurnData();
//       await getGarminPulseData();
//       await getGarminStepData();
//     } else if (deviceInfos.value!.device == 'google_fit') {
//       await fetchGoogleFitHeartRateGraph();
//       await fetchGoogleFitStepGraph();
//       await fetchGoogleFitBurnedCaloriesGraph();
//     }

//     double step = (stepDataPoint.value.value != null)
//         ? stepDataPoint.value.value as double
//         : 0;
//     double pulse = (pulseDataPoint.value.value != null)
//         ? pulseDataPoint.value.value as double
//         : 0;
//     double burn = (burnedCaloriesDataPoint.value.value != null)
//         ? burnedCaloriesDataPoint.value.value as double
//         : 0;
//     updateHealthData(
//         DateFormat('yyyy-MM-dd').format(DateTime(
//             DateTime.now().year, DateTime.now().month, DateTime.now().day)),
//         step.toInt(),
//         pulse.toInt(),
//         burn.toInt());
//   }

//   Future<void> fetchHealthData(String? device) async {
//     fetchBMIDatapoint();
//     switch (device) {
//       case "apple":
//         if (Platform.isIOS) {
//           if (await requestAppAuthorization()) {
//             fetchHeartRateGraph();
//             fetchBurnedCaloriesGraph();
//             fetchStepGraph();
//             fetchWorkoutDataTest();
//             updateDailyData();
//           }
//         }
//         break;
//       case "google_fit":
//         final types = [
//           HealthDataType.BLOOD_GLUCOSE,
//           HealthDataType.BODY_MASS_INDEX,
//           HealthDataType.HEART_RATE,
//           HealthDataType.BLOOD_PRESSURE_DIASTOLIC,
//           HealthDataType.BLOOD_PRESSURE_SYSTOLIC,
//           HealthDataType.STEPS,
//           HealthDataType.SLEEP_ASLEEP,
//           HealthDataType.SLEEP_AWAKE,
//           HealthDataType.SLEEP_IN_BED,
//           HealthDataType.ACTIVE_ENERGY_BURNED,
//           HealthDataType.WORKOUT
//         ];
//         final permissions = [
//           HealthDataAccess.READ,
//           HealthDataAccess.READ,
//           HealthDataAccess.READ,
//           HealthDataAccess.READ,
//           HealthDataAccess.READ,
//           HealthDataAccess.READ,
//           HealthDataAccess.READ,
//           HealthDataAccess.READ,
//           HealthDataAccess.READ,
//           HealthDataAccess.READ,
//           HealthDataAccess.READ
//         ];
//         var permission =
//             await health.hasPermissions(types, permissions: permissions);

//         if (permission == true) {
//           await fetchGoogleFitHeartRateGraph();
//           await fetchGoogleFitBurnedCaloriesGraph();
//           await fetchGoogleFitStepGraph();
//           await fetchWorkoutDataTest();
//           await updateDailyData();
//         } else {
//           await requestAppAuthorization();
//           Map<String, dynamic> data1 = {};
//           data1['type'] = 'heart rate';
//           data1['unit'] = 'bpm';
//           data1['status'] = 'too high/too low/normal';
//           pulseDataPoint.value = HealthItemModel.fromJson(data1);
//           Map<String, dynamic> data2 = {};
//           data2['type'] = 'step';
//           data2['unit'] = 'steps';
//           stepDataPoint.value = HealthItemModel.fromJson(data2);
//           Map<String, dynamic> data3 = {};
//           data3['type'] = 'burn';
//           data3['unit'] = 'kcal';
//           burnedCaloriesDataPoint.value = HealthItemModel.fromJson(data3);
//           Map<String, dynamic> data4 = {};
//           data4['type'] = 'sleep';
//           data4['unit'] = 'hours';
//           sleepDataPoint.value = HealthItemModel.fromJson(data4);
//         }
//         break;
//       case "garmin":
//         getGarminDataPoint();
//         getGarminBurnData();
//         getGarminPulseData();
//         getGarminStepData();
//         // getGarminSleepData();
//         updateDailyData();
//         break;
//       case null:
//       case "":
//       case "strava":
//         Map<String, dynamic> data1 = {};
//         data1['type'] = 'heart rate';
//         data1['unit'] = 'bpm';
//         data1['status'] = 'too high/too low/normal';
//         pulseDataPoint.value = HealthItemModel.fromJson(data1);
//         Map<String, dynamic> data2 = {};
//         data2['type'] = 'step';
//         data2['unit'] = 'steps';
//         stepDataPoint.value = HealthItemModel.fromJson(data2);
//         Map<String, dynamic> data3 = {};
//         data3['type'] = 'burn';
//         data3['unit'] = 'kcal';
//         burnedCaloriesDataPoint.value = HealthItemModel.fromJson(data3);
//         Map<String, dynamic> data4 = {};
//         data4['type'] = 'sleep';
//         data4['unit'] = 'hours';
//         sleepDataPoint.value = HealthItemModel.fromJson(data4);
//         break;
//       default:
//         Map<String, dynamic> data1 = {};
//         data1['type'] = 'heart rate';
//         data1['unit'] = 'bpm';
//         data1['status'] = 'too high/too low/normal';
//         pulseDataPoint.value = HealthItemModel.fromJson(data1);
//         Map<String, dynamic> data2 = {};
//         data2['type'] = 'step';
//         data2['unit'] = 'steps';
//         stepDataPoint.value = HealthItemModel.fromJson(data2);
//         Map<String, dynamic> data3 = {};
//         data3['type'] = 'burn';
//         data3['unit'] = 'kcal';
//         burnedCaloriesDataPoint.value = HealthItemModel.fromJson(data3);
//         Map<String, dynamic> data4 = {};
//         data4['type'] = 'sleep';
//         data4['unit'] = 'hours';
//         sleepDataPoint.value = HealthItemModel.fromJson(data4);
//         break;
//     }
//   }
// }
import 'dart:io';

import 'package:appcheck/appcheck.dart';
import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:health/health.dart';
import 'package:intl/intl.dart';
import 'package:samitivej_flutter_app/apps/auth/auth_app.dart';
import 'package:samitivej_flutter_app/apps/user/user_app.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/models/device_info_model.dart';
import 'package:samitivej_flutter_app/models/health_item_model.dart';
import 'package:samitivej_flutter_app/models/response_model.dart';
import 'package:samitivej_flutter_app/services/api_client.dart';
import 'package:samitivej_flutter_app/services/storage_service.dart';

class HealthApp extends GetxController {
  // HealthFactory health = HealthFactory();
  bool isFitInstalled = false;
  List<HealthDataPoint> _pulseDataList = [];
  List<HealthDataPoint> _stepDataList = [];
  List<HealthDataPoint> _burnCaloriesSubDataList1 = [];
  List<HealthDataPoint> _burnCaloriesSubDataList2 = [];
  List<HealthDataPoint> _workoutDataList = [];

  final ApiClient _client = ApiClient(baseUrl: ApiConstant.fitDomain);
  final UserApp _userApp = Get.find<UserApp>();
  final AuthApp authApp = Get.find<AuthApp>();
  final storage = StorageService();

  Rx<HealthItemModel> pulseDataPoint = Rx(HealthItemModel());
  Rx<HealthItemModel> stepDataPoint = Rx(HealthItemModel());
  Rx<HealthItemModel> burnedCaloriesDataPoint = Rx(HealthItemModel());
  Rx<HealthItemModel> sleepDataPoint = Rx(HealthItemModel());
  Rx<HealthItemModel> bmiDataPoint = Rx(HealthItemModel());
  Rx<HealthItemModel> heightDataPoint = Rx(HealthItemModel());
  Rx<HealthItemModel> weightDataPoint = Rx(HealthItemModel());
  RxString pulseDataGraph = ''.obs;
  RxString stepDataGraph = ''.obs;
  RxString burnDataGraph = ''.obs;
  RxString sleepDataGraph = ''.obs;

  RxString errorMessage = ''.obs;

  RxBool isDeviceLoading = false.obs;
  RxBool isDeviceConnected = false.obs;
  RxnBool isGrantPermission = RxnBool();
  RxBool isLoadingHealthData = false.obs;
  RxBool askPermissionFirstTime = RxBool(true);
  RxBool hasPermission = false.obs;

  RxList<DeviceInfoModel> deviceInfo = RxList.empty();
  Rx<DeviceInfoModel?> deviceInfos = Rx(null);
  RxString deviceStatus = ''.obs;
  RxString oauthToken = ''.obs;
  RxString oauthTokenSecret = ''.obs;
  RxString oauthVerifier = ''.obs;
  RxInt count = 0.obs;

  @override
  void onInit() {
    isGrantPermission.value = storage.read<bool>('granthealthapp');
    checkApp();
    super.onInit();
  }

  Future<void> setUserPermission(bool value) async {
    await storage.write('granthealthapp', value);
    askPermissionFirstTime.value = false;
    isGrantPermission.value = value;
  }

  bool get needUserPermission {
    bool? grantPermission = isGrantPermission.value;
    return deviceStatus.value == "apple" &&
        Platform.isIOS &&
        (grantPermission == null || !grantPermission);
  }

  Future<void> checkApp() async {
    AppInfo? installedApps;

    if (Platform.isAndroid) {
      const package = "com.google.android.apps.fitness";
      try {
        installedApps = await AppCheck().checkAvailability(package);
        if (installedApps != null) {
          isFitInstalled = true;
        }
      } catch (_) {
        isFitInstalled = false;
      }
    }
  }

  Future<void> upsertDeviceInfo(
      {String? device,
      String? deviceId,
      String? deviceAccessToken,
      String? deviceAccessTokenSecret,
      String? deviceRefreshToken}) async {
    try {
      authApp;
      isDeviceLoading.value = true;
      await _client.post(
        ApiConstant.upsertFitUser,
        data: {
          "createFitUserRequestBean": {
            'birthDate': '',
            'height': 0,
            'weight': 0,
            'pace': 0,
            'device': device,
            'deviceId': deviceId,
            'deviceAccessToken': deviceAccessToken,
            'deviceAccessTokenSecret': deviceAccessTokenSecret,
            'deviceRefreshToken': deviceRefreshToken
          }
        },
      );
      isDeviceLoading.value = false;
    } on DioException catch (e) {
      errorMessage.value = e.message ?? e.type.name;
    }
  }

  Future<void> updateDeviceInfo(
      {String? device,
      String? deviceId,
      String? deviceAccessToken,
      String? deviceAccessTokenSecret,
      String? deviceRefreshToken}) async {
    try {
      authApp;
      isDeviceLoading.value = true;
      await _client.post(
        ApiConstant.updateFitDevice,
        data: {
          "updateDeviceInfoRequestBean": {
            'device': device,
            'deviceId': deviceId,
            'deviceAccessToken': deviceAccessToken,
            'deviceAccessTokenSecret': deviceAccessTokenSecret,
            'deviceRefreshToken': deviceRefreshToken
          }
        },
      );
      isDeviceLoading.value = false;
    } on DioException catch (e) {
      errorMessage.value = e.message ?? e.type.name;
    }
  }

  Future<void> getDeviceInfo({int retry = 1}) async {
    try {
      isDeviceLoading.value = true;
      final ResponseModel response = await _client.post(
        ApiConstant.getDeviceInfo,
      );
      deviceInfos.value = DeviceInfoModel.fromJson(response.data!);
      if (deviceInfos.value!.device != null) {
        isDeviceConnected.value = deviceInfos.value!.device!.isNotEmpty;
        deviceStatus.value = deviceInfos.value!.device!;
        isDeviceLoading.value = false;
      } else {
        isDeviceConnected.value = false;
        isDeviceLoading.value = false;
      }
    } on DioException catch (e) {
      deviceInfo.value = RxList.empty();
      isDeviceConnected.value = false;
      errorMessage.value = e.message ?? e.type.name;
    } catch (e) {
      if (deviceInfos.value!.device == null && retry > 0) {
        await upsertDeviceInfo();
        await getDeviceInfo(retry: retry - 1);
      }
    }
  }

  Future<void> fetchBMIDatapoint() async {
    Map<String, dynamic> data1 = {};
    Map<String, dynamic> data2 = {};
    Map<String, dynamic> data3 = {};
    data1['type'] = 'bmi';
    data2['type'] = 'height';
    data3['type'] = 'weight';
    data2['unit'] = 'cm';
    data3['unit'] = 'kg';
    bmiDataPoint.value = HealthItemModel.fromJson(data1);
    heightDataPoint.value = HealthItemModel.fromJson(data2);
    weightDataPoint.value = HealthItemModel.fromJson(data3);
    try {
      isDeviceLoading.value = true;
      final ResponseModel response = await _client.post(ApiConstant.getBMI);
      response.data!.forEach((key, value) {
        if (key == 'height') {
          data2['value'] = value;
        }
        if (key == 'weight') {
          data3['value'] = value;
        }
        if (key == 'bmi') {
          data1['value'] = value;
        }
        if (key == 'date') {
          if (value != null) {
            final format = DateFormat('yyyy-MM-ddTHH:mm:ss');
            int time = format.parse(value.toString()).millisecondsSinceEpoch;
            data1['last_update_time'] = time;
            data2['last_update_time'] = time;
            data3['last_update_time'] = time;
          }
        }
      });
      bmiDataPoint.value = HealthItemModel.fromJson(data1);
      heightDataPoint.value = HealthItemModel.fromJson(data2);
      weightDataPoint.value = HealthItemModel.fromJson(data3);
      isDeviceLoading.value = false;
    } on DioException catch (e) {
      errorMessage.value = e.message ?? e.type.name;
    }
  }

  Future<void> updateHealthData(
      String date, int step, int pulse, int burn) async {
    try {
      isDeviceLoading.value = true;
      await _client.post(ApiConstant.upsertHealthData, data: {
        "healthRequestBean": {
          "date": date,
          "step": step,
          "pulse": pulse,
          "burnedCalorie": burn,
          "stress": null,
          "sleep": null
        }
      });
      isDeviceLoading.value = false;
    } on DioException catch (e) {
      errorMessage.value = e.message ?? e.type.name;
    }
  }

  Future<void> updateDailyData() async {
    double step = (stepDataPoint.value.value != null)
        ? stepDataPoint.value.value as double
        : 0;
    double pulse = (pulseDataPoint.value.value != null)
        ? pulseDataPoint.value.value as double
        : 0;
    double burn = (burnedCaloriesDataPoint.value.value != null)
        ? burnedCaloriesDataPoint.value.value as double
        : 0;
    updateHealthData(
        DateFormat('yyyy-MM-dd').format(DateTime(
            DateTime.now().year, DateTime.now().month, DateTime.now().day)),
        step.toInt(),
        pulse.toInt(),
        burn.toInt());
  }

  Future<void> fetchHealthData(String? device) async {
    fetchBMIDatapoint();
    switch (device) {
      default:
        Map<String, dynamic> data1 = {};
        data1['type'] = 'heart rate';
        data1['unit'] = 'bpm';
        data1['status'] = 'too high/too low/normal';
        pulseDataPoint.value = HealthItemModel.fromJson(data1);
        Map<String, dynamic> data2 = {};
        data2['type'] = 'step';
        data2['unit'] = 'steps';
        stepDataPoint.value = HealthItemModel.fromJson(data2);
        Map<String, dynamic> data3 = {};
        data3['type'] = 'burn';
        data3['unit'] = 'kcal';
        burnedCaloriesDataPoint.value = HealthItemModel.fromJson(data3);
        Map<String, dynamic> data4 = {};
        data4['type'] = 'sleep';
        data4['unit'] = 'hours';
        sleepDataPoint.value = HealthItemModel.fromJson(data4);
        break;
    }
  }
}
