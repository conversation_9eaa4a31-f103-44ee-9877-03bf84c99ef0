import 'dart:developer' as developer;
import 'dart:io';

import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:samitivej_flutter_app/apps/guardian/guardian_controller.dart';
import 'package:samitivej_flutter_app/apps/user/user_app.dart';
import 'package:samitivej_flutter_app/commons/extensions/date_time_extensions.dart';
import 'package:samitivej_flutter_app/components/custom_alert_dialog.dart';
import 'package:samitivej_flutter_app/components/custom_button.dart';
import 'package:samitivej_flutter_app/components/custom_dialog.dart';
import 'package:samitivej_flutter_app/constants/app_color.dart';
import 'package:samitivej_flutter_app/constants/app_layout.dart';
import 'package:samitivej_flutter_app/constants/app_text.dart';
import 'package:samitivej_flutter_app/data/models/appointment/doctor_bookable_info_model.dart';
import 'package:samitivej_flutter_app/data/models/guardian/ward_hn_model.dart';
import 'package:samitivej_flutter_app/localization/app_translate_key.dart';
import 'package:samitivej_flutter_app/models/all_type_appointment_model.dart';
import 'package:samitivej_flutter_app/models/appointment_patient_model.dart';
import 'package:samitivej_flutter_app/models/doctor_detail_model.dart';
import 'package:samitivej_flutter_app/models/doctor_model.dart';
import 'package:samitivej_flutter_app/models/doctor_slot_model.dart';
import 'package:samitivej_flutter_app/models/hospital_model.dart';
import 'package:samitivej_flutter_app/models/interpreter_model.dart';
import 'package:samitivej_flutter_app/models/nationality_model.dart';
import 'package:samitivej_flutter_app/models/speciality_model.dart';
import 'package:samitivej_flutter_app/services/amplify_service.dart';
import 'package:samitivej_flutter_app/services/appointment/appointment_service.dart';
import 'package:samitivej_flutter_app/services/appointment/doctor_service.dart';
import 'package:samitivej_flutter_app/utils/exception_handle.dart';
import 'package:samitivej_flutter_app/utils/utils.dart';

class AppointmentApp extends GetxController {
  final Rx<List<SpecialityModel>> specialtyList = Rx([]);
  final Rx<List<HospitalModel>> hospitalList = Rx([]);
  final Rx<List<InterpreterModel>> interpretationLanguage = Rx([]);
  final Rx<List<NationalityModel>> nationalities = Rx([]);
  final Rx<DoctorModel?> selectedDoctor = Rx(null);
  // suggest doctor -------------
  final Rx<HospitalModel?> selectedHospital = Rx(null);
  final Rx<String?> selectedAppointmentType = Rx(null);
  final Rx<String?> selectedSpecialties = Rx(null);
  final Rx<bool> selectedRecommendDoctor = Rx(true);

  final Rx<DateTime?> selectedAppontmentDate = Rx(null);
  final Rx<String?> selectedAppontmentTime = Rx(null);

  final Rx<bool?> isSuggestValidated = Rx(null);

  bool get isSuggestValid => !isHospitalInValid;

  bool get isBookAppointmentValid =>
      !isAppointmentTypeInValid && selectedDoctor.value != null;

  bool get isHospitalInValid => selectedHospital.value == null;
  bool get isAppointmentTypeInValid => selectedAppointmentType.value == null;
  bool get isSpecialtieInValid => selectedSpecialties.value == null;

  // create appointment -------------
  final Rx<AppointmentPatientModel?> patient = Rx(null);
  final Rx<bool> isPatirentHN = Rx<bool>(false);
  final Rx<bool?> isAleadyMeetDoctor = Rx(null);
  final Rx<bool?> isCreateForSelf = Rx(null);
  final Rx<String?> name = Rx(null);
  final Rx<DateTime?> dateOfBirth = Rx(null);
  final Rx<String?> phone = Rx(null);
  final Rx<String?> symptoms = Rx(null);
  final Rx<List<File>> symptomsImages = Rx([]);
  final Rx<bool> isInterpretation = Rx(false);
  final Rx<InterpreterModel?> interpretation = Rx(null);
  final Rx<WardHnModelList?> wardHns = Rx<WardHnModelList?>(null);

  final Rx<bool?> isCreateValidated = Rx(null);
  bool get isCreateValid =>
      !isAleadyMeetDoctorInvalid &&
      !isCreateForSelfInvalid &&
      !isNameInValid &&
      !isDateOfBirthInValid &&
      !isPhoneInValid &&
      !isPhoneFormatInValid &&
      !isAppointmentDateInValid &&
      !isAppointmentTimeInValid;

  bool get isAleadyMeetDoctorInvalid => isAleadyMeetDoctor.value == null;
  bool get isCreateForSelfInvalid => isCreateForSelf.value == null;
  bool get isNameInValid => name.value == null || name.value!.isEmpty;

  bool get isDateOfBirthInValid => dateOfBirth.value == null;
  bool get isPhoneInValid => phone.value == null || phone.value!.isEmpty;
  bool get isPhoneFormatInValid {
    final regExp = RegExp(r'[0]{1}[0-9]{8,9}');
    return !regExp.hasMatch(phone.value!);
  }

  bool? get isAppointmentTypeHospital => selectedAppointmentType.value != null
      ? selectedAppointmentType.value == 'Hospital Visit'
      : null;

  bool get isAppointmentDateInValid => selectedAppontmentDate.value == null;
  bool get isAppointmentTimeInValid => selectedAppontmentTime.value == null;

  DoctorSlotTimeModel? get selectedSlotTime => getDoctorSlotFromDateTime(
        selectedAppontmentDate.value,
        selectedAppontmentTime.value,
        selectedDoctor.value?.slot ?? [],
      );

  Rx<String?> hospitalContact = Rx<String?>(null);
  Rx<String?> referrer = Rx<String?>(null);

  static List<DoctorSlotTimeModel> doctorTimeFiltering(
    DateTime? date,
    List<DoctorSlotModel> doctorSlot,
  ) {
    if (date == null) return [];
    try {
      final dateSelected = doctorSlot
          .firstWhere((e) => e.date == DateFormat('yyyy-MM-dd').format(date));
      return dateSelected.times ?? [];
    } catch (_) {
      return [];
    }
  }

  DoctorSlotTimeModel? getDoctorSlotFromDateTime(
    DateTime? date,
    String? time,
    List<DoctorSlotModel> doctorSlot,
  ) {
    if (date == null || time == null) return null;
    try {
      final dateSelected = doctorSlot
          .firstWhere((e) => e.date == DateFormat('yyyy-MM-dd').format(date));

      final timeSelected = dateSelected.times
          ?.firstWhere((e) => e.time == time || e.startTime == time);
      return timeSelected;
    } catch (_) {
      return null;
    }
  }

  void clearAppointmentCreate({bool onlyForm = false}) {
    if (!onlyForm) {
      isAleadyMeetDoctor.value = null;
      isCreateForSelf.value = null;
    }

    name.value = null;
    dateOfBirth.value = null;
    phone.value = null;
    symptoms.value = null;
    symptomsImages.value = [];
    isInterpretation.value = false;
    interpretation.value = null;
    isCreateValidated.value = null;
  }

  Future<void> getSpecialtyList() async {
    try {
      final resp = await AppointmentService.getSpecialtyList(
        site: selectedHospital.value?.hospitalCode,
        isHospital: isAppointmentTypeHospital,
      );
      specialtyList.value = resp;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> getHospitalList() async {
    try {
      final resp = await AppointmentService.getHospital();
      hospitalList.value = resp;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> getinterpretationLanguage() async {
    try {
      if (interpretationLanguage.value.isNotEmpty) return;
      final resp = await AppointmentService.getInterpreterList();
      interpretationLanguage.value = resp;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> getNationalities() async {
    try {
      if (nationalities.value.isNotEmpty) return;
      final resp = await AppointmentService.getNationalities();
      nationalities.value = resp;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> getDoctorDetail(DoctorModel doctor) async {
    try {
      late DoctorDetailModel? doctorDetail;
      late List<DoctorSlotModel> doctorSlot;
      late bool? isVirtual;

      final dateFormat = DateFormat('yyyy-MM-dd');
      final c = selectedAppontmentDate.value ?? DateTime.now();

      await Future.wait([
        (() async {
          doctorDetail = await DoctorService.getDoctorDetail(
            site: 'ALL',
            doctorId: doctor.drId,
          );
        })(),
        (() async {
          doctorSlot = await DoctorService.getDoctorTimeAvalible(
            site: doctor.site,
            doctorId: doctor.drId,
            dateFrom: dateFormat.format(DateTime(c.year, c.month, c.day)),
            dateTo: dateFormat.format(DateTime(c.year, c.month + 1, c.day)),
          );
        })(),
        (() async {
          isVirtual = await DoctorService.isDoctorVirtual(
            doctorId: doctor.drId,
          );
        })()
      ]);

      final newDoctor = doctor;
      newDoctor.isVirtual = isVirtual;
      newDoctor.detail = doctorDetail;
      newDoctor.slot = doctorSlot;
      selectedDoctor.value = newDoctor;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> getHospitalContact() async {
    try {
      if (selectedDoctor.value == null) {
        throw CommonException('Doctor not found');
      }

      final String result = await AppointmentService.getHospitalContact(
        hospitalId: selectedDoctor.value!.site,
        specialtyCode: selectedSpecialties.value,
      );
      hospitalContact.value = result;
    } catch (_) {
      rethrow;
    }
  }

  Future<void> favoriteDoctor({
    required String doctorId,
    required String site,
    required bool isFav,
  }) async {
    try {
      isFav
          ? await DoctorService.favoriteDoctor(
              doctorId: doctorId,
              site: site,
            )
          : await DoctorService.unFavoriteDoctor(
              doctorId: doctorId,
              site: site,
            );
    } catch (e) {
      rethrow;
    }
  }

  Future<DoctorBookableInfoModel> getDoctorBookableInfo({
    required String doctorId,
    required String site,
  }) async {
    try {
      final result = await AppointmentService.getDoctorBookableInfo(
        doctorId: doctorId,
        site: site,
      );
      return result;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('getDoctorBookableInfo: $e');
      }
      return DoctorBookableInfoModel(
        doctorId: doctorId,
        site: site,
        priorHour: 0,
        allowBook: false,
      );
    }
  }

  Future<void> getAppointmentPatient() async {
    try {
      final resultPatient = await AppointmentService.getAppointmentPatient();
      final isPatientHn = await AppointmentService.getIsPatientHN();
      patient.value = resultPatient;
      isPatirentHN.value = isPatientHn;
    } catch (e) {
      rethrow;
    }
  }

  Future<String> fileToUrl(File e) async {
    final userApp = Get.find<UserApp>();

    final nameString = AppUtils.getRandomString(24);
    final imagePath =
        'appointment/${userApp.cognitoUserInfo?.value.userId}/$nameString.png';
    final imageUrl = await AmplifyService.uploadFile(
      e,
      fileName: imagePath,
      accessLevel: StorageAccessLevel.guest,
    );
    return imageUrl?.url ?? '';
  }

  Future<AllTypeAppointmentModel?> createAppointment() async {
    try {
      final doctor = selectedDoctor.value;
      if (doctor == null) throw CommonException('Doctor not found');

      final formattedDateOfBirth = dateOfBirth.value != null
          ? DateFormat('yyyy-MM-dd').format(dateOfBirth.value!)
          : '';

      final appointmentDate = selectedAppontmentDate.value ?? DateTime.now();
      final appointmentTime =
          selectedAppontmentTime.value?.split('-') ?? ['00:00', '23:59'];
      final startMappedTime = DateFormat('HH:mm').parse(appointmentTime.first);
      final endMappedTime = DateFormat('HH:mm').parse(appointmentTime.last);

      final startAppointment = DateTime(
        appointmentDate.year,
        appointmentDate.month,
        appointmentDate.day,
        startMappedTime.hour,
        startMappedTime.minute,
      );
      final endAppointment = DateTime(
        appointmentDate.year,
        appointmentDate.month,
        appointmentDate.day,
        endMappedTime.hour,
        endMappedTime.minute,
      );

      String formatedPhone = '';
      if (phone.value != null && phone.value!.startsWith('+')) {
        formatedPhone = '0${phone.value!.substring(3, phone.value!.length)}';
      }
      if (phone.value != null && phone.value!.startsWith('0')) {
        formatedPhone = phone.value!;
      }

      final dateFormat = DateFormat('yyyy-MM-ddTHH:mm:ss');
      final listImages = <String>[];
      for (var e in symptomsImages.value) {
        var imgUrl = await fileToUrl(e);
        listImages.add(imgUrl);
      }
      final result = await AppointmentService.createAppointment(
        doctorId: doctor.drId,
        isAleadyMeetDoctor: isAleadyMeetDoctor.value ?? false,
        isCreateForSelf: isCreateForSelf.value ?? false,
        name: name.value ?? '',
        phone: formatedPhone,
        dateOfBirth: formattedDateOfBirth,
        site: doctor.site,
        symptoms: symptoms.value ?? '',
        symptomImgUrl: listImages,
        isHospital: isAppointmentTypeHospital ?? true,
        startAppointmentDateTime: dateFormat.format(startAppointment),
        endAppointmentDateTime: dateFormat.format(endAppointment),
        isInterpretation: isInterpretation.value,
        interpretation: interpretation.value?.interpreterCode ?? '',
        doctorLocation: selectedSlotTime?.location ?? '',
      );
      return result;
    } catch (_) {
      rethrow;
    }
  }

  Future<AllTypeAppointmentModel?> bookingAppointment() async {
    try {
      final doctor = selectedDoctor.value;
      if (doctor == null) throw CommonException('Doctor not found');
      if (selectedSlotTime?.slotId == null) {
        throw CommonException(
            'Selected slot no slot id: ${selectedSlotTime?.toJson()}');
      }

      final formattedDateOfBirth = dateOfBirth.value != null
          ? DateFormat('yyyy-MM-dd').format(dateOfBirth.value!)
          : '';

      final appointmentDate = selectedAppontmentDate.value ?? DateTime.now();
      final appointmentTime =
          selectedAppontmentTime.value?.split('-') ?? ['00:00', '23:59'];
      final startMappedTime = DateFormat('HH:mm').parse(appointmentTime.first);
      final endMappedTime = DateFormat('HH:mm').parse(appointmentTime.last);

      final startAppointment = DateTime(
        appointmentDate.year,
        appointmentDate.month,
        appointmentDate.day,
        startMappedTime.hour,
        startMappedTime.minute,
      );
      final endAppointment = DateTime(
        appointmentDate.year,
        appointmentDate.month,
        appointmentDate.day,
        endMappedTime.hour,
        endMappedTime.minute,
      );

      String formatedPhone = '';
      if (phone.value != null && phone.value!.startsWith('+')) {
        formatedPhone = '0${phone.value!.substring(3, phone.value!.length)}';
      }
      if (phone.value != null && phone.value!.startsWith('0')) {
        formatedPhone = phone.value!;
      }

      final dateFormat = DateFormat('yyyy-MM-ddTHH:mm:ss');
      final listImages = <String>[];
      for (var e in symptomsImages.value) {
        var imgUrl = await fileToUrl(e);
        listImages.add(imgUrl);
      }
      final result = await AppointmentService.bookingAppointment(
        slotId: selectedSlotTime?.slotId ?? '',
        doctorId: doctor.drId,
        isAlreadyMeetDoctor: isAleadyMeetDoctor.value ?? false,
        isCreateForSelf: isCreateForSelf.value ?? false,
        name: name.value ?? '',
        phone: formatedPhone,
        dateOfBirth: formattedDateOfBirth,
        site: doctor.site,
        symptoms: symptoms.value ?? '',
        symptomImgUrl: listImages,
        isHospital: isAppointmentTypeHospital ?? true,
        appointmentDate: dateFormat.format(appointmentDate),
        appointmentTime: appointmentTime.first,
        startAppointmentDateTime: dateFormat.format(startAppointment),
        endAppointmentDateTime: dateFormat.format(endAppointment),
        isInterpretation: isInterpretation.value,
        interpretation: interpretation.value?.interpreterCode ?? '',
        locationCode: selectedSlotTime?.locationCode ?? '',
        doctorLocation: selectedSlotTime?.location ?? '',
      );
      return result;
    } catch (_) {
      rethrow;
    }
  }

  Future<DoctorModel?> getRecommendedDoctor({
    String? specialityCode,
    DateTime? date,
    String? site,
    bool? isVirtual,
    String? gender,
    required BuildContext context,
  }) async {
    try {
      final resp = await DoctorService.getRecommendedDoctorList(
          specialityCode: specialityCode,
          date: date,
          site: site,
          gender: gender);
      if (resp.isEmpty) {
        return null;
      } else {
        return resp.first;
      }
    } catch (e) {
      Future.sync(() => Get.dialog<void>(noDoctorSlotDialog(context)));
      rethrow;
    }
  }

  Future<DoctorModel?> getDoctorWithTimeSlots(
      List<DoctorModel> doctorList, DateTime? date) async {
    DoctorModel? selectedDoctor;
    for (var doctor in doctorList) {
      List<DoctorSlotModel> doctorSlotList;
      try {
        doctorSlotList = await DoctorService.getDoctorTimeAvalible(
            site: doctor.site, doctorId: doctor.drId);
      } catch (e) {
        doctorSlotList = [];
      }
      if (doctorSlotList.isNotEmpty) {
        selectedDoctor ??= doctor;
        if (date == null) {
          selectedDoctor = doctor;
          break;
        }
        for (var doctorSlot in doctorSlotList) {
          DateTime slotDateTime = DateTime.tryParse(doctorSlot.date!)!;
          try {
            if (date.isSameDate(slotDateTime)) {
              selectedDoctor = doctor;
              break;
            }
          } catch (e) {
            rethrow;
          }
        }
        break;
      }
    }
    selectedDoctor ??= doctorList.first;

    return selectedDoctor;
  }

  // Future<bool> deleteAppointment() async {
  //   try {
  //     var data = await AppointmentService.deleteAppointment(appointmentId);
  //     if (data != null) {
  //       return true;
  //     } else {
  //       return false;
  //     }
  //   } catch (_) {
  //     return false;
  //   }
  // }

  CustomDialog noDoctorSlotDialog(BuildContext context) {
    return CustomDialog(
      insetPadding: const EdgeInsets.symmetric(
        horizontal: 50,
        vertical: 24,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Column(
            children: [
              Padding(
                  padding: EdgeInsets.only(top: 2.5.h, left: 5.w, right: 5.w),
                  child: Column(
                    children: [
                      Padding(
                          padding: EdgeInsets.only(bottom: 12.px),
                          child: Text(
                            AppLocalizations.of(context)?.sorry ?? '',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                fontSize: AppText.theme(context)
                                    .titleMedium
                                    ?.fontSize,
                                fontFamily: AppText.theme(context)
                                    .titleMedium
                                    ?.fontFamily,
                                fontWeight: AppText.theme(context)
                                    .titleMedium
                                    ?.fontWeight,
                                color: AppColor.red400),
                          )),
                      Text(
                        AppLocalizations.of(context)?.doctorSlotNotFound ?? "",
                        textAlign: TextAlign.center,
                        style: AppText.theme(context).bodyMedium,
                      ),
                    ],
                  )),
              AppLayout.mediumH,
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (selectedHospital.value != null)
                    CustomButton(
                      width: 20.h,
                      label: selectedHospital.value?.hospitalTel,
                      onPressed: () async {
                        final result = await AppUtils.makePhoneCall(
                          (selectedHospital.value?.hospitalTel ?? '')
                              .split(RegExp(r'[\s()\-]'))
                              .join(),
                        );
                        if (!result) {
                          await Get.dialog(CustomAlertDialog(
                              title: AppTranslateKey.somethingwentwrong,
                              description: AppTranslateKey.pleasetryagainlater,
                              onButtonPressed: () => {
                                    Get.until((route) =>
                                        !(Get.isDialogOpen ?? false)),
                                  }));
                        }
                      },
                      borderRadiusSize: 12.5.sp,
                    ),
                  if (selectedHospital.value != null) AppLayout.mediumW,
                  CustomButton(
                    width: 10.h,
                    label: AppTranslateKey.close,
                    onPressed: () => Get.back<void>(),
                    borderRadiusSize: 12.5.sp,
                  ),
                ],
              ),
              AppLayout.mediumH,
            ],
          ),
        ],
      ),
    );
  }

  // Constants for sites that allow same-day booking
  static const Set<String> _sameDayBookingSites = {'SVH', 'SNH'};
  static const Duration _standardAdvanceBookingDuration = Duration(hours: 24);

  /// Returns the earliest date available for appointment selection.
  ///
  /// Same-day booking is allowed for:
  /// - Physical appointments at SVH or SNH sites
  ///
  /// All other appointments require 24-hour advance booking:
  /// - Virtual appointments (regardless of site)
  /// - Physical appointments at other sites
  DateTime getFirstDayForSelectAppointmentDate({
    String? site,
    bool? isVirtual,
  }) {
    final bool isVirtualAppointment = _determineIfVirtualAppointment(isVirtual);
    final bool isSameDayBookingSite = _isSameDayBookingSite(site);
    final bool hasHnOnSameDayBookingSite = _hasHnOnSameDayBookingSite(site);

    return _canBookSameDay(isSameDayBookingSite, isVirtualAppointment,
            hasHnOnSameDayBookingSite)
        ? DateTime.now()
        : DateTime.now().add(_standardAdvanceBookingDuration);
  }

  /// Determines if the appointment is virtual based on the isVirtual parameter
  /// or falls back to checking if it's not a hospital appointment
  bool _determineIfVirtualAppointment(bool? isVirtual) {
    return isVirtual ?? !(isAppointmentTypeHospital ?? true);
  }

  /// Checks if the given site allows same-day booking
  bool _isSameDayBookingSite(String? site) {
    return site != null && _sameDayBookingSites.contains(site);
  }

  bool _hasHnOnSameDayBookingSite(String? site) {
    if (site == null) return false;
    if (GuardianController().currentGuardianId.isParent) {
      final userApp = Get.find<UserApp>();
      final matchedActiveHn = userApp.plusUserData.value?.customerHn
          .firstWhereOrNull(
              (hnModel) => hnModel.active && hnModel.site == site);
      if (matchedActiveHn != null) {
        return _sameDayBookingSites.contains(matchedActiveHn.site);
      }
    } else {
      //Check HN of guardian ward
      final matchedActiveHn =
          wardHns.value?.activeWards.firstWhereOrNull((hn) => hn.site == site);
      if (matchedActiveHn != null) {
        return _sameDayBookingSites.contains(matchedActiveHn.site);
      }
    }
    return false;
  }

  /// Determines if same-day booking is allowed based on site and appointment type
  bool _canBookSameDay(bool isSameDayBookingSite, bool isVirtualAppointment,
      bool hasHnOnSameDayBookingSite) {
    bool isBookingForSelf = isCreateForSelf.value ?? true;
    return isSameDayBookingSite &&
        !isVirtualAppointment &&
        isBookingForSelf &&
        hasHnOnSameDayBookingSite;
  }

  /// Validates the currently selected appointment date against the first allowed date
  /// and resets it to null if the selected date is before the allowed date.
  ///
  /// This ensures that users cannot have an appointment date that is before
  /// the minimum allowed date based on the selected doctor's site.
  void validateAndResetInvalidAppointmentDate() {
    final firstDayForSelect = getFirstDayForSelectAppointmentDate(
      site: selectedDoctor.value?.site,
    );
    final selectedDate = selectedAppontmentDate.value;

    if (selectedDate != null && selectedDate.isBeforeDate(firstDayForSelect)) {
      selectedAppontmentDate.value = null;
    }
  }

  Future<void> loadAppointmentWardHns() async {
    if (GuardianController().currentGuardianId.isGuardian) {
      try {
        wardHns.value = await AppointmentService.getWardHns(
            GuardianController().currentGuardianId.value);
      } catch (e) {
        developer.log('Error loading appointment ward HNs: $e');
      }
    }
  }
}
