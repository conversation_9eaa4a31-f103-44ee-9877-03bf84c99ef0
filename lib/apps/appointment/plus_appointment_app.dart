import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:samitivej_flutter_app/models/all_type_appointment_model.dart';
import 'package:samitivej_flutter_app/services/appointment/appointment_service.dart';

class PlusAppointmentApp extends GetxController {
  /// Error message.
  RxString errorMessage = ''.obs;

  Rx<List<AllTypeAppointmentModel>> myAppointments =
      Rx(<AllTypeAppointmentModel>[]);

  /// Whether the loading of appointments of the current user is in progress or not.
  RxBool isLoadingAppointments = false.obs;

  /// Gets appointments of the current user.
  Future<void> getAllAppointments() async {
    try {
      isLoadingAppointments.value = true;
      final List<AllTypeAppointmentModel>? response =
          await AppointmentService.getAllAppointments();
      if (response != null) {
        myAppointments.value = response;
      }
    } on DioException catch (e) {
      errorMessage.value = e.message ?? e.type.name;
      myAppointments.value = [];
    } finally {
      isLoadingAppointments.value = false;
    }
  }

  /// Whether the loading of upcoming appointments of the current user is in progress or not.
  RxBool isGuardianUpcomingAppointmentsLoading = false.obs;

  /// The upcoming appointments of the current user.
  Rx<List<AllTypeAppointmentModel>> guardianUpcomingAppointments =
      Rx(<AllTypeAppointmentModel>[]);

  /// Gets upcoming appointments of the current user.
  Future<void> getGuardianUpcomingAppointments() async {
    try {
      isGuardianUpcomingAppointmentsLoading.value = true;
      final List<AllTypeAppointmentModel>? response =
          await AppointmentService.getGuardianUpcomingAppointments();
      if (response != null) {
        guardianUpcomingAppointments.value = response;
      }
    } on DioException catch (e) {
      errorMessage.value = e.message ?? "An error occur";
      guardianUpcomingAppointments.value = [];
    } finally {
      isGuardianUpcomingAppointmentsLoading.value = false;
    }
  }

  final wellAppointments = Rx<Map<String, List<AllTypeAppointmentModel>>>({});
  Rx<bool> wellAppointmentsIsEmpty = Rx<bool>(true);

  Future<Map<String, List<AllTypeAppointmentModel>>>
      getWellAppointments() async {
    try {
      var res = await AppointmentService.getWellAppointments();
      res.forEach((key, value) {
        if (value.isNotEmpty) {
          wellAppointmentsIsEmpty.value = false;
        }
      });
      wellAppointments.value = res;
      return res;
    } catch (e) {
      rethrow;
    }
  }
}
