import 'dart:io';
import 'dart:async'; // Import for timeout functionality

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:samitivej_flutter_app/apps/appointment/appointment_app.dart';
import 'package:samitivej_flutter_app/apps/appointment/plus_appointment_app.dart';
import 'package:samitivej_flutter_app/data/models/appointment/doctor_bookable_info_model.dart';
import 'package:samitivej_flutter_app/models/all_type_appointment_model.dart';
import 'package:samitivej_flutter_app/models/doctor_slot_model.dart';
import 'package:samitivej_flutter_app/models/interpreter_model.dart';
import 'package:samitivej_flutter_app/services/appointment/appointment_service.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';

class MyAppointmentApp extends GetxController
    with GetSingleTickerProviderStateMixin {
  final activeTab = Rx<int>(0);
  late final TabController tabC = TabController(
    length: 4,
    vsync: this,
    initialIndex: activeTab.value,
  );

  final myAppointments = Rx<Map<String, List<AllTypeAppointmentModel>>>({});

  Future<void> getMyAppointments() async {
    final plusAppointmentApp = Get.find<PlusAppointmentApp>();
    try {
      final res = await plusAppointmentApp.getWellAppointments();
      myAppointments.value = res;
    } catch (e) {
      rethrow;
    }
  }

  // Re-Schedule Part
  final Rx<DateTime?> selectedAppointmentDate = Rx(null);
  final Rx<String?> selectedAppointmentTime = Rx(null);
  final Rx<String?> symptoms = Rx(null);
  final Rx<List<File>> symptomsImages = Rx([]);
  final Rx<bool> isInterpretation = Rx(false);
  final Rx<InterpreterModel?> interpretation = Rx(null);
  final Rx<List<InterpreterModel>> interpretationLanguage = Rx([]);
  final Rx<List<DoctorSlotTimeModel>> slotTime = Rx([]);

  // Validator
  bool get isAppointmentDateInValid => selectedAppointmentDate.value == null;
  bool get isAppointmentTimeInValid => selectedAppointmentTime.value == null;
  final Rx<bool?> isCreateValidated = Rx(null);

  bool get isCreateValid =>
      !isAppointmentDateInValid && !isAppointmentTimeInValid;

  Future<void> getinterpretationLanguage() async {
    try {
      if (interpretationLanguage.value.isNotEmpty) return;
      final resp = await AppointmentService.getInterpreterList();
      interpretationLanguage.value = resp;
    } catch (e) {
      rethrow;
    }
  }

  Future<File?> urlToFile(String url, String uniqueId) async {
    final resp = await http.get(Uri.parse(url));
    final uint8list = resp.bodyBytes;
    var buffer = uint8list.buffer;
    ByteData byteData = ByteData.view(buffer);

    // get temporary directory of device.
    Directory tempDir = await getTemporaryDirectory();

    File file = await File('${tempDir.path}/img$uniqueId').writeAsBytes(
        buffer.asUint8List(byteData.offsetInBytes, byteData.lengthInBytes));
    return file;
  }

  Future<AllTypeAppointmentModel?> updateAppointment(
    int? appointmentId, {
    required String slotId,
    required String doctorId,
    required String doctorName,
    required String locationCode,
    required String location,
    required String site,
    required DateTime lastStartTime,
    required String name,
    required bool isUser,
    String? hn,
    String? dob,
  }) async {
    final appointmentApp = Get.find<AppointmentApp>();
    try {
      final dateFormat = DateFormat('yyyy-MM-ddTHH:mm:ss');
      final appointmentDate = selectedAppointmentDate.value ?? DateTime.now();
      final appointmentTime =
          selectedAppointmentTime.value?.split('-') ?? ['00:00', '23:59'];
      final startMappedTime = DateFormat('HH:mm').parse(appointmentTime.first);
      final endMappedTime = DateFormat('HH:mm').parse(appointmentTime.last);

      final startAppointment = DateTime(
        appointmentDate.year,
        appointmentDate.month,
        appointmentDate.day,
        startMappedTime.hour,
        startMappedTime.minute,
      );
      final endAppointment = DateTime(
        appointmentDate.year,
        appointmentDate.month,
        appointmentDate.day,
        endMappedTime.hour,
        endMappedTime.minute,
      );

      final listImages = <String>[];
      for (var e in symptomsImages.value) {
        var imgUrl = await appointmentApp.fileToUrl(e);
        listImages.add(imgUrl);
      }
      var data = await AppointmentService.updateAppointment(
          appointmentId: appointmentId,
          symptoms: symptoms.value ?? '',
          symptomImgUrl: listImages,
          slotId: slotId,
          startTime: dateFormat.format(startAppointment),
          endTime: dateFormat.format(endAppointment),
          isNeedInterpreter: isInterpretation.value,
          interpreter: interpretation.value?.interpreterCode ?? '',
          doctorId: doctorId,
          doctorName: doctorName,
          locationCode: locationCode,
          location: location,
          site: site,
          lastStartTime: lastStartTime,
          name: name,
          isUser: isUser,
          hn: hn,
          dob: dob);
      if (data != null) {
        try {
          return AllTypeAppointmentModel.fromJson(data);
        } catch (_) {
          return null;
        }
      } else {
        return null;
      }
    } catch (_) {
      rethrow;
    }
  }

  Future<AllTypeAppointmentModel?> deleteAppointment(
      AllTypeAppointmentModel appointment) async {
    final appointmentApp = Get.find<AppointmentApp>();
    try {
      final dateFormat = DateFormat('yyyy-MM-ddTHH:mm:ss');
      final listImages = <String>[];
      for (var e in symptomsImages.value) {
        var imgUrl = await appointmentApp.fileToUrl(e);
        listImages.add(imgUrl);
      }
      var data = await AppointmentService.deleteAppointment(
          appointmentId: appointment.appointmentId,
          symptoms: symptoms.value ?? '',
          symptomImgUrl: listImages,
          startTime: dateFormat.format(appointment.startTime),
          endTime: dateFormat.format(appointment.endTime ??
              appointment.startTime.add(const Duration(minutes: 30))),
          isNeedInterpreter: isInterpretation.value,
          interpreter: interpretation.value?.interpreterCode ?? '',
          doctorId: appointment.doctorId ?? '',
          doctorName: appointment.doctorName ?? '',
          location: appointment.location ?? '',
          site: appointment.site ?? '',
          name: appointment.patient.name ?? '',
          hn: appointment.hn);
      if (data != null) {
        try {
          return AllTypeAppointmentModel.fromJson(data);
        } catch (_) {
          return null;
        }
      } else {
        return null;
      }
    } catch (_) {
      rethrow;
    }
  }

  /// Test method specifically for testing the timeout in deleteAppointment
  Future<bool> testDeleteAppointmentTimeout(
      AllTypeAppointmentModel appointment) async {
    try {
      // Create a completer that will never complete to simulate a hanging request
      final Completer<bool> completer = Completer();

      // Set up a timer to timeout the request after 2 seconds
      Timer(const Duration(seconds: 2), () {
        if (!completer.isCompleted) {
          completer.completeError(TimeoutException(
              'Delete appointment request timed out after 2 seconds'));
        }
      });

      // This simulates the deleteAppointment method hanging
      return await completer.future;
    } on TimeoutException catch (e) {
      if (kDebugMode) {
        print('Timeout exception in delete appointment test: $e');
      }
      rethrow;
    } catch (e) {
      rethrow;
    }
  }

  Future<String> getHospitalContact(String site, String? specialtyCode) async {
    try {
      final String result = await AppointmentService.getHospitalContact(
        hospitalId: site,
        specialtyCode: specialtyCode,
      );
      return result;
    } catch (e) {
      if (kDebugMode) {
        debugPrint(e.toString());
      }
      return '';
    }
  }

  Future<DoctorBookableInfoModel> getDoctorBookableInfo({
    required String doctorId,
    required String site,
  }) async {
    try {
      final result = await AppointmentService.getDoctorBookableInfo(
        doctorId: doctorId,
        site: site,
      );
      return result;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('getDoctorBookableInfo: $e');
      }
      return DoctorBookableInfoModel(
        doctorId: doctorId,
        site: site,
        priorHour: 0,
        allowBook: false,
      );
    }
  }

  /// Validate and reset the selected appointment time if it is not in the slot time list
  void validateSelectedAppointmentTimeWithSlotTime() {
    if (selectedAppointmentTime.value != null && slotTime.value.isNotEmpty) {
      final slotTimeList = slotTime.value.map((e) => e.time!).toList();
      if (!slotTimeList.contains(selectedAppointmentTime.value)) {
        selectedAppointmentTime.value = null;
      }
    }
  }
}
