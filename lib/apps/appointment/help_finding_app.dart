import 'package:get/get.dart';
import 'package:samitivej_flutter_app/models/help_finding_body_part_model.dart';
import 'package:samitivej_flutter_app/models/help_finding_organ_model.dart';
import 'package:samitivej_flutter_app/models/help_finding_symptom_model.dart';
import 'package:samitivej_flutter_app/models/hospital_model.dart';
import 'package:samitivej_flutter_app/services/appointment/help_finding_service.dart';
import 'package:samitivej_flutter_app/utils/exception_handle.dart';

class HelpFindingApp extends GetxController {
  Rx<List<HelpFindingBodyPartModel>> bodyParts = Rx([]);
  Rx<List<HelpFindingOrganModel>> organs = Rx([]);
  Rx<List<HelpFindingSymptomModel>> symptoms = Rx([]);

  Rx<List<HelpFindingOrganModel>> searchOrgans = Rx([]);
  Rx<List<HelpFindingSymptomModel>> searchSymptoms = Rx([]);

  Rx<int> step = 0.obs;
  Rx<HelpFindingBodyPartModel?> selectedBodyPart = Rx(null);
  Rx<HelpFindingOrganModel?> selectedOrgan = Rx(null);
  Rx<HelpFindingSymptomModel?> selectedSymptoms = Rx(null);
  Rx<DateTime?> selectedAppontmentDate = Rx(null);
  Rx<bool?> selectedRecommendDoctor = Rx(null);
  Rx<String?> selectedGender = Rx(null);
  Rx<HospitalModel?> selectedHospital = Rx(null);

  bool get isQuestionValid =>
      !isAppointmentDateInValid &&
      !isWantToSelectDoctorInValid &&
      !isHospitalInValid;

  bool get isAppointmentDateInValid => selectedAppontmentDate.value == null;
  bool get isWantToSelectDoctorInValid => selectedRecommendDoctor.value == null;
  bool get isHospitalInValid => selectedHospital.value == null;

  HelpFindingBodyPartModel? getBodyPartById(int id) {
    final part = bodyParts.value.firstWhereOrNull((e) => e.bodyPartId == id);
    return part;
  }

  Future<void> getBodyPart() async {
    try {
      final result = await HelpFindingService.getBodyPart();
      bodyParts.value = result;
    } catch (_) {
      rethrow;
    }
  }

  Future<void> getOrgan({String? condition}) async {
    try {
      if (selectedBodyPart.value == null) {
        throw CommonException('Body part not select');
      }

      final result = await HelpFindingService.getOrgan(
        bodyPartId: selectedBodyPart.value!.bodyPartId,
        condition: condition,
      );

      if (condition != null) {
        searchOrgans.value = result;
      } else {
        organs.value = result;
      }
    } catch (_) {
      rethrow;
    }
  }

  Future<void> getSymptoms({String? condition}) async {
    try {
      if (selectedOrgan.value == null) {
        throw CommonException('Organ not select');
      }

      final result = await HelpFindingService.getSymptioms(
        organId: selectedOrgan.value!.organId,
        condition: condition,
      );

      if (condition != null) {
        searchSymptoms.value = result;
      } else {
        symptoms.value = result;
      }
    } catch (_) {
      rethrow;
    }
  }
}
