import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:samitivej_flutter_app/models/doctor_model.dart';
import 'package:samitivej_flutter_app/screen/appointment/filter_screen.dart';
import 'package:samitivej_flutter_app/services/appointment/doctor_service.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class SuggestApp extends GetxController {
  SuggestApp({
    this.specialityCode,
    this.appointmentDate,
    this.site,
    this.isVirtual,
    this.doctorGender,
  }) {
    if (doctorGender != null) {
      genderFilter.value = genderSelection.firstWhereOrNull(
        (e) => e.key == doctorGender,
      );
    }
  }

  final String? specialityCode;
  final DateTime? appointmentDate;
  final String? site;
  final bool? isVirtual;
  final String? doctorGender;

  final Rx<List<Filter>> languages = Rx([]);
  final Rx<List<DoctorModel>> doctors = Rx([]);
  final searchTextController = TextEditingController();
  final Rx<String> searchCondition = Rx('');

  final Rx<List<Filter>> languageFilter = Rx([]);
  final Rx<Filter?> timeFilter = Rx(null);
  final Rx<Filter?> genderFilter = Rx(null);

  final List<Filter> timeSelection = [
    Filter(
      key: '07:00-10:59',
      value: AppLocalizations.of(Get.context!)!.morning,
    ),
    Filter(
      key: '11:00-15:59',
      value: AppLocalizations.of(Get.context!)!.afternoon,
    ),
    Filter(
      key: '16:00-18:59',
      value: AppLocalizations.of(Get.context!)!.evening,
    ),
  ];

  final List<Filter> genderSelection = [
    Filter(key: 'M', value: AppLocalizations.of(Get.context!)!.maleGender),
    Filter(key: 'F', value: AppLocalizations.of(Get.context!)!.femaleGender)
  ];

  bool searchDoctorCondition(DoctorModel e, String condition) {
    return e.drId.toString().toLowerCase().contains(condition.toLowerCase()) ||
        e.fnameEn.toString().toLowerCase().contains(condition.toLowerCase()) ||
        e.fnameTh.toString().toLowerCase().contains(condition.toLowerCase()) ||
        e.branchEn.toString().toLowerCase().contains(condition.toLowerCase()) ||
        e.branchTh.toString().toLowerCase().contains(condition.toLowerCase());
  }

  Future<List<DoctorModel>> doctorTimeFilter() async {
    if (timeFilter.value == null) return [...doctors.value];
    try {
      final resp = await DoctorService.getDoctorList(
        specialityCode: specialityCode!,
        date: appointmentDate,
        time: timeFilter.value?.key,
        site: site,
        isVirtual: isVirtual,
      );

      return resp;
    } catch (e) {
      rethrow;
    }
  }

  List<DoctorModel> doctorSearchFilter(List<DoctorModel> doctors) {
    if (searchCondition.value.isEmpty) return [...doctors];
    return doctors
        .where((e) => searchDoctorCondition(e, searchCondition.value))
        .toList();
  }

  List<DoctorModel> doctorLanguageFilter(List<DoctorModel> doctors) {
    if (languageFilter.value.isEmpty) return [...doctors];
    List<DoctorModel> temps = [];
    for (final temp in [...doctors]) {
      if ((temp.languages ?? [])
          .any((e) => languageFilter.value.map((e) => e.key).contains(e))) {
        temps.add(temp);
      }
    }
    return temps;
  }

  List<DoctorModel> doctorGenderFilter(List<DoctorModel> doctors) {
    if (genderFilter.value == null) return doctors;
    return doctors.where((e) => e.gender == genderFilter.value!.key).toList();
  }

  Future<void> getDoctorLanguages() async {
    try {
      final resp = await DoctorService.getDoctorLanguages();
      languages.value = resp;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> suggestDoctor() async {
    try {
      final resp = await DoctorService.getDoctorList(
        specialityCode: specialityCode,
        date: appointmentDate,
        site: site,
        isVirtual: isVirtual,
      );

      doctors.value = resp;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> favoriteDoctor() async {
    try {
      final resp = await DoctorService.getFavoriteDoctors();
      doctors.value = resp;
    } catch (e) {
      rethrow;
    }
  }
}
