import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:samitivej_flutter_app/localization/app_translate_key.dart';
import 'package:samitivej_flutter_app/models/doctor_model.dart';
import 'package:samitivej_flutter_app/models/help_finding_symptom_model.dart';
import 'package:samitivej_flutter_app/services/appointment/appointment_service.dart';
import 'package:samitivej_flutter_app/services/appointment/doctor_service.dart';
import 'package:samitivej_flutter_app/services/appointment/help_finding_service.dart';
import 'package:samitivej_flutter_app/services/storage_service.dart';

class SearchApp extends GetxController {
  final storage = StorageService();
  final searchTextController = TextEditingController();

  final Rx<List<String>> searchRecent = Rx([]);
  final Rx<List<String>> searchTop = Rx([]);
  final Rx<List<String>> searchSymptoms = Rx([]);
  final Rx<bool> showSearchResult = Rx(false);

  final Rx<List<DoctorModel>> searchedDoctor = Rx([]);
  final Rx<List<HelpFindingSymptomModel>> suggestSymptom = Rx([]);

  Future<void> getTopAndRecentSearch() async {
    final result = await AppointmentService.getTopAndRecentSearch();
    searchTop.value = result['top'] ?? [];
    searchRecent.value = result['recent'] ?? [];
  }

  Future<void> getSearchChoices() async {
    searchSymptoms.value = [AppTranslateKey.headache, AppTranslateKey.fever, AppTranslateKey.scalds];
    await getTopAndRecentSearch();
  }

  Future<void> saveSearchRecent(String condition) async {
    await AppointmentService.saveSearch(condition: condition);
    await getTopAndRecentSearch();
  }

  Future<void> searchDoctor(String condition) async {
    try {
      final lowered = condition.toLowerCase();
      List<DoctorModel> doctorResult = [];
      List<HelpFindingSymptomModel> suggestResult = [];

      await Future.wait([
        (() async {
          return doctorResult = await DoctorService.searchDoctorName(
            site: 'ALL',
            drName: lowered,
          );
        })(),
        (() async {
          return suggestResult = await HelpFindingService.getSymptioms(
            condition: condition.toLowerCase(),
          );
        })()
      ]);

      searchedDoctor.value = doctorResult;
      suggestSymptom.value = suggestResult;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> searchDoctorBySymptom(String specialityCode) async {
    try {
      final doctorSuggest = await DoctorService.getDoctorList(
        specialityCode: specialityCode,
      );
      searchedDoctor.value = doctorSuggest;
    } catch (e) {
      rethrow;
    }
  }
}

