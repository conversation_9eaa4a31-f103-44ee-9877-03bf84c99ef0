import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/models/discover_item_model.dart';
import 'package:samitivej_flutter_app/models/doctor_profile_model.dart';
import 'package:samitivej_flutter_app/models/response_model.dart';
import 'package:samitivej_flutter_app/services/api_client.dart';
import 'package:samitivej_flutter_app/services/appointment/doctor_service.dart';

class ArticleApp extends GetxController {
  /// API client for the food & fit services.
  final ApiClient _client = ApiClient();

  /// API client for the plus services.
  final ApiClient _plusClient = ApiClient(baseUrl: ApiConstant.cognitoPlusUrl);

  /// Error message.
  RxString errorMessage = ''.obs;

  /// Whether the loading of article is in progress or not.
  RxBool isArticleLoading = false.obs;

  /// The article.
  Rx<DiscoverItemModel> article = Rx(DiscoverItemModel());

  /// Whether the loading of doctor profile is in progress or not.
  RxBool isDoctorLoading = false.obs;

  /// The doctor profile.
  Rx<DoctorProfileModel?> doctor = Rx(null);

  /// Gets an article with [id].
  Future<void> getArticle(int id) async {
    try {
      isArticleLoading.value = true;
      final ResponseModel response = await _client.get(
        ApiConstant.getDiscoverDetail,
        queryParameters: {
          'itemId': id.toString(),
        },
      );
      article.value = DiscoverItemModel.fromJson(response.data!);
      isArticleLoading.value = false;
    } on DioException {
      article.value = DiscoverItemModel();
      isArticleLoading.value = false;
    }
  }

  /// Gets an article with [id].
  Future<void> getDoctor(String id) async {
    try {
      isDoctorLoading.value = true;
      final ResponseModel response = await _plusClient.post(
        ApiConstant.getDoctorProfile,
        data: {'doctorId': id, 'site': 'ALL'},
      );
      doctor.value = response.data!['data'] != null
          ? DoctorProfileModel.fromJson(
              response.data!['data'] as Map<String, dynamic>,
            )
          : null;
      isDoctorLoading.value = false;
    } on DioException {
      isDoctorLoading.value = false;
    }
  }

  /// Sets doctor with ID [doctorId] as favorite or not.
  Future<void> setFavoriteDoctor(
    String doctorId,
    bool isFavorite,
    String site,
  ) async {
    isFavorite
        ? await DoctorService.favoriteDoctor(doctorId: doctorId, site: site)
        : await DoctorService.unFavoriteDoctor(doctorId: doctorId, site: site);
  }
}
