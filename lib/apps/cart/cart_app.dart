import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:samitivej_flutter_app/apps/address/address_app.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/constants/app_constant.dart';
import 'package:samitivej_flutter_app/models/cart_item_model.dart';
import 'package:samitivej_flutter_app/models/food_purchase_model.dart';
import 'package:samitivej_flutter_app/models/invoice_model.dart';
import 'package:samitivej_flutter_app/models/invoices_model.dart';
import 'package:samitivej_flutter_app/models/purchase_model.dart';
import 'package:samitivej_flutter_app/models/response_model.dart';
import 'package:samitivej_flutter_app/services/api_client.dart';
import 'package:samitivej_flutter_app/services/storage_service.dart';

class CartApp extends GetxController {
  /// API client for the total health services.
  final ApiClient _client = ApiClient(baseUrl: ApiConstant.totalHealthDomain);

  final StorageService _storage = StorageService();
  RxList<CartItemModel> cartItems = RxList<CartItemModel>.empty();
  RxList<String> selectedCartItems = RxList<String>.empty();
  RxBool isSelectedCartItemAll = false.obs;

  /// The invoice number of the selected invoice.
  RxnString selectedInvoiceNumber = RxnString();

  /// Whether the loading of pending payment invoices of the current user is in progress or not.
  RxBool isPendingPaymentInvoicesLoading = false.obs;

  /// The pending payment invoices of the current user.
  Rx<List<InvoiceModel>> pendingPaymentInvoices = Rx([]);

  /// Whether the loading of pending payment purchases of the current user is in progress or not.
  RxBool isPendingPaymentPurchaseLoading = false.obs;

  /// The pending payment purchases of the current user.
  Rx<List<PurchaseModel>> pendingPaymentPurchases = Rx([]);

  /// Whether the loading of pending payment food purchases of the current user is in progress or not.
  RxBool isPendingPaymentFoodPurchaseLoading = false.obs;

  /// The pending payment food purchases of the current user.
  Rx<List<FoodPurchaseModel>> pendingPaymentFoodPurchases = Rx([]);

  /// Whether the loading of paid purchases of the current user is in progress or not.
  RxBool isPaidPurchasesLoading = false.obs;

  /// The paid purchases of the current user.
  Rx<List<PurchaseModel>> paidPurchases = Rx([]);

  /// Whether the loading of paid food purchases of the current user is in progress or not.
  RxBool isPaidFoodPurchasesLoading = false.obs;

  /// The paid food purchases of the current user.
  Rx<List<FoodPurchaseModel>> paidFoodPurchases = Rx([]);

  /// Whether the pending invoices is selected or not.
  RxBool isPendingInvoicesSelected = false.obs;

  /// Whether all invoices are selected or not.
  RxBool isAllInvoicesSelected = false.obs;

  /// Whether all items is selected or not.
  RxBool isAllItemsSelected = false.obs;

  /// Whether deleting an order is in progress or not.
  RxBool isDeleteOrderLoading = false.obs;

  /// Whether deleting a food order is in progress or not.
  RxBool isDeleteFoodOrderLoading = false.obs;

  /// The total net price of selected items.
  RxDouble selectedTotalNetPrice = 0.0.obs;

  /// The total full price of selected items.
  RxDouble selectedTotalFullPrice = 0.0.obs;

  /// The total delivery fee of selected items.
  RxDouble selectedTotalDeliveryFee = 0.0.obs;

  Future<void> initCart() async {
    final List<dynamic>? result =
        _storage.read<List<dynamic>>(AppConstant.cartItemListKey);
    if (result != null) {
      cartItems.clear();
      cartItems.addAll(
        result
            .map(
              // ignore: implicit_dynamic_parameter
              (e) => CartItemModel.fromJson(e as Map<String, dynamic>),
            )
            .toList(),
      );
    }

    // Load pending payment invoices.
    getPendingPaymentInvoices();

    // Load pending purchases.
    getPendingPaymentPurchases();

    // Load pending payment purchases.
    getPendingPaymentFoodPurchases();

    // Load paid purchases.
    getPaidPurchases();
    getPaidFoodPurchases();
  }

  void selectedCartItem(String cartId) {
    if (selectedCartItems.contains(cartId)) {
      selectedCartItems.removeWhere((element) => element == cartId);
    } else {
      selectedCartItems.add(
        cartItems.firstWhere((element) => element.id == cartId).id!,
      );
    }
  }

  void selectAllCartItems(bool value) {
    if (value) {
      selectedCartItems.addAll(
        cartItems.map<String>((element) => element.id!).toList(),
      );
      isSelectedCartItemAll.value = true;
    } else {
      selectedCartItems.clear();
      isSelectedCartItemAll.value = false;
    }
  }

  Future<void> deleteCartItem(String cartId) async {
    cartItems.removeWhere((element) => element.id == cartId);
    selectedCartItems.removeWhere((element) => element == cartId);
    await saveCartItems();
  }

  Future<void> saveCartItems() async {
    await _storage.write(
      AppConstant.cartItemListKey,
      cartItems,
    );
  }

  Future<void> addItem(CartItemModel item) async {
    cartItems.add(item);
    await _storage.write(
      AppConstant.cartItemListKey,
      cartItems,
    );
  }

  /// Gets pending payment invoices of the current user on [month] and [year].
  Future<void> getPendingPaymentInvoices() async {
    try {
      isPendingPaymentInvoicesLoading.value = true;
      final ResponseModel response =
          await _client.post(ApiConstant.getPendingPaymentInvoices);
      final invoices =
          InvoicesModel.fromJson(response.data as Map<String, dynamic>).data;
      final pendingInvoices = <InvoiceModel>[];
      for (final invoice in invoices) {
        if (invoice.paymentStatus == InvoiceModel.paymentStatusPending) {
          pendingInvoices.add(invoice);
        }
      }
      pendingPaymentInvoices.value = pendingInvoices;

      isPendingPaymentInvoicesLoading.value = false;
    } on DioException {
      pendingPaymentInvoices.value = [];
      isPendingPaymentInvoicesLoading.value = false;
    }
  }

  /// Gets pending payment purchases of the current user on [month] and [year].
  Future<void> getPendingPaymentPurchases() async {
    try {
      isPendingPaymentPurchaseLoading.value = true;
      final ResponseModel response =
          await _client.post(ApiConstant.getPendingPaymentPurchases);
      final List listData = response.data!['listData'] as List;
      pendingPaymentPurchases.value = listData
          .map((purchase) =>
              PurchaseModel.fromJson(purchase as Map<String, dynamic>))
          .toList();
      isPendingPaymentPurchaseLoading.value = false;
    } on DioException {
      pendingPaymentPurchases.value = [];
      isPendingPaymentPurchaseLoading.value = false;
    }
  }

  /// Gets pending payment food purchases of the current user on [month] and [year].
  Future<void> getPendingPaymentFoodPurchases() async {
    try {
      isPendingPaymentFoodPurchaseLoading.value = true;
      final ResponseModel response =
          await _client.post(ApiConstant.getPendingPaymentFoodPurchases);
      final List listData = response.data!['listData'] as List;
      pendingPaymentFoodPurchases.value = listData.map((purchase) {
        return FoodPurchaseModel.fromJson(purchase as Map<String, dynamic>);
      }).toList();
      isPendingPaymentFoodPurchaseLoading.value = false;
    } on DioException {
      pendingPaymentFoodPurchases.value = [];
      isPendingPaymentFoodPurchaseLoading.value = false;
    }
  }

  /// Calculate delivery fee for selected cart items.
  Future<void> calculateDeliveryFee() async {
    final selectedAddressId = Get.find<AddressApp>().selectedAddressId.value;
    if (selectedAddressId != null) {
      bool selected = false;
      double totalDeliveryFee = 0;
      for (final purchase in pendingPaymentFoodPurchases.value) {
        if (purchase.selected) {
          selected = true;
          try {
            final ResponseModel response = await _client.post(
              ApiConstant.calculateDeliveryFee,
              data: {
                'calculateDeliveryFeeRequestBean': {
                  'shipmentAddressId': selectedAddressId,
                  'immPlanId': purchase.purchase?.plan?.immPlanId
                }
              },
            );
            purchase.deliveryFee = (response.data?['data'] as num?)?.toDouble();
          } on DioException {
            purchase.deliveryFee = null;
          }
          totalDeliveryFee += purchase.deliveryFee ?? 0;
        }
      }
      selectedTotalDeliveryFee.value = totalDeliveryFee;
      if (selected) {
        pendingPaymentFoodPurchases.trigger(pendingPaymentFoodPurchases.value);
      }
    }
  }

  /// Gets paid health invoices of the current user on [month] and [year].
  Future<void> getPaidPurchases() async {
    try {
      isPaidPurchasesLoading.value = true;
      final ResponseModel response =
          await _client.post(ApiConstant.getPaidPurchases);
      final List listData = response.data!['listData'] as List;
      final purchases = <PurchaseModel>[];
      purchases.addAll((listData
              .map((purchase) =>
                  PurchaseModel.fromJson(purchase as Map<String, dynamic>))
              .toList())
          .reversed);
      paidPurchases.value = purchases;
      isPaidPurchasesLoading.value = false;
    } on DioException {
      paidPurchases.value = [];
      isPaidPurchasesLoading.value = false;
    }
  }

  /// Gets paid food invoices of the current user on [month] and [year].
  Future<void> getPaidFoodPurchases() async {
    try {
      isPaidFoodPurchasesLoading.value = true;
      final ResponseModel response =
          await _client.post(ApiConstant.getPaidFoodPurchases);
      final List listData = response.data!['listData'] as List;
      final purchases = <FoodPurchaseModel>[];
      purchases.addAll((listData
              .map((purchase) =>
                  FoodPurchaseModel.fromJson(purchase as Map<String, dynamic>))
              .toList())
          .reversed);
      paidFoodPurchases.value = purchases;
      isPaidFoodPurchasesLoading.value = false;
    } on DioException {
      paidFoodPurchases.value = [];
      isPaidFoodPurchasesLoading.value = false;
    }
  }

  /// Reloads cart.
  Future<void> reloadCart() async {
    getPendingPaymentInvoices();
    getPendingPaymentPurchases();
    getPendingPaymentFoodPurchases();
    getPaidPurchases();
    getPaidFoodPurchases();
  }

  /// Deletes an order with [orderNumber].
  Future<void> deleteOrder(String orderNumber) async {
    try {
      isDeleteOrderLoading.value = true;
      await _client.post(ApiConstant.deleteOrder, data: {
        'orderNumber': orderNumber,
      });
      pendingPaymentPurchases.value
          .removeWhere((purchase) => purchase.orderNumber == orderNumber);
      pendingPaymentPurchases.trigger(pendingPaymentPurchases.value);
      isDeleteOrderLoading.value = false;
    } on DioException {
      isDeleteOrderLoading.value = false;
    }

    _updateSelectAllStatus();
  }

  /// Deletes a food order with [orderNumber].
  Future<void> deleteFoodOrder(String orderNumber) async {
    try {
      isDeleteFoodOrderLoading.value = true;
      await _client.post(ApiConstant.deleteFoodOrder, data: {
        'orderNumber': orderNumber,
      });
      pendingPaymentFoodPurchases.value.removeWhere((purchase) =>
          purchase.purchase?.purchase?.orderNumber == orderNumber);
      pendingPaymentFoodPurchases.trigger(pendingPaymentFoodPurchases.value);
      isDeleteFoodOrderLoading.value = false;
    } on DioException {
      isDeleteFoodOrderLoading.value = false;
    }

    _updateSelectAllStatus();
  }

  /// Toggles selection on a purchase.
  void togglePurchaseSelection(int id) {
    final purchase = pendingPaymentPurchases.value
        .firstWhereOrNull((purchase) => purchase.id == id);
    if (purchase != null) {
      purchase.selected = !purchase.selected;
      pendingPaymentPurchases.trigger(pendingPaymentPurchases.value);
    }

    _updateSelectAllStatus();
  }

  /// Toggles selection on a food purchase.
  void toggleFoodPurchaseSelection(int id) {
    final purchase = pendingPaymentFoodPurchases.value
        .firstWhereOrNull((purchase) => purchase.purchase?.purchase?.id == id);
    if (purchase != null) {
      purchase.selected = !purchase.selected;
      pendingPaymentFoodPurchases.trigger(pendingPaymentFoodPurchases.value);
    }

    _updateSelectAllStatus();
  }

  /// Selects all purchases and invoices.
  void selectAll() {
    for (final purchase in pendingPaymentPurchases.value) {
      purchase.selected = true;
    }
    for (final purchase in pendingPaymentFoodPurchases.value) {
      purchase.selected = true;
    }
    pendingPaymentPurchases.trigger(pendingPaymentPurchases.value);
    pendingPaymentFoodPurchases.trigger(pendingPaymentFoodPurchases.value);

    isAllItemsSelected.trigger(true);

    _updateSelectAllStatus();
  }

  /// Deselects all purchases and invoices.
  void deselectAll() {
    for (final purchase in pendingPaymentPurchases.value) {
      purchase.selected = false;
    }
    for (final purchase in pendingPaymentFoodPurchases.value) {
      purchase.selected = false;
    }
    pendingPaymentPurchases.trigger(pendingPaymentPurchases.value);
    pendingPaymentFoodPurchases.trigger(pendingPaymentFoodPurchases.value);

    isAllItemsSelected.trigger(false);

    _updateSelectAllStatus();
  }

  /// Updates if all items are selected or not.
  void _updateSelectAllStatus() {
    if (pendingPaymentPurchases.value
                .firstWhereOrNull((purchase) => !purchase.selected) !=
            null ||
        pendingPaymentFoodPurchases.value
                .firstWhereOrNull((purchase) => !purchase.selected) !=
            null) {
      isAllItemsSelected.trigger(false);
    } else {
      isAllItemsSelected.trigger(true);
    }

    double fullPrice = 0;
    double netPrice = 0;
    for (final purchase in pendingPaymentPurchases.value) {
      if (purchase.selected) {
        for (final item in purchase.items) {
          fullPrice += item.fullPrice;
          netPrice += item.priceAfterDiscount;
        }
      }
    }
    for (final purchase in pendingPaymentFoodPurchases.value) {
      if (purchase.selected) {
        fullPrice += purchase.purchase?.purchase?.price ?? 0;
        netPrice += purchase.purchase?.purchase?.price ?? 0;
      }
    }
    selectedTotalNetPrice.value = netPrice;
    selectedTotalFullPrice.value = fullPrice;
  }
}
