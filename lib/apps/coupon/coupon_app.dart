import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:samitivej_flutter_app/apps/common/common_app.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/models/coupon/coupon_model.dart';
import 'package:samitivej_flutter_app/models/invoice_model.dart';
import 'package:samitivej_flutter_app/models/response_model.dart';
import 'package:samitivej_flutter_app/screen/cart/payment_screen.dart';
import 'package:samitivej_flutter_app/services/api_client.dart';
import 'package:samitivej_flutter_app/utils/exception_handle.dart';

class CouponApp extends GetxController {
  final TextEditingController searchController = TextEditingController();

  final CommonApp _commonApp = Get.find<CommonApp>();
  final ApiClient _client = ApiClient();
  final RxString errorMessage = ''.obs;

  final Rx<bool> isSearching = Rx<bool>(false);
  final Rx<bool> isCouponLoading = Rx<bool>(false);
  final Rx<List<CouponModel>> couponSearchList = Rx([]);
  final Rx<List<CouponModel>> couponList = Rx([]);
  final Rx<CouponModel?> selectedCoupon = Rx(null);

  static String? validateCoupon({
    CouponModel? coupon,
    InvoiceModel? invoice,
    String? paymentMethod,
  }) {
    if (coupon == null) return null;

    if (invoice != null &&
        !(double.parse(invoice.totalNetAmount ?? '0') >
            (coupon.promotion.minimumSpent ?? 0))) {
      return 'invalidMiniMumSpent';
    }

    if (paymentMethod != null &&
        paymentMethod != PaymentScreen.paymentMethodCreditCard) {
      return 'invalidPaymentMethod';
    }

    return null;
  }

  Future<CouponModel> getCoupon({
    required String? couponInsert,
  }) async {
    try {
      isCouponLoading.value = true;
      final ResponseModel response =
          await _client.post(ApiConstant.checkCouponCode,
              data: {
                "promotionCode": couponInsert,
              },
              options: Options(
                headers: {
                  "Accept-Language": _commonApp.selectedLanguage.value,
                },
              ));
      final rawResp = response.data?['data'] as Map<String, dynamic>?;
      if (rawResp != null) {
        return CouponModel.fromJson(rawResp);
      } else {
        throw CommonException('Server error');
      }
    } on DioException catch (e) {
      throw ExceptionHandle.apiExceptionHandle(e);
    } catch (_) {
      rethrow;
    }
  }

  Future<void> getCouponList() async {
    try {
      isCouponLoading.value = true;
      final ResponseModel response =
          await _client.get(ApiConstant.getCouponList,
              options: Options(
                headers: {
                  "Accept-Language": _commonApp.selectedLanguage.value,
                },
              ));
      final List listResponse = response.data!['listData'] as List;
      couponList.value = listResponse
          .map((coupon) => CouponModel.fromJson(coupon as Map<String, dynamic>))
          .toList();
      isCouponLoading.value = false;
    } on DioException {
      couponList.value = [];
      isCouponLoading.value = false;
    }
  }

  Future<void> searchCoupon() async {
    try {
      isCouponLoading.value = true;
      final ResponseModel response = await _client.post(
        ApiConstant.checkCouponCode,
        data: {
          "promotionCode": searchController.text,
          "promotionId": 0,
        },
        options: Options(
          headers: {
            "Accept-Language": _commonApp.selectedLanguage.value,
          },
        ),
      );
      final Map<String, dynamic>? res = response.data;

      if (res == null || res.isEmpty) {
        throw CommonException('Server error');
      }

      final mappedCoupon = CouponModel.fromJson(res);
      if (mappedCoupon.couponCondition == 'Success') {
        couponList.value.add(mappedCoupon);
      }
      couponSearchList.value = [mappedCoupon];
    } on DioException catch (error) {
      throw CommonException(
          error.response?.data['message'] as String? ?? error.message);
    } catch (error) {
      throw CommonException('Server error');
    } finally {
      isCouponLoading.value = false;
    }
  }
}
