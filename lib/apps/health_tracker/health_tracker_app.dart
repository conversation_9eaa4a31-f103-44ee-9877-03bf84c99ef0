import 'package:collection/collection.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:samitivej_flutter_app/screen/health_tracker/components/health_tracker_detail/heath_tracker_detail_chart.dart';
import 'package:samitivej_flutter_app/services/health_tracker_service.dart';

class HealthTrackerApp extends GetxController {
  HealthTrackerApp({required this.trackerType});
  final String trackerType;

  Rx<List<LinearData>> dayData = Rx([]);
  Rx<List<LinearData>> yearData = Rx([]);
  Rx<List<LinearData>> filteredData = Rx([]);
  Rx<LinearData?> selectedData = Rx(null);
  Rx<String> filter = Rx('Day');
  bool get isTypeBMI => trackerType == 'bmi';

  List<LinearData> groupData(List<LinearData> result, String groupBy) {
    List<LinearData> newData = <LinearData>[];
    LinearData? tempData;

    for (final data in result.asMap().entries) {
      final e = data.value;
      final i = data.key;

      DateTime resetedDateTime;
      bool isSum = false;

      if (groupBy == 'month') {
        resetedDateTime = DateTime(
          e.horizontal.year,
          e.horizontal.month,
        );
        isSum = tempData?.horizontal.month == e.horizontal.month;
      } else if (groupBy == 'day') {
        resetedDateTime = DateTime(
          e.horizontal.year,
          e.horizontal.month,
          e.horizontal.day,
        );
        isSum = tempData?.horizontal.day == e.horizontal.day;
      } else {
        resetedDateTime = DateTime(
          e.horizontal.year,
          e.horizontal.month,
          e.horizontal.day,
          e.horizontal.hour,
        );
        isSum = tempData?.horizontal.hour == e.horizontal.hour;
      }

      if (tempData == null || isSum) {
        tempData = LinearData(
          resetedDateTime,
          (tempData?.vertical ?? 0) + e.vertical,
        );

        if (i == (result.length - 1)) {
          newData.add(LinearData(
            tempData.horizontal,
            tempData.vertical,
          ));
        }
      } else {
        newData.add(LinearData(
          tempData.horizontal,
          tempData.vertical,
        ));

        tempData = LinearData(
          resetedDateTime,
          e.vertical,
        );

        if (i == (result.length - 1)) {
          newData.add(LinearData(
            tempData.horizontal,
            tempData.vertical,
          ));
        }
      }
    }

    return newData;
  }

  List<LinearData> nearData(List<LinearData> result, String nearFrom) {
    final dateFormater = DateFormat(nearFrom == 'month'
        ? 'MMM/yyyy'
        : nearFrom == 'day'
            ? 'dd/MMM/yyy'
            : 'HH');

    final rawResult = result.map(
      (e) => {
        'time': dateFormater.format(e.horizontal),
        'value': e,
      },
    );

    final groupByDate = groupBy<dynamic, String>(
      rawResult,
      (obj) => obj['time'] as String,
    );

    List<LinearData> newData = [];
    if (nearFrom == 'month' || nearFrom == 'day') {
      // avg value
      for (final e in groupByDate.entries) {
        final list = e.value.map((e) => e['value'] as LinearData).toList();
        double avgValue = (list.map((e) => e.vertical).reduce((a, b) => a + b) /
            e.value.length);

        newData.add(
          LinearData(dateFormater.parse(e.key), avgValue),
        );
      }
    } else {
      // get first value of hour
      newData = [
        ...groupByDate.entries
            .map((e) => e.value.first['value'] as LinearData)
            
      ];
    }

    newData = newData.map((e) {
      DateTime addedHour;
      if (nearFrom == 'month') {
        addedHour = DateTime(
          e.horizontal.year,
          e.horizontal.month,
        );
      } else if (nearFrom == 'day') {
        addedHour = DateTime(
          e.horizontal.year,
          e.horizontal.month,
          e.horizontal.day,
        );
      } else {
        addedHour = DateTime(
          e.horizontal.year,
          e.horizontal.month,
          e.horizontal.day,
          e.horizontal.hour,
        );
      }
      return LinearData(addedHour, e.vertical);
    }).toList();

    return newData;
  }

  List<LinearData> groupingData(List<LinearData> data, String by) {
    List<LinearData> result = [];
    switch (trackerType) {
      case 'burn':
      case 'step':
        result = groupData(data, by);
        break;

      case 'heart rate':
        result = nearData(data, by);
        break;

      default:
        result = data;
        break;
    }

    return result;
  }

  void filtering() {
    List<LinearData> dataList = [];
    DateTime getDate(DateTime d, {int? h}) => DateTime(
          d.year,
          d.month,
          d.day,
          h ?? 0,
        );
    final now = DateTime.now();

    switch (filter.value) {
      case 'Day':
        dataList = groupingData(dayData.value, 'hour');
        break;

      case 'Week':
        final firstDayOfCurrentWeek = getDate(
          now.subtract(
            Duration(days: now.weekday),
          ),
        ).millisecondsSinceEpoch;
        final lasyDayOfCurrentWeek = getDate(
          now.add(
            Duration(days: DateTime.daysPerWeek - (now.weekday + 1)),
          ),
        ).millisecondsSinceEpoch;

        final filtered = yearData.value
            .where((e) =>
                e.horizontal.millisecondsSinceEpoch >= firstDayOfCurrentWeek &&
                e.horizontal.millisecondsSinceEpoch <= lasyDayOfCurrentWeek)
            .toList();
        dataList = groupingData(filtered, 'day');
        break;

      case 'Month':
        final firstDateOfMonth = DateTime(now.year, (now.month), 1);
        final lastDateOfMonth = DateTime(now.year, (now.month) + 1, 0);
        final filtered = yearData.value
            .where((e) =>
                e.horizontal.millisecondsSinceEpoch >=
                    firstDateOfMonth.millisecondsSinceEpoch &&
                e.horizontal.millisecondsSinceEpoch <=
                    lastDateOfMonth.millisecondsSinceEpoch)
            .toList();
        dataList = groupingData(filtered, 'day');
        break;

      default:
        dataList = groupingData(yearData.value, 'month');
        break;
    }

    filteredData.value = dataList;
    selectedData.value = dataList.last;
  }

  Future<Map<String, double>> bmiCalculator(
    double height,
    double weight, {
    double? bmi,
  }) async {
    final double result = bmi != null
        ? bmi.toDouble()
        : weight / ((height / 100) * (height / 100));

    if (bmi == null) {
      await HealthTrackerService.updateBmi(height, weight, result);
    }

    String stringResult = '';
    if (result >= 23.0) stringResult = 'น้ำหนักเกิน';
    if (result < 23.0) stringResult = 'สมส่วน';
    if (result < 18.5) stringResult = 'น้ำหนักน้อยกว่าเกณฑ์';

    return {stringResult: result};
  }

  Future<Map<String, double>> getBody() async {
    try {
      final data = await HealthTrackerService.getBmi();
      return data;
    } catch (_) {
      rethrow;
    }
  }

  Future<void> getHealthTrack() async {
    try {
      final now = DateTime.now();
      final dayResult = await HealthTrackerService.getHealthGraph(trackerType);
      final yearResult = await HealthTrackerService.getHealthGraphByDate(
        trackerType,
        DateTime(now.year, 1),
        DateTime(now.year, 12 + 1, 0),
      );
      dayResult.sort((a, b) => a.horizontal.compareTo(b.horizontal));
      yearResult.sort((a, b) => a.horizontal.compareTo(b.horizontal));

      dayData.value = dayResult;
      yearData.value = yearResult;
    } catch (_) {
      rethrow;
    }
  }
}
