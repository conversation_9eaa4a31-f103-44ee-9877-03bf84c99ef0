import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:samitivej_flutter_app/apps/auth/auth_app.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/models/queue_appointment_model.dart';
import 'package:samitivej_flutter_app/models/queue_status_model.dart';
import 'package:samitivej_flutter_app/models/response_model.dart';
import 'package:samitivej_flutter_app/services/api_client.dart';

class QueueApp extends GetxController {
  /// The auth controller.
  final AuthApp authApp = Get.find<AuthApp>();

  /// API client for the queue services.
  final ApiClient _client = ApiClient(baseUrl: ApiConstant.queueDomain);

  /// Whether loading queue status list.
  RxBool isGetQueueListLoading = false.obs;
  RxBool isGetCurrentQueueLoading = false.obs;
  RxBool isRatingLoading = false.obs;

  RxBool isShowingWelcomeDialog = false.obs;
  RxBool isShowingRatingDialog = false.obs;

  RxBool isScroll = false.obs;
  RxBool isHighlight = false.obs;

  /// The queue list of the current user
  Rx<List<QueueAppointmentModel>> queueList = Rx([]);
  Rx<List<QueueStatusModel>> currentQueue = Rx([]);

  /// Get queue status list of the current user.
  Future<void> getQueueList() async {
    try {
      isGetQueueListLoading.value = true;

      final ResponseModel response =
          await _client.get(ApiConstant.getAllQueueStatusList,
              options: Options(
                headers: {
                  'Authorization': authApp.cognitoIdToken.value,
                },
              ));
      final List listData = response.data!['listData'] as List;
      queueList.value = listData
          .map((e) => QueueAppointmentModel.fromJson(e as Map<String, dynamic>))
          .toList();
      isGetQueueListLoading.value = false;
    } on DioException catch (_) {
      isGetQueueListLoading.value = false;
    } finally {
      isGetQueueListLoading.value = false;
    }
  }

  Future<void> getCurrentQueueStatus() async {
    try {
      isGetCurrentQueueLoading.value = true;

      final ResponseModel response =
          await _client.get(ApiConstant.getCurrentQueueStatus,
              options: Options(
                headers: {
                  'Authorization': authApp.cognitoIdToken.value,
                  'Accept-Language': 'th'
                },
              ));
      final List listData = response.data!['listData'] as List;
      currentQueue.value = listData
          .map((e) => QueueStatusModel.fromJson(e as Map<String, dynamic>))
          .toList();
      isGetCurrentQueueLoading.value = false;
    } on DioException catch (_) {
      isGetCurrentQueueLoading.value = false;
    } finally {
      isGetCurrentQueueLoading.value = false;
    }
  }

  Future<void> sendQueueRating(
      int? star1, int? star2, String? episodeNo, String? comment) async {
    try {
      isRatingLoading.value = true;

      await _client.post(
        ApiConstant.insertQueueRating,
        options: Options(
          headers: {
            'Authorization': authApp.cognitoIdToken.value,
            'Accept-Language': 'th'
          },
        ),
        data: {
          'rating': star1,
          'rating2': star2,
          'episodeNo': episodeNo,
          'comment': comment
        },
      );
      isRatingLoading.value = false;
    } on DioException catch (_) {
      isRatingLoading.value = false;
    } finally {
      isRatingLoading.value = false;
    }
  }
}
