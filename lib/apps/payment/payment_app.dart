import 'package:credit_card_validator/credit_card_validator.dart';
import 'package:credit_card_validator/validation_results.dart';
import 'package:expandable/expandable.dart';
import 'package:get/get.dart';
import 'package:samitivej_flutter_app/constants/app_constant.dart';
import 'package:samitivej_flutter_app/models/validation_model.dart';
import 'package:samitivej_flutter_app/services/storage_service.dart';
import 'package:samitivej_flutter_app/utils/card_utils/card_util.dart';
import 'package:samitivej_flutter_app/utils/validation_helper.dart';
import 'package:samitivej_flutter_app/models/payment_card_model.dart';

class PaymentApp extends GetxController {
  final StorageService _storage = StorageService();
  final CreditCardValidator _ccValidator = CreditCardValidator();

  RxString selectedPaymentMethod = ''.obs;
  RxString selectedMobileBanking = ''.obs;

  RxBool isUseCoin = false.obs;
  RxDouble totalPrice = 0.0.obs;
  RxDouble netPrice = 0.0.obs;
  RxDouble discountAmount = 0.0.obs;

  Rx<ValidationModel> cardNumber = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> cardHolderName = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> expiryMonth = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> expiryYear = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> expiryDate = Rx(ValidationModel(value: ''));
  Rx<ValidationModel> cvvCode = Rx(ValidationModel(value: ''));
  RxBool setAsDefaultPayment = false.obs;
  RxString cardType = 'unknown'.obs;

  Rx<ExpandableController> cardPaymentExpandable = Rx(ExpandableController());
  Rx<ExpandableController> mobileBankingPaymentExpandable =
      Rx(ExpandableController());

  RxList<PaymentCardModel> cardList = RxList<PaymentCardModel>.empty();

  Future<void> initCard() async {
    final List<dynamic>? result =
        _storage.read<List<dynamic>>(AppConstant.cardListKey);
    if (result != null) {
      cardList.clear();
      cardList.addAll(
        result
            .map(
              // ignore: implicit_dynamic_parameter
              (e) => PaymentCardModel.fromJson(e as Map<String, dynamic>),
            )
            .toList(),
      );
    }
  }

  void clearCardForm() {
    cardNumber = Rx(ValidationModel(value: ''));
    cardHolderName = Rx(ValidationModel(value: ''));
    expiryDate = Rx(ValidationModel(value: ''));
    expiryMonth = Rx(ValidationModel(value: ''));
    expiryYear = Rx(ValidationModel(value: ''));
    cvvCode = Rx(ValidationModel(value: ''));
    setAsDefaultPayment = false.obs;
  }

  void onChangeCardNumber(String value) {
    cardNumber.value = cardNumberValidate(value);
  }

  ValidationModel cardNumberValidate(String value) {
    final CCNumValidationResults ccNumResults =
        _ccValidator.validateCCNum(value);

    cardType.value = ccNumResults.ccType.toString().split('.')[1];
    if (!ccNumResults.isValid && !ccNumResults.isPotentiallyValid) {
      return ValidationModel(
        value: value,
        isError: !ccNumResults.isValid,
        error: ccNumResults.message,
      );
    } else {
      return ValidationModel(
        value: value,
      );
    }
  }

  void onChangeCardHolderName(String value) {
    cardHolderName.value = ValidationHelper.isNotEmptyValidate(value);
  }

  void onChangeExpiryDate(String value) {
    expiryDate.value = expiryDateValidate(value);
  }

  ValidationModel expiryDateValidate(String value) {
    final ValidationResults expDateResults =
        _ccValidator.validateExpDate(value);
    if (value.length > 4) {
      List<int> expList = CardUtil.getExpiryDate(value);
      final DateTime now = DateTime.now();
      final int nowYear = int.parse(now.year.toString().substring(2));

      if (expList.last < nowYear ||
          (expList.last == nowYear && expList.first < now.month)) {
        return ValidationModel(
          value: value,
          isError: !expDateResults.isValid,
          error: 'Expired card',
        );
      }
    }
    if (!expDateResults.isValid && !expDateResults.isPotentiallyValid) {
      return ValidationModel(
        value: value,
        isError: !expDateResults.isValid,
        error: expDateResults.message,
      );
    } else {
      return ValidationModel(
        value: value,
      );
    }
  }

  void onChangeCVVCode(String value) {
    cvvCode.value = cvvCodeValidate(value);
  }

  ValidationModel cvvCodeValidate(String value) {
    if (value.isEmpty) {
      return ValidationModel(
        value: '',
        error: 'Please fill in',
        isError: true,
      );
    } else if (value.length >= 3) {
      return ValidationModel(
        value: value,
      );
    } else {
      return ValidationModel(value: value);
    }
  }

  bool isCardFormValid() {
    cardNumber.value = cardNumberValidate(cardNumber.value.value!);
    cardHolderName.value =
        ValidationHelper.isNotEmptyValidate(cardHolderName.value.value);
    expiryDate.value = expiryDateValidate(expiryDate.value.value!);
    cvvCode.value = cvvCodeValidate(cvvCode.value.value!);

    return !cardNumber.value.isError &&
        !cardHolderName.value.isError &&
        !expiryDate.value.isError &&
        !cvvCode.value.isError;
  }

  Future<void> saveCard() async {
    final List<int> expList = CardUtil.getExpiryDate(expiryDate.value.value!);
    final String cardNumberResult =
        cardNumber.value.value!.split(' ').map((e) => e.trim()).join();
    if (setAsDefaultPayment.value) {
      cardList.value = cardList.map((element) {
        element = PaymentCardModel(
          number: element.number,
          type: element.type,
          holderName: element.holderName,
          month: element.month,
          year: element.year,
          cvv: element.cvv,
          defaultCard: false,
        );
        return element;
      }).toList();
    }

    final PaymentCardModel newCard = PaymentCardModel(
      number: cardNumberResult,
      type: cardType.value,
      holderName: cardHolderName.value.value!,
      month: expList.first,
      year: expList.last,
      cvv: int.parse(cvvCode.value.value!),
      defaultCard: setAsDefaultPayment.value,
    );
    cardList.add(newCard);
    _storage.write(AppConstant.cardListKey, cardList);
  }

  Future<void> deleteCard(String cardNumber) async {
    cardList.removeWhere((element) => element.number == cardNumber);
    _storage.write(AppConstant.cardListKey, cardList);
  }
}
