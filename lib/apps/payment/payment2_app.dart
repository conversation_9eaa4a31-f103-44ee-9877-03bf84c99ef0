import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:samitivej_flutter_app/apps/address/address_app.dart';
import 'package:samitivej_flutter_app/apps/user/user_app.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/models/coupon/coupon_model.dart';
import 'package:samitivej_flutter_app/models/credit_card_payment_status_model.dart';
import 'package:samitivej_flutter_app/models/food_purchase_model.dart';
import 'package:samitivej_flutter_app/models/invoice_item_model.dart';
import 'package:samitivej_flutter_app/models/invoice_model.dart';
import 'package:samitivej_flutter_app/models/merchant_ids_model.dart';
import 'package:samitivej_flutter_app/models/payment_customer_model.dart';
import 'package:samitivej_flutter_app/models/purchase_model.dart';
import 'package:samitivej_flutter_app/models/qr_code_payment_status_model.dart';
import 'package:samitivej_flutter_app/models/request_credit_card_payment_result_model.dart';
import 'package:samitivej_flutter_app/models/request_qr_code_payment_result_model.dart';
import 'package:samitivej_flutter_app/models/response_model.dart';
import 'package:samitivej_flutter_app/services/api_client.dart';

class Payment2App extends GetxController {
  /// API client for the payment services.
  final ApiClient _client = ApiClient(baseUrl: ApiConstant.paymentDomain);

  // The user controller.
  final UserApp _userApp = Get.find<UserApp>();

  // The address controller.
  final AddressApp _addressApp = Get.find<AddressApp>();

  /// Whether the getting merchant IDs is in progress or not.
  RxBool isGetMerchantIdsLoading = false.obs;

  /// The merchant IDs.
  Rx<MerchantIdsModel> merchantIds = Rx(MerchantIdsModel());

  /// Whether the request for payment with credit card is in progress or not.
  RxBool isRequestCreditCardPaymentLoading = false.obs;

  /// The result of requesting credit card payment.
  Rx<RequestCreditCardPaymentResultModel> requestCreditCardPaymentResult =
      Rx(RequestCreditCardPaymentResultModel());

  /// Whether the request for getting status of a payment with credit card is in progress or not.
  RxBool isGetCreditCardPaymentStatusLoading = false.obs;

  /// The status of a credit card payment.
  Rx<CreditCardPaymentStatusModel> creditCardPaymentStatus =
      Rx(CreditCardPaymentStatusModel());

  /// Whether the request for payment with QR code is in progress or not.
  RxBool isRequestQrCodePaymentLoading = false.obs;

  /// The result of requesting QR code payment.
  Rx<RequestQrCodePaymentResultModel> requestQrCodePaymentResult =
      Rx(RequestQrCodePaymentResultModel());

  /// Whether the request for getting status of a payment with QR code is in progress or not.
  RxBool isGetQrCodePaymentStatusLoading = false.obs;

  /// The status of a QR code payment.
  Rx<QrCodePaymentStatusModel> qrCodePaymentStatus =
      Rx(QrCodePaymentStatusModel());

  /// Whether the inquiring of a payment customer is in progress or not.
  RxBool isInquirePaymentCustomerLoading = false.obs;

  /// The payment customer.
  Rx<PaymentCustomerModel?> paymentCustomer = Rx(null);

  Rx<CouponModel?> selectedCoupon = Rx(null);

  /// Gets merchant IDs.
  Future<void> getMerchantIds() async {
    try {
      isGetMerchantIdsLoading.value = true;
      final ResponseModel response = await _client.post(
        ApiConstant.getMerchantIds,
      );
      merchantIds.value =
          MerchantIdsModel.fromJson(response.data as Map<String, dynamic>);
      isGetMerchantIdsLoading.value = false;
    } on DioException {
      isGetMerchantIdsLoading.value = false;
    }
  }

  /// Requests a payment with credit card.
  Future<void> requestCreditCardPayment(
    String? kbankToken,
    List<InvoiceModel>? invoices,
    List<PurchaseModel>? purchases,
    List<FoodPurchaseModel>? foodPurchases,
    bool saveCard,
    String? currency,
    String merchantIdPrefix,
  ) async {
    if (invoices == null) {
      return;
    }
    try {
      isRequestCreditCardPaymentLoading.value = true;

      final items = <Map>[];
      final String firstName = invoices.first.patient?.firstName ?? '';
      final String lastName = invoices.first.patient?.lastName ?? '';
      final String episodeDate = invoices.first.invoiceDate ?? '';
      final String episodeTime = invoices.first.invoiceTime ?? '';
      final String hn = invoices.first.hn ?? '';
      final bool isUser = invoices.first.patient?.isUser ?? false;
      final List<InvoiceItemModel> orderItems = invoices.first.items;

      for (final invoice in invoices) {
        items.add({
          'source': 'HIS',
          'description': invoice.invoiceNumber,
          'orderNumber': invoice.episodeNumber,
          'purchasePrice': invoice.totalNetAmount,
          'deliveryFee': 0,
          'quantity': 1,
          'additionalData': invoice.invoiceNumber,
          'branchNo': invoice.branchNumber,
          'firstName': firstName,
          'lastName': lastName,
          'hn': hn,
          'episodeDate': episodeDate,
          'episodeTime': episodeTime,
          'isUser': isUser,
          'orderItems': orderItems
        });
      }
      if (purchases != null) {
        for (final purchase in purchases) {
          for (var element in purchase.items) {
            items.add({
              'source': 'TOTALSOL',
              'description': element.packageName,
              'orderNumber': purchase.orderNumber,
              'purchasePrice': element.priceAfterDiscount,
              'deliveryFee': 0,
              'quantity': element.quantity,
              'additionalData': '',
              'branchNo': null
            });
          }
        }
      }
      if (foodPurchases != null) {
        final addressId = _addressApp.selectedAddressId.value;
        for (final purchase in foodPurchases) {
          items.add({
            'source': 'FOOD',
            'description': purchase.purchase?.plan?.name,
            'orderNumber': purchase.purchase?.purchase?.orderNumber,
            'purchasePrice': purchase.purchase?.purchase?.price,
            'deliveryFee': purchase.deliveryFee ?? 0,
            'quantity': 1,
            'additionalData': '',
            'branchNo': null,
            'shipmentAddressId': addressId,
          });
        }
      }

      double totalPrice = (double.parse(invoices.first.totalNetAmount ?? '0')) -
          (selectedCoupon.value?.promotion.discountValue ?? 0);

      String? merchantId;
      switch (merchantIdPrefix) {
        case MerchantIdsModel.prefixHis:
          merchantId = merchantIds.value.hisDcc;
          break;
        case MerchantIdsModel.prefixFhf:
          merchantId = merchantIds.value.fhfDcc;
      }

      final ResponseModel response = await _client.post(
        ApiConstant.requestCreditCardPayment,
        data: {
          'setupPaymentRequestBean': {
            'currency': 'THB',
            'description': DateTime.now().toIso8601String(),
            'orderDetailBean': items,
            'token': kbankToken,
            'paymentMethod': 'card',
            'paymentType': 'credit-card',
            'rememberCard': saveCard,
            'merchantId': merchantId,
            'dccCurrency': currency,
            'couponDiscount':
                selectedCoupon.value?.promotion.discountValue ?? '',
            'couponCode': selectedCoupon.value?.promotionCouponCode?.code ?? '',
            'totalPrice': totalPrice,
          }
        },
      );
      requestCreditCardPaymentResult.value =
          RequestCreditCardPaymentResultModel.fromJson(
              response.data as Map<String, dynamic>);
      isRequestCreditCardPaymentLoading.value = false;
    } on DioException {
      requestCreditCardPaymentResult.value =
          RequestCreditCardPaymentResultModel();
      isRequestCreditCardPaymentLoading.value = false;
    }
  }

  /// Gets status of a payment with credit card.
  Future<void> getCreditCardPaymentStatus(
    String? chargeId,
  ) async {
    try {
      isGetCreditCardPaymentStatusLoading.value = true;
      final ResponseModel response = await _client.post(
        ApiConstant.getCreditCardPaymentStatus,
        data: {
          'paymentNotifyRequestBean': {
            'id': chargeId,
          }
        },
      );
      creditCardPaymentStatus.value = CreditCardPaymentStatusModel.fromJson(
          response.data as Map<String, dynamic>);
      isGetCreditCardPaymentStatusLoading.value = false;
    } on DioException {
      creditCardPaymentStatus.value = CreditCardPaymentStatusModel();
      isGetCreditCardPaymentStatusLoading.value = false;
    }
  }

  /// Requests a payment with QR code.
  Future<void> requestQrCodePayment(
    List<InvoiceModel>? invoices,
    List<PurchaseModel>? purchases,
    List<FoodPurchaseModel>? foodPurchases,
    String merchantIdPrefix,
  ) async {
    try {
      isRequestQrCodePaymentLoading.value = true;

      final items = <Map>[];
      String hn = '';
      bool isUser = true;
      if (invoices != null) {
        final String firstName = invoices.first.patient?.firstName ?? '';
        final String lastName = invoices.first.patient?.lastName ?? '';
        final String episodeDate = invoices.first.invoiceDate ?? '';
        final String episodeTime = invoices.first.invoiceTime ?? '';
        hn = invoices.first.hn ?? '';
        isUser = invoices.first.patient?.isUser ?? false;
        final List<InvoiceItemModel> orderItems = invoices.first.items;

        for (final invoice in invoices) {
          items.add({
            'source': 'HIS',
            'description': invoice.invoiceNumber,
            'orderNumber': invoice.episodeNumber,
            'purchasePrice': invoice.totalNetAmount,
            'deliveryFee': 0,
            'quantity': 1,
            'additionalData': invoice.invoiceNumber,
            'branchNo': invoice.branchNumber,
            'firstName': firstName,
            'lastName': lastName,
            'hn': hn,
            'episodeDate': episodeDate,
            'episodeTime': episodeTime,
            'isUser': isUser,
            'orderItems': orderItems
          });
        }
      }
      if (purchases != null) {
        for (final purchase in purchases) {
          for (var element in purchase.items) {
            items.add({
              'source': 'TOTALSOL',
              'description': element.packageName,
              'orderNumber': purchase.orderNumber,
              'purchasePrice': element.priceAfterDiscount,
              'deliveryFee': 0,
              'quantity': element.quantity,
              'additionalData': '',
              'branchNo': null
            });
          }
        }
      }
      if (foodPurchases != null) {
        final addressId = _addressApp.selectedAddressId.value;
        for (final purchase in foodPurchases) {
          items.add({
            'source': 'FOOD',
            'description': purchase.purchase?.plan?.name,
            'orderNumber': purchase.purchase?.purchase?.orderNumber,
            'purchasePrice': purchase.purchase?.purchase?.price,
            'deliveryFee': purchase.deliveryFee ?? 0,
            'quantity': 1,
            'additionalData': '',
            'branchNo': null,
            'shipmentAddressId': addressId,
          });
        }
      }

      final memberId = await _userApp.getMemberId();
      String? merchantId;
      switch (merchantIdPrefix) {
        case MerchantIdsModel.prefixHis:
          merchantId = merchantIds.value.hisDcc;
          break;
        case MerchantIdsModel.prefixFhf:
          merchantId = merchantIds.value.fhfDcc;
      }
      final ResponseModel response = await _client.post(
        ApiConstant.requestQrCodePayment,
        data: {
          'setupPaymentRequestBean': {
            'currency': 'THB',
            'description': DateTime.now().toIso8601String(),
            'orderDetailBean': items,
            'token': '',
            'paymentMethod': 'qr',
            'paymentType': 'qr',
            'rememberCard': false,
            'memberId': '$memberId',
            'merchantId': merchantId,
            'hn': hn,
            'isUser': isUser
          }
        },
      );
      requestQrCodePaymentResult.value =
          RequestQrCodePaymentResultModel.fromJson(
              response.data as Map<String, dynamic>);
      isRequestQrCodePaymentLoading.value = false;
    } on DioException {
      requestQrCodePaymentResult.value = RequestQrCodePaymentResultModel();
      isRequestQrCodePaymentLoading.value = false;
    }
  }

  /// Gets status of a payment with QR code.
  Future<void> getQrCodePaymentStatus(String? orderId) async {
    try {
      isGetQrCodePaymentStatusLoading.value = true;
      final ResponseModel response = await _client.post(
        ApiConstant.getQrCodePaymentStatus,
        data: {
          'paymentNotifyRequestBean': {
            'id': orderId,
          }
        },
      );
      qrCodePaymentStatus.value = QrCodePaymentStatusModel.fromJson(
          response.data as Map<String, dynamic>);
      isGetQrCodePaymentStatusLoading.value = false;
    } on DioException {
      qrCodePaymentStatus.value = QrCodePaymentStatusModel();
      isGetQrCodePaymentStatusLoading.value = false;
    }
  }

  /// Inquires a customer info.
  Future<void> inquireCustomer() async {
    try {
      isInquirePaymentCustomerLoading.value = true;
      final ResponseModel response = await _client.post(
          ApiConstant.inquirePaymentCustomer,
          options: Options(headers: {"MerchantId": merchantIds.value.hisDcc}));
      paymentCustomer.value = response.data != null
          ? PaymentCustomerModel.fromJson(response.data!)
          : null;
      isInquirePaymentCustomerLoading.value = false;
    } on DioException {
      paymentCustomer.value = null;
      isInquirePaymentCustomerLoading.value = false;
    }
  }
}
