import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:samitivej_flutter_app/apps/common/common_app.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/models/challenge_model.dart';
import 'package:samitivej_flutter_app/models/challenges_by_dates_model.dart';
import 'package:samitivej_flutter_app/models/daily_task_points_model.dart';
import 'package:samitivej_flutter_app/models/food_task_model.dart';
import 'package:samitivej_flutter_app/models/mood_item_model.dart';
import 'package:samitivej_flutter_app/models/response_model.dart';
import 'package:samitivej_flutter_app/models/routine_data_model.dart';
import 'package:samitivej_flutter_app/services/api_client.dart';

class CalendarApp extends GetxController {
  /// API client for the fit services.
  final ApiClient _client = ApiClient();

  final CommonApp _commonApp = Get.find<CommonApp>();

  /// Whether loading mood.
  RxBool isMoodLoading = false.obs;

  /// Mood data.
  Rx<MoodItemModel> moodData = Rx(MoodItemModel());

  /// Whether loading moods.
  RxBool isMoodsLoading = false.obs;

  /// Moods data.
  Rx<List<MoodItemModel>> moods = Rx([]);

  /// Whether the loading of daily food tasks of the current user is in progress or not.
  RxBool isDailyFoodTasksLoading = false.obs;

  /// The daily food tasks of the current user.
  Rx<List<FoodTaskModel>> dailyFoodTasks = Rx([]);

  /// Whether the loading of daily fit tasks of the current user is in progress or not.
  RxBool isDailyFitTasksLoading = false.obs;

  /// The fit tasks of the current user.
  RxList<RoutineDataModel> dailyFitTasks = RxList.empty();

  /// Whether the loading of daily food task points of the current user is in progress or not.
  RxBool isDailyFoodTaskPointsLoading = false.obs;

  /// The daily food task points of the current user.
  Rx<List<DailyTaskPointsModel>> dailyFoodTaskPoints = Rx([]);

  /// Whether the loading of daily fit task points of the current user is in progress or not.
  RxBool isDailyFitTaskPointsLoading = false.obs;

  /// The daily fit task points of the current user.
  Rx<List<DailyTaskPointsModel>> dailyFitTaskPoints = Rx([]);

  /// Whether the loading of daily challenge points of the current user is in progress or not.
  RxBool isDailyChallengePointsLoading = false.obs;

  /// The daily challenge points of the current user.
  Rx<List<DailyTaskPointsModel>> dailyChallengePoints = Rx([]);

  /// Whether the loading of daily challenges of the current user is in progress or not.
  RxBool isGetDailyChallengesLoading = false.obs;

  /// The daily challenges of the current user.
  RxList<ChallengeModel> dailyChallenges = RxList.empty();

  /// Whether the loading of current food plan of the current user is in progress or not.
  RxBool isGetFoodPlanLoading = false.obs;

  /// Whether the current user has a food plan or not.
  RxBool hasFoodPlan = false.obs;

  /// Whether the loading of current fit plan of the current user is in progress or not.
  RxBool isGetFitPlanLoading = false.obs;

  /// Whether the current user has a fit plan or not.
  RxBool hasFitPlan = false.obs;

  /// Gets mood data.
  Future<void> getMood(DateTime date) async {
    try {
      isMoodLoading.value = true;

      final ResponseModel response = await _client.post(
        ApiConstant.getMood,
        data: {
          'date': DateFormat('y-MM-dd').format(date),
        },
      );
      moodData.value = MoodItemModel.fromJson(
        response.data as Map<String, dynamic>,
      );

      isMoodLoading.value = false;
    } catch (e) {
      e.printError();
      isMoodLoading.value = false;
    }
  }

  /// Gets moods of the current user on [month] and [year].
  Future<void> getMoods(int year, int month) async {
    try {
      isMoodsLoading.value = true;
      final ResponseModel response = await _client.post(
        ApiConstant.getMoodsByDateRange,
        data: {
          'startDate':
              '${year.toString().padLeft(4, '0')}-${month.toString().padLeft(2, '0')}-01',
          'endDate':
              '${year.toString().padLeft(4, '0')}-${month.toString().padLeft(2, '0')}-${DateUtils.getDaysInMonth(year, month).toString().padLeft(2, '0')}'
        },
      );
      moods.value = (response.data?['listData'] as List)
          .map((item) => MoodItemModel.fromJson(item as Map<String, dynamic>))
          .toList(growable: false);
      isMoodsLoading.value = false;
    } catch (e) {
      e.printError();
      moods.value = [];
      isMoodsLoading.value = false;
    }
  }

  /// Gets food plan of the current user.
  Future<void> getFoodPlan() async {
    try {
      isGetFoodPlanLoading.value = true;
      await _client.post(ApiConstant.getFoodPlan);
      hasFoodPlan.trigger(true);
      isGetFoodPlanLoading.value = false;
    } on DioException {
      hasFoodPlan.trigger(false);
      isGetFoodPlanLoading.value = false;
    }
  }

  /// Gets fit plan of the current user.
  Future<void> getFitPlan() async {
    try {
      isGetFitPlanLoading.value = true;
      await _client.post(ApiConstant.getFitPlan);
      hasFitPlan.trigger(true);
      isGetFitPlanLoading.value = false;
    } on DioException {
      hasFitPlan.trigger(false);
      isGetFitPlanLoading.value = false;
    }
  }

  /// Gets daily food tasks of the current user on [date].
  Future<void> getDailyFoodTasks(DateTime date) async {
    try {
      isDailyFoodTasksLoading.value = true;
      final today = DateFormat('yyyy-MM-dd').format(date);
      final ResponseModel response = await _client.post(
        ApiConstant.getDailyFoodTasks,
        data: {'date': today},
      );
      dailyFoodTasks.value = (response.data?['listData'] as List)
          .map((task) => FoodTaskModel.fromJson(task as Map<String, dynamic>))
          .toList(growable: false);
      isDailyFoodTasksLoading.value = false;
    } on DioException {
      dailyFoodTasks.value = [];
      isDailyFoodTasksLoading.value = false;
    }
  }

  /// Gets daily fit tasks of the current user on [date].
  Future<void> getDailyFitTasks(DateTime datetime) async {
    final startDate = DateFormat('yyyy-MM-ddTHH:mm:ss')
        .format(DateTime(datetime.year, datetime.month, datetime.day));
    final endDate = DateFormat('yyyy-MM-ddTHH:mm:ss').format(
        DateTime(datetime.year, datetime.month, datetime.day, 23, 59, 59));
    try {
      isDailyFitTasksLoading.value = true;
      final Map<String, dynamic> body = {
        'getFitTaskRequestBean': {
          'start_date': startDate,
          'end_date': endDate,
        }
      };

      final ResponseModel response =
          await _client.post(ApiConstant.getFitDailyTask,
              data: body,
              options: Options(
                headers: {
                  "Accept-Language": _commonApp.selectedLanguage.value,
                },
              ));
      final List listResponse = response.data!['listData'] as List;
      dailyFitTasks.value = listResponse
          .map((e) => RoutineDataModel.fromJson(e as Map<String, dynamic>))
          .toList();
      isDailyFitTasksLoading.value = false;
    } catch (e) {
      e.printError();
      dailyFitTasks.clear();
      isDailyFitTasksLoading.value = false;
    }
  }

  /// Gets daily challenges of the current user on [date].
  Future<void> getDailyChallenges(DateTime date) async {
    try {
      isGetDailyChallengesLoading.value = true;
      final ResponseModel response = await _client.post(
        ApiConstant.getDailyChallenges,
        data: {'date': DateFormat('yyyy-MM-dd').format(date)},
      );
      dailyChallenges.value = (response.data?['listData'] as List)
          .map((challenge) =>
              ChallengeModel.fromJson(challenge as Map<String, dynamic>))
          .toList(growable: false);
      isGetDailyChallengesLoading.value = false;
    } on DioException {
      dailyChallenges.clear();
      isGetDailyChallengesLoading.value = false;
    }
  }

  /// Gets daily food task points of the current user on [month] and [year].
  Future<void> getDailyFoodTaskPoints(int year, int month) async {
    try {
      isDailyFoodTaskPointsLoading.value = true;
      final ResponseModel response = await _client.post(
        ApiConstant.getDailyFoodTaskPoints,
        data: {
          'date':
              '${year.toString().padLeft(4, '0')}-${month.toString().padLeft(2, '0')}-01',
        },
      );
      dailyFoodTaskPoints.value = (response.data?['listData'] as List)
          .map((item) =>
              DailyTaskPointsModel.fromJson(item as Map<String, dynamic>))
          .toList(growable: false);
      isDailyFoodTaskPointsLoading.value = false;
    } on DioException {
      dailyFoodTaskPoints.value = <DailyTaskPointsModel>[];
      isDailyFoodTaskPointsLoading.value = false;
    }
  }

  /// Gets daily fit task points of the current user on [month] and [year].
  Future<void> getDailyFitTaskPoints(int year, int month) async {
    final startDate = DateFormat('yyyy-MM-ddTHH:mm:ss').format(
      DateTime(year, month, 1),
    );
    final endDate = DateFormat('yyyy-MM-ddTHH:mm:ss').format(
      DateTime(year, month + 1, 0, 23, 59, 59, 999),
    );
    try {
      isDailyFitTaskPointsLoading.value = true;
      final Map<String, dynamic> body = {
        'getFitTaskRequestBean': {
          'start_date': startDate,
          'end_date': endDate,
        }
      };
      final ResponseModel response =
          await _client.post(ApiConstant.getFitDailyTask,
              data: body,
              options: Options(
                headers: {
                  "Accept-Language": _commonApp.selectedLanguage.value,
                },
              ));

      final List listResponse = response.data!['listData'] as List;
      final data = listResponse
          .map((e) => RoutineDataModel.fromJson(e as Map<String, dynamic>))
          .toList();
      data.sort((a, b) => (a.date ?? '').compareTo(b.date ?? ''));

      final taskPoints = <DailyTaskPointsModel>[];
      String? lastDate;
      int doneTasksCount = 0;
      int allTasksCount = 0;
      for (final item in data) {
        final date = item.date?.substring(0, 10);
        if (lastDate == date) {
          doneTasksCount += item.status == RoutineDataModel.statusDone ? 1 : 0;
          allTasksCount++;
        } else {
          if (allTasksCount != 0) {
            taskPoints.add(DailyTaskPointsModel(
              date: lastDate,
              percent: (doneTasksCount / allTasksCount) * 100,
            ));
          }
          doneTasksCount = item.status == RoutineDataModel.statusDone ? 1 : 0;
          allTasksCount = 1;
          lastDate = date;
        }
      }
      if (lastDate != null && allTasksCount != 0) {
        taskPoints.add(DailyTaskPointsModel(
          date: lastDate,
          percent: (doneTasksCount / allTasksCount) * 100,
        ));
      }
      dailyFitTaskPoints.value = taskPoints;

      isDailyFitTaskPointsLoading.value = false;
    } catch (e) {
      e.printError();
      isDailyFitTaskPointsLoading.value = false;
    }
  }

  /// Gets daily challenge points of the current user on [month] and [year].
  Future<void> getDailyChallengePoints(int year, int month) async {
    try {
      isDailyFitTaskPointsLoading.value = true;

      final startDate = DateFormat('yyyy-MM-dd').format(
        DateTime(year, month, 1),
      );
      final endDate = DateFormat('yyyy-MM-dd').format(
        DateTime(year, month + 1, 0),
      );
      final ResponseModel response = await _client.post(
        ApiConstant.getChallengesByDates,
        data: {
          'startDate': startDate,
          'endDate': endDate,
        },
      );

      final List listResponse = response.data!['listData'] as List;
      final data = listResponse
          .map(
              (e) => ChallengesByDatesModel.fromJson(e as Map<String, dynamic>))
          .toList();

      final taskPoints = <DailyTaskPointsModel>[];
      for (final item in data) {
        if (item.challenges?.isNotEmpty ?? false) {
          int doneTasksCount = item.challenges!
              .where((challenge) =>
                  challenge.statusId == ChallengeModel.statusIdSuccess)
              .length;
          taskPoints.add(DailyTaskPointsModel(
            date: item.date,
            percent: (doneTasksCount / item.challenges!.length) * 100,
          ));
        }
      }
      dailyChallengePoints.value = taskPoints;

      isDailyFitTaskPointsLoading.value = false;
    } catch (e) {
      e.printError();
      isDailyFitTaskPointsLoading.value = false;
    }
  }
}
