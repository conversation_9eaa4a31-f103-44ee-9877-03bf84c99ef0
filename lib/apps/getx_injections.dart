import 'package:get/get.dart';
import 'package:samitivej_flutter_app/apps/address/address_app.dart';
import 'package:samitivej_flutter_app/apps/appointment/plus_appointment_app.dart';
import 'package:samitivej_flutter_app/apps/article/article_app.dart';
import 'package:samitivej_flutter_app/apps/auth/auth_app.dart';
import 'package:samitivej_flutter_app/apps/auth/change_password_form_app.dart';
import 'package:samitivej_flutter_app/apps/calendar/calendar_app.dart';
import 'package:samitivej_flutter_app/apps/cart/cart_app.dart';
import 'package:samitivej_flutter_app/apps/coin/coin_app.dart';
import 'package:samitivej_flutter_app/apps/common/calendar_pageview_controller_app.dart';
import 'package:samitivej_flutter_app/apps/common/common_app.dart';
import 'package:samitivej_flutter_app/apps/feature_flags/feature_flags_app.dart';
import 'package:samitivej_flutter_app/apps/guardian/guardian_controller.dart';
import 'package:samitivej_flutter_app/apps/health_app/health_app.dart';
import 'package:samitivej_flutter_app/apps/home/<USER>';
import 'package:samitivej_flutter_app/apps/notification/notification_app.dart';
import 'package:samitivej_flutter_app/apps/payment/payment2_app.dart';
import 'package:samitivej_flutter_app/apps/payment/payment_app.dart';
import 'package:samitivej_flutter_app/apps/queue/queue_app.dart';
import 'package:samitivej_flutter_app/apps/user/user_app.dart';
import 'package:samitivej_flutter_app/screen/home_fit/controllers/home_fit_app.dart';
import 'package:samitivej_flutter_app/services/connect_device_service.dart';

class GetXInjections implements Bindings {
  @override
  void dependencies() {
    Get.put(CommonApp());
    Get.put(AuthApp());
    Get.put(UserApp());
    Get.put(CoinApp());
    Get.put(CalendarApp());
    Get.put(HomeApp());
    Get.put(ChangePasswordFormApp());
    Get.put(AddressApp());
    Get.put(CartApp());
    Get.put(PaymentApp());
    Get.put(Payment2App());
    Get.put(CalendarPageviewControllerApp());
    Get.put(PlusAppointmentApp());
    Get.put(ArticleApp());
    Get.put(HealthApp());
    Get.put(QueueApp());
    Get.put(NotificationApp());
    Get.put(HomeFitApp());
    Get.put(GuardianController());
    Get.put(FeatureFlagsApp());
    Get.put(ConnectDeviceService());
  }
}
