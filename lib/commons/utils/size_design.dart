import 'package:flutter/material.dart';

class SizeDesign {
  static late double? screenWidth;
  static late double? screenHeight;
  static late Orientation? orientation;

  SizeDesign.init(BuildContext context) {
    var mediaQueryData = MediaQuery.of(context);
    orientation = mediaQueryData.orientation;
    screenWidth = mediaQueryData.size.width;
    screenHeight = mediaQueryData.size.height;

    /*if (orientation == Orientation.portrait) {
      screenWidth = mediaQueryData.size.width;
      screenHeight = mediaQueryData.size.height;
    } else {
      screenWidth = mediaQueryData.size.height;
      screenHeight = mediaQueryData.size.width;
    }*/
  }

  /// Get the proportionate height as per screen size
  static double heightOf(double height) {
    // 812 is the layout height that designer use (iPhone 11 Pro)
    // 896 is the layout height that designer use (iPhone 11)
    const designHeight = 896.0;
    if (SizeDesign.orientation == Orientation.portrait) {
      return ((screenHeight ?? designHeight) / designHeight) * height;
    } else {
      return ((screenWidth ?? designHeight) / designHeight) * height;
    }
  }

  /// Get the proportionate width as per screen size
  static double widthOf(double width) {
    // 375 is the layout width that designer use (iPhone 11 Pro)
    // 414 is the layout height that designer use (iPhone 11)
    const designWidth = 414.0;
    if (SizeDesign.orientation == Orientation.portrait) {
      return ((screenWidth ?? designWidth) / designWidth) * width;
    } else {
      return ((screenHeight ?? designWidth) / designWidth) * width;
    }
  }
}
