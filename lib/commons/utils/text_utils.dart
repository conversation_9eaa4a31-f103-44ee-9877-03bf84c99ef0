import 'package:intl/intl.dart';

/// Utilities for text.
class TextUtils {
  TextUtils._();

  /// Format for currency.
  static final _currencyFormat = NumberFormat('#,##0.00');

  /// Returns a price string with comma thousand separator (if required),
  /// two decimal places if the double has a non-zero fraction part.
  /// If [showCurrencySign] is true, the result is prefixed with [currencySign].
  static String formatPrice(double price,
      {bool showCurrencySign = false, String currencySign = '฿'}) {
    final result = _currencyFormat.format(price);
    return (showCurrencySign ? currencySign : '') +
        (result.endsWith('.00')
            ? result.substring(0, result.length - 3)
            : result);
  }
}
