import 'package:intl/intl.dart';
import 'package:samitivej_flutter_app/utils/extensions/date_format.dart';

class DateTimeUtils {
  DateTimeUtils._();

  /// Parse string of date in [format] to [DateTime] object
  static DateTime? parseStringToDateTime(String dateString, String format) {
    try {
      var inputFormat = DateFormat(format);
      return inputFormat.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  static DateTime? parseStringToDateTimeLocal(String dateString, String format, String? locale) {
    try {
      Intl.defaultLocale = locale;
      var inputFormat = DateFormat(format, locale);
      if (locale == "th") {
        return inputFormat.parse(dateString).setBuddhaYear();
      } else {
        return inputFormat.parse(dateString);
      }
    } catch (e) {
      return null;
    }
  }
}
