import 'package:samitivej_flutter_app/commons/api/network_info.dart';
import 'package:samitivej_flutter_app/commons/di/app_injector.dart';
import 'package:samitivej_flutter_app/presentation/widgets/dialog/no_internet_dialog.dart';

/// Utility class for handling internet connectivity checks with retry functionality
class InternetConnectionHelper {
  /// Executes an action after checking internet connectivity
  /// If no internet, shows retry dialog that will re-execute the action when connected
  ///
  /// [action] - The function to execute if internet is available
  /// [showNoInternetDialog] - Whether to show the no internet dialog (default: true)
  ///
  /// Returns true if action was executed, false if no internet connection
  static Future<bool> executeWithInternetCheck(
    Future<void> Function() action, {
    bool showNoInternetDialog = true,
  }) async {
    try {
      final isConnected = await AppInjector.get<NetworkInfo>().isConnected;

      if (!isConnected) {
        if (showNoInternetDialog) {
          await NoInternetDialog.show(
            onRetry: () => executeWithInternetCheck(
              action,
              showNoInternetDialog: showNoInternetDialog,
            ),
          );
        }
        return false;
      }

      // Execute the action if internet is available
      await action();
      return true;
    } catch (e) {
      // Handle any network check errors
      if (showNoInternetDialog) {
        await NoInternetDialog.show(
          onRetry: () => executeWithInternetCheck(
            action,
            showNoInternetDialog: showNoInternetDialog,
          ),
        );
      }
      return false;
    }
  }

  /// Check internet connectivity without executing any action
  /// Returns true if connected, false otherwise
  static Future<bool> isInternetConnected() async {
    try {
      return await AppInjector.get<NetworkInfo>().isConnected;
    } catch (e) {
      return false;
    }
  }

  /// Execute an action only if internet is connected
  /// Does not show any dialog if not connected
  /// Returns true if action was executed, false if no internet
  static Future<bool> executeIfConnected(Future<void> Function() action) async {
    final isConnected = await isInternetConnected();
    if (isConnected) {
      await action();
      return true;
    }
    return false;
  }
}
