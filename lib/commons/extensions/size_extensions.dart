import 'dart:math';

import 'package:samitivej_flutter_app/commons/utils/size_design.dart';

extension DoubleExtensions on double {
  /// Get the proportionate width as per screen size
  double get fitWidth {
    return SizeDesign.widthOf(this);
  }

  /// Get the proportionate height as per screen size
  double get fitHeight {
    return SizeDesign.heightOf(this);
  }
}

extension IntExtensions on int {
  /// Get the proportionate width as per screen size
  double get fitWidth {
    return SizeDesign.widthOf(toDouble());
  }

  /// Get the proportionate height as per screen size
  double get fitHeight {
    return SizeDesign.heightOf(toDouble());
  }

  /// Get the proportionate size of text as per screen size
  double get fitText {
    return min(SizeDesign.widthOf(toDouble()), SizeDesign.heightOf(toDouble()));
  }
}
