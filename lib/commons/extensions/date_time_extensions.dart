/// Extensions for DateTime to provide additional functionality
extension DateTimeExtensions on DateTime {
  /// Compares two DateTime objects considering only day, month, and year
  /// Returns true if this date is before [other] date (date-only comparison)
  bool isBeforeDate(DateTime other) {
    final thisDateOnly = DateTime(year, month, day);
    final otherDateOnly = DateTime(other.year, other.month, other.day);
    return thisDateOnly.isBefore(otherDateOnly);
  }

  /// Compares two DateTime objects considering only day, month, and year
  /// Returns true if this date is after [other] date (date-only comparison)
  bool isAfterDate(DateTime other) {
    final thisDateOnly = DateTime(year, month, day);
    final otherDateOnly = DateTime(other.year, other.month, other.day);
    return thisDateOnly.isAfter(otherDateOnly);
  }

  /// Compares two DateTime objects considering only day, month, and year
  /// Returns true if this date is the same as [other] date (date-only comparison)
  bool isSameDate(DateTime other) {
    return year == other.year && month == other.month && day == other.day;
  }

  /// Returns a new DateTime with only date components (time set to 00:00:00)
  DateTime get dateOnly => DateTime(year, month, day);
}
