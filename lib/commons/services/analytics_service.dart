import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:get/get.dart';
import 'package:samitivej_flutter_app/constants/analytics_constant.dart';
import 'dart:developer' as developer;

class AnalyticsService {
  AnalyticsService();

  static Future<void> logEventSignupResult({
    String? method,
    bool status = false,
  }) async {
    await FirebaseAnalytics.instance.logEvent(
      name: AnalyticsConstant.eventSignupResult,
      parameters: {"method": method ?? "", "status": status},
    );
  }

  static Future<void> logEventButtonClick({
    String? name,
  }) async {
    await FirebaseAnalytics.instance.logEvent(
      name: AnalyticsConstant.eventButtonClick,
      parameters: {
        "name": name ?? "",
      },
    );
  }

  static Future<void> logEventHomeInteraction({
    String? areaName,
    int? mediaGroupId,
  }) async {
    var parameters = <String, Object>{};

    parameters.addIf(areaName != null, "area_name", areaName ?? "");
    parameters.addIf(
        mediaGroupId != null, "media_group_id", mediaGroupId?.toString() ?? "");

    await FirebaseAnalytics.instance.logEvent(
      name: AnalyticsConstant.eventHomeInteraction,
      parameters: parameters.isEmpty ? {} : parameters,
    );
  }

  static Future<void> logEventInteraction({
    required String name,
    String? areaName,
    int? mediaGroupId,
    int? timeSecond,
    Map<String, dynamic>? customParameters,
  }) async {
    try {
      name = name.replaceAll('/', '_');
      name = name.replaceAll('.', '_');
      var parameters = <String, Object>{};
      if (customParameters != null) {
        parameters.addAll(customParameters
            .map((key, value) => MapEntry(key, value.toString())));
      }

      parameters.addIf(areaName != null, "area_name", areaName ?? "");
      parameters.addIf(mediaGroupId != null, "media_group_id",
          mediaGroupId?.toString() ?? "");
      parameters.addIf(
          timeSecond != null, "time_second", timeSecond?.toString() ?? "");
      await FirebaseAnalytics.instance
          .logEvent(name: name, parameters: parameters);
      developer.log('Analytics logEvent: $name, $parameters');
    } catch (e) {
      developer.log('Analytics logEvent error: $e');
    }
  }
}
