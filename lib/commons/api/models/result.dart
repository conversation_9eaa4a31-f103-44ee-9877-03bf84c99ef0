class Result<T> {
  Result._();

  factory Result.loading() = LoadingState<T>;

  factory Result.success(T value) = SuccessState<T>;

  factory Result.error(T? value) = ErrorState<T>;
}

class LoadingState<T> extends Result<T> {
  LoadingState() : super._();
}

class ErrorState<T> extends Result<T> {
  ErrorState(this.value) : super._();
  final T? value;
}

class SuccessState<T> extends Result<T> {
  SuccessState(this.value) : super._();
  final T value;
}
