import 'dart:developer' as developer;

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart' as get_x;
import 'package:samitivej_flutter_app/apps/auth/auth_app.dart';
import 'package:samitivej_flutter_app/commons/api/api_client.dart';
import 'package:samitivej_flutter_app/commons/api/models/result.dart';
import 'package:samitivej_flutter_app/constants/app_api.dart';
import 'package:samitivej_flutter_app/services/notification_service.dart';

class ApiClientImpl extends ApiClient {
  final _connectTimeout = 30000;
  final _receiveTimeout = 30000;
  late Dio _dio;

  ApiClientImpl({
    String? baseUrl,
  }) {
    if (baseUrl != null) {
      if (!baseUrl.startsWith("http")) {
        baseUrl = "https://$baseUrl";
      }
    } else {
      baseUrl = ApiConstant.baseCognitoPlusUrl;
    }
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      headers: {
        Headers.contentTypeHeader: Headers.jsonContentType,
      },
      connectTimeout: Duration(milliseconds: _connectTimeout),
      receiveTimeout: Duration(milliseconds: _receiveTimeout),
    ));
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          try {
            if (options.headers['Authorization'] == null) {
              AuthApp authApp = get_x.Get.find<AuthApp>();
              await authApp.ensureCognitoIdToken();
              options.headers['Authorization'] =
                  "Bearer ${authApp.cognitoIdToken.value}";

              options.headers['Accept-Language'] =
                  get_x.Get.locale!.languageCode.toLowerCase();
              if(authApp.totalhealthToken != null){
                options.headers['Pin'] = authApp.totalhealthToken?.token;
              }
              options.headers['Device'] = await NotificationService.getDeviceId() ?? "";
            }
          } catch (e) {
            return handler.reject(
              DioException(requestOptions: options),
            );
          }
          return handler.next(options); //continue
        }, /*
        onError: (error, handler) {
          developer.log(
            'from<== ${error.requestOptions.baseUrl}${error.requestOptions.path}',
          );
          developer.log('error=${error.toString()}');
          // developer.log('error=${error.response?.data}');
          handler.next(error);
        },*/
      ),
    );
    if (kDebugMode) {
      _dio.interceptors.add(
        LogInterceptor(
          requestHeader: true,
          requestBody: true,
          responseHeader: true,
          responseBody: false,
          error: true,
        ),
      );
    }
  }

  Result _handleResponse(Response response) {
    final responseStatus = response.statusCode;
    developer.log("status=$responseStatus");
    developer.log('response=$response');
    if (responseStatus == 200) {
      return Result.success(response.data);
    } else {
      return Result.error(response.data);
    }
  }

  @override
  Future<Result> post(
    String path, {
    dynamic data,
    String? baseUrl,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      if (baseUrl != null) {
        if (!baseUrl.startsWith("http")) {
          baseUrl = "https://$baseUrl";
        }
        _dio.options.baseUrl = baseUrl;
      } else {
        baseUrl = ApiConstant.baseCognitoPlusUrl;
      }
      _dio.options.baseUrl = baseUrl;
      var response = await _dio.post<void>(
        path,
        data: data,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      return _handleResponse(response);
    } on DioException catch (e) {
      return Result.error(e.response?.data);
    } catch (e) {
      developer.log('error=$e');
      return Result.error('$e');
    }
  }

  @override
  Future<Result> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    String? baseUrl,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      if (baseUrl != null) {
        if (!baseUrl.startsWith("http")) {
          baseUrl = "https://$baseUrl";
        }
      } else {
        baseUrl = ApiConstant.baseCognitoPlusUrl;
      }
      _dio.options.baseUrl = baseUrl;
      var response = await _dio.get<void>(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
      return _handleResponse(response);
    } catch (e) {
      developer.log('error=$e');
      return Result.error('$e');
    }
  }
}
