import 'package:get_it/get_it.dart';
import 'package:samitivej_flutter_app/commons/api/api_client.dart';
import 'package:samitivej_flutter_app/commons/api/api_client_impl.dart';
import 'package:samitivej_flutter_app/commons/api/data_connection_checker.dart';
import 'package:samitivej_flutter_app/commons/api/network_info.dart';
import 'package:samitivej_flutter_app/data/datasources/account/account_api_data_source.dart';
import 'package:samitivej_flutter_app/data/datasources/history/history_api_data_source.dart';
import 'package:samitivej_flutter_app/data/repositories/account_repository_impl.dart';
import 'package:samitivej_flutter_app/data/repositories/history_repository_impl.dart';
import 'package:samitivej_flutter_app/domain/repositories/account_repository.dart';
import 'package:samitivej_flutter_app/domain/repositories/history_repository.dart';
import 'package:samitivej_flutter_app/domain/usecases/account/log_device_info.dart';
import 'package:samitivej_flutter_app/domain/usecases/account/send_confirm_email.dart';
import 'package:samitivej_flutter_app/domain/usecases/account/update_customer_email.dart';
import 'package:samitivej_flutter_app/domain/usecases/checkup/download_checkup_report.dart';
import 'package:samitivej_flutter_app/domain/usecases/checkup/list_checkup_report.dart';
import 'package:samitivej_flutter_app/domain/usecases/get_patient_hn.dart';
import 'package:samitivej_flutter_app/domain/usecases/history/list_all_lab_group.dart';
import 'package:samitivej_flutter_app/domain/usecases/history/list_all_lab_result.dart';
import 'package:samitivej_flutter_app/domain/usecases/history/list_all_pacs_group.dart';
import 'package:samitivej_flutter_app/domain/usecases/history/list_all_pacs_result.dart';
import 'package:samitivej_flutter_app/domain/usecases/history/list_episode_diagnosis.dart';
import 'package:samitivej_flutter_app/domain/usecases/history/list_episode_labs.dart';
import 'package:samitivej_flutter_app/domain/usecases/history/list_episode_pacs.dart';
import 'package:samitivej_flutter_app/domain/usecases/history/list_episode_prescription.dart';
import 'package:samitivej_flutter_app/domain/usecases/history/list_medical_history.dart';
import 'package:samitivej_flutter_app/presentation/bloc/add_email/add_email_cubit.dart';
import 'package:samitivej_flutter_app/presentation/bloc/checkup_dowloading/checkup_downloading.dart';
import 'package:samitivej_flutter_app/presentation/bloc/checkup_report/checkup_report_cubit.dart';
import 'package:samitivej_flutter_app/presentation/bloc/episode_diagnosis/episode_diagnosis_cubit.dart';
import 'package:samitivej_flutter_app/presentation/bloc/episode_imaging/episode_imaging.dart';
import 'package:samitivej_flutter_app/presentation/bloc/episode_labs/episode_labs.dart';
import 'package:samitivej_flutter_app/presentation/bloc/episode_prescription/episode_prescription_cubit.dart';
import 'package:samitivej_flutter_app/presentation/bloc/medical_history/all_lab_group_cubit.dart';
import 'package:samitivej_flutter_app/presentation/bloc/medical_history/all_lab_result_cubit.dart';
import 'package:samitivej_flutter_app/presentation/bloc/medical_history/all_pacs_group_cubit.dart';
import 'package:samitivej_flutter_app/presentation/bloc/medical_history/medical_history_cubit.dart';
import 'package:samitivej_flutter_app/presentation/bloc/pacs/all_pacs_study_cubit.dart';
import 'package:samitivej_flutter_app/presentation/bloc/patient_hn/patient_hn_cubit.dart';

/// GetIt is a service locator package named get_it that’s predefined in pubspec.yaml under dependencies.
/// Behind the scenes, get_it keeps track of all your registered objects.
/// The service locator is a global singleton that you can access from anywhere within your app.
GetIt _sl = GetIt.I;

class AppInjector {
  static final AppInjector _singleton = AppInjector._internal();

  factory AppInjector() => _singleton;

  /// The _internal construction is just a name often given to constructors
  /// that are private to the class (the name is not required to be ._internal
  /// you can create a private constructor using any Class._someName construction).
  AppInjector._internal();

  static T get<T extends Object>() => _sl<T>();

  /// This function is where you register your services. You should call it before you build the UI.
  /// That means calling it first thing in main.dart.
  static Future<void> init() async {
    // External
    await _initExternal();
    // Commons
    _initCommons();
    // Bloc
    _initCubits();
    // Use cases
    _initUseCases();
    // Repository
    _initRepos();
    // Data sources
    _initDataSources();
  }
}

void _initCommons() {
  /*_sl.registerLazySingleton(() => InputValidator());*/
  _sl.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl(_sl()));
  _sl.registerLazySingleton<ApiClient>(() => ApiClientImpl());
  /*_sl.registerLazySingleton<Storage>(() => LocalStorage(_sl()));*/
}

void _initCubits() {
  _sl.registerFactory(() => MedicalHistoryCubit(_sl()));
  _sl.registerFactory(() => CheckupReportCubit(_sl()));
  _sl.registerFactory(() => EpisodeDiagnosisCubit(_sl()));
  _sl.registerFactory(() => EpisodePrescriptionCubit(_sl()));
  _sl.registerFactory(() => EpisodeLabsCubit(_sl()));
  _sl.registerFactory(() => EpisodeImagingCubit(_sl()));
  _sl.registerFactory(() => PatientHnCubit(_sl()));
  _sl.registerFactory(() => CheckupDownloadingCubit(_sl()));
  _sl.registerFactory(() => AddEmailCubit(_sl(), _sl()));
  _sl.registerFactory(() => AllLabGroupCubit(_sl()));
  _sl.registerFactory(() => AllLabResultCubit(_sl()));
  _sl.registerFactory(() => AllPacsGroupCubit(_sl()));
  _sl.registerFactory(() => AllPacsStudyCubit(_sl()));
}

void _initUseCases() {
  _sl.registerLazySingleton(() => GetPatientHn(_sl()));
  _sl.registerLazySingleton(() => ListMedicalHistory(_sl(), _sl()));
  _sl.registerLazySingleton(() => ListCheckupReport(_sl()));
  _sl.registerLazySingleton(() => ListEpisodeDiagnosis(_sl()));
  _sl.registerLazySingleton(() => ListEpisodePrescription(_sl()));
  _sl.registerLazySingleton(() => ListEpisodeLabs(_sl()));
  _sl.registerLazySingleton(() => ListAllLabGroup(_sl(), _sl()));
  _sl.registerLazySingleton(() => ListAllLabResult(_sl()));
  _sl.registerLazySingleton(() => ListEpisodePacs(_sl()));
  _sl.registerLazySingleton(() => ListAllPacsGroup(_sl(), _sl()));
  _sl.registerLazySingleton(() => ListAllPacsResult(_sl()));
  _sl.registerLazySingleton(() => DownloadCheckupReport(_sl()));
  _sl.registerLazySingleton(() => UpdateCustomerEmail(_sl()));
  _sl.registerLazySingleton(() => SendConfirmEmail(_sl()));
  _sl.registerLazySingleton(() => LogDeviceInfo(_sl()));
}

void _initRepos() {
  _sl.registerLazySingleton<HistoryRepository>(
    () => HistoryRepositoryImpl(_sl(), _sl()),
  );
  _sl.registerLazySingleton<AccountRepository>(
    () => AccountRepositoryImpl(_sl(), _sl()),
  );
}

void _initDataSources() {
  _sl.registerLazySingleton<HistoryApiDataSource>(
    () => HistoryApiDataSourceImpl(_sl()),
  );
  _sl.registerLazySingleton<AccountApiDataSource>(
    () => AccountApiDataSourceImpl(_sl()),
  );
}

Future _initExternal() async {
/*  final sharedPreferences = await SharedPreferences.getInstance();
  _sl.registerLazySingleton(() => sharedPreferences);*/
  _sl.registerLazySingleton(() => DataConnectionChecker());
}
