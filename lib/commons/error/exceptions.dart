class ServerException implements Exception {
  final String? code;
  final String? message;

  ServerException({this.code, this.message});
}

class InvalidException implements Exception {
  final String? message;
  final String? code;

  InvalidException({this.message, this.code});
}

class CacheException implements Exception {}

class UnknownException implements Exception {}

class NullResponseException implements Exception {}

class UnderageException implements Exception {}
