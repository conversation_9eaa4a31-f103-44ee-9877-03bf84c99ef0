import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  @override
  List<Object> get props => [];
}

// General failures
class InternetConnectionFailure extends Failure {}

class ServerFailure extends Failure {
  final String? code;
  final String? message;

  ServerFailure({this.code, this.message});
}

class InvalidFailure extends Failure {
  final String? message;

  InvalidFailure({this.message});
}

/// For input validation fail
class InvalidInputFailure extends Failure {}

class CacheFailure extends Failure {}

class CacheUserNotFoundFailure extends Failure {}

class NotFoundFailure extends Failure {
  final String? message;

  NotFoundFailure({this.message});
}

class UnknownFailure extends Failure {}

class StreamFailure extends Failure {}

class UserNotConfirmedFailure extends Failure {}