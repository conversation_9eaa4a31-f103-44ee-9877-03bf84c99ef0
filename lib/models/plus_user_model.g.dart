// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'plus_user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PlusUserModel _$PlusUserModelFromJson(Map<String, dynamic> json) =>
    PlusUserModel(
      customerHn: (json['customerHn'] as List<dynamic>)
          .map((e) => PlusHNModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$PlusUserModelToJson(PlusUserModel instance) =>
    <String, dynamic>{
      'customerHn': instance.customerHn.map((e) => e.toJson()).toList(),
    };

PlusHNModel _$PlusHNModelFromJson(Map<String, dynamic> json) => PlusHNModel(
      json['uniqueId'] as String?,
      json['hn'] as String,
      json['site'] as String,
      json['active'] as bool,
    );

Map<String, dynamic> _$PlusHNModelToJson(PlusHNModel instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('uniqueId', instance.uniqueId);
  val['hn'] = instance.hn;
  val['site'] = instance.site;
  val['active'] = instance.active;
  return val;
}
