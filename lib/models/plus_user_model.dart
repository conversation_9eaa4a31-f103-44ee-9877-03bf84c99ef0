import 'package:json_annotation/json_annotation.dart';

part 'plus_user_model.g.dart';

@JsonSerializable(includeIfNull: false, explicitToJson: true)
class PlusUserModel {
  @JsonKey(name: 'customerHn')
  final List<PlusHNModel> customerHn;

  PlusUserModel({required this.customerHn});

  factory PlusUserModel.fromJson(Map<String, dynamic> json) =>
      _$PlusUserModelFromJson(json);

  Map<String, dynamic> toJson() => _$PlusUserModelToJson(this);
}

@JsonSerializable(includeIfNull: false, explicitToJson: true)
class PlusHNModel {
  @JsonKey(name: 'uniqueId')
  final String? uniqueId;

  @JsonKey(name: 'hn')
  final String hn;

  @JsonKey(name: 'site')
  final String site;

  @Json<PERSON>ey(name: 'active')
  final bool active;

  PlusHNModel(this.uniqueId, this.hn, this.site, this.active);

  factory PlusHNModel.fromJson(Map<String, dynamic> json) =>
      _$PlusHNModelFromJson(json);

  Map<String, dynamic> toJson() => _$PlusHNModelToJson(this);
}
