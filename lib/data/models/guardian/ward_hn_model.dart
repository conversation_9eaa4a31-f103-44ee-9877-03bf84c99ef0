import 'package:json_annotation/json_annotation.dart';

part 'ward_hn_model.g.dart';

@JsonSerializable(includeIfNull: false, explicitToJson: true)
class WardHnModel {
  @J<PERSON><PERSON>ey(name: 'accessPolicyId')
  final int accessPolicyId;

  @J<PERSON><PERSON><PERSON>(name: 'hn')
  final String hn;

  @Json<PERSON>ey(name: 'site')
  final String site;

  @<PERSON>son<PERSON>ey(name: 'isActive')
  final bool isActive;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'createdAt')
  final String createdAt;

  WardHnModel({
    required this.accessPolicyId,
    required this.hn,
    required this.site,
    required this.isActive,
    required this.createdAt,
  });

  factory WardHnModel.fromJson(Map<String, dynamic> json) =>
      _$WardHnModelFromJson(json);

  Map<String, dynamic> toJson() => _$WardHnModelToJson(this);
}

typedef WardHnModelList = List<WardHnModel>;

extension WardHnModelListExtension on WardHnModelList {
  /// Get active ward HN records
  WardHnModelList get activeWards =>
      where((wardHn) => wardHn.isActive).toList();

  /// Get inactive ward HN records
  WardHnModelList get inactiveWards =>
      where((wardHn) => !wardHn.isActive).toList();
}
