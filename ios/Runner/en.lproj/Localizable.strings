/* Pre-chat form question prompting the user to pick the department they'd like to speak to. */
"ios.conversation.ui.pre_chat.form.department.choice.message" = "Please select the topic that you want to know.";

/* Offline message informing the user that there is no active agents, and to check back later. Shown when ChatConfiguration.isOfflineFormEnabled is disabled */
"ios.conversation.ui.offline.no_agents_online" = "Our business hour are from 08:00-17:00, Monday-Friday. We will contact you as soon as possible. Sorry for the inconvenience.";
"ios.ZDKRequests.requestList.title" = " ";
"ios.ZDKRequests.createRequest.title" = "Chat";
"ios.ZDKHelpCenter.support.contactSupport" = "Chat";
"ios.ZDKRequests.rightBarButton.title.contact" = "Chat";

/* Initial greetings from Answer Bot */
"ios.answer_bot.text.label.hi" = "Hello";
"ios.answer_bot.text.label.ask_a_question" = "What information do you want to know?";

/* Shown after initial greetings if Support, Chat, or other Messaging engines are configured */
"ios.answer_bot.cell.text.inactivity_get_in_touch_message" = "Or would you like to inquire with the staff?";

/* Input hint text */
"ios.answer_bot.ui.type_your_question.input.label" = "Type your question…";

/* Shown when a single article was found by Answer Bot */
"ios.common.ui.cell.text.single_article" = "Here's an article that may help:";

/* Shown when multiple articles were found by Answer Bot */
"ios.common.ui.cell.text.many_articles" = "Here are some articles that may help:";

/* Shown when no articles are found by Answer Bot */
"ios.common.ui.cell.text.no_articles" = "I couldn't find any relevant articles.";
"ios.common.ui.cell.text.get_in_touch" = "Would you like to inquire with the staff?";
"ios.answer_bot.cell.text.ask_me_another_question" = "Or would you like to ask for more information?";

/* Shown at the bottom of an article, above the yes and no labels */
"ios.answer_bot.article_view.label.question" = "Does this article answer your question?";
"ios.answer_bot.resolution_view.question.text.tell_us_why" = "Was the article related to your question?";

/* Quick reply labels shown to ask whether an article was helpful or not */
"ios.answer_bot.article.view.anwser.label.yes" = "Yes";
"ios.answer_bot.resolution_view.button.label.no" = "No";

/* Shown when the end-user marks a question as resolved */
"ios.answer_bot.cell.text.question_resolved" = "Nice. Thank you very much.";

/* Shown after a successful resolution, asking if there's another question */
"ios.answer_bot.cell.text.type_another_question" = "If there's anything else I can find for you, just type another question.";

/* Shown after an unsuccessful resolution */
"ios.answer_bot.cell.text.question_unresolved" = "I see. Your question is still unresolved.";

/* Shown if the user navigates back to the conversation and they didn't select yes / no on the previous article screen */
"ios.answer_bot.cell.text.article_helpful_question" = "Did the article you viewed help to answer your question?";
"ios.answer_bot.resolution_view.question.text.tell_us_more" = "Please tell us more. Was the article related to your question?";

/* Shown when there is no contact engine like Support or Chat */
"ios.answer_bot.cell.text.ask_me_another_question_no_transfer_options" = "You can try asking me another question.";

/* Labels shown when an end-user can transfer to Support, Chat, and other contact engines */
"ios.conversation.ui.chat.handover.message.selection" = "Chat with the staff";
"ios.ZDKRequests.createRequest.leaveAMessage.title" = "Chat with the staff";

/* Errors */
"ios.answer_bot.load_article.error" = "Failed to load article";
"ios.answer_bot.text.label.disabled" = "Uh-oh. Sorry, something’s up. I can’t answer questions right now.";

/* Labels shown after the end-user selects the "Leave a message" option */
"ios.SupportEngine.requestCreated.conversationsEnabledMessage" = "Thank you. A message has been sent to the team. You can view this message and add additional details here:";
"ios.SupportEngine.requestCreated.conversationsOff.message" = "Thank you. A message has been sent to the team. If a reply is needed, they will contact you by email.";
"ios.SupportEngine.requestCreated.requestList.button" = "View messages";
"ios.SupportEngine.greeting.message" = "Please inform the information you want to inquire further";
