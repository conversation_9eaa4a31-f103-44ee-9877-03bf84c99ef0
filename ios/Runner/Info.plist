<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Well</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>Well by Samitivej</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>wellbysamitivej</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>myapp</string>
					<string>line3rdp.$(PRODUCT_BUNDLE_IDENTIFIER)</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>fb348461046856518</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>FacebookAppID</key>
		<string>348461046856518</string>
		<key>FacebookDisplayName</key>
		<string>Well</string>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false/>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>sms</string>
			<string>tel</string>
			<string>lineauth2</string>
			<string>fbauth2</string>
			<string>fbapi</string>
			<string>fbapi20130214</string>
			<string>fbapi20130410</string>
			<string>fbapi20130702</string>
			<string>fbapi20131010</string>
			<string>fbapi20131219</string>
			<string>fbapi20140410</string>
			<string>fbapi20140116</string>
			<string>fbapi20150313</string>
			<string>fbapi20150629</string>
			<string>fbapi20160328</string>
			<string>fbauth</string>
			<string>fb-messenger-share-api</string>
			<string>fbauth2</string>
			<string>fbshareextension</string>
			<string>twitter</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSCalendarsUsageDescription</key>
		<string>We need this permission to add appointment to your calendar</string>
		<key>NSCameraUsageDescription</key>
		<string>We need camera to provide service for you i.e. telemedicine or meal-tracking</string>
		<key>NSFaceIDUsageDescription</key>
		<string>Use your Face ID to protect your information</string>
		<key>NSHealthShareUsageDescription</key>
		<string>We will sync your data with the Apple Health app to give you better insights</string>
		<key>NSHealthUpdateUsageDescription</key>
		<string>We will sync your data with the Apple Health app to give you better insights</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>Used to display a map while you exercise.</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>Used to display a map while you exercise.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>Used to display a map while you exercise.</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>We need microphone to provide service for you i.e. telemedicine</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>We will use your photo to help us for diagnosis your symptoms</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>We need access to your photo library to save images.</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>location</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIRequiresFullScreen</key>
		<true/>
		<key>UIStatusBarHidden</key>
		<false/>
		<key>UIStatusBarStyle</key>
		<string>UIStatusBarStyleLightContent</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
		<key>FacebookClientToken</key>
		<string>ec46167bb29511a7d272591c62b12212</string>
		<key>FlutterDeepLinkingEnabled</key>
		<true/>
		<key>io.flutter.embedded_views_preview</key>
		<string>YES</string>
	</dict>
</plist>
