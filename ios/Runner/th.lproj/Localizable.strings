/* Pre-chat form question prompting the user to pick the department they'd like to speak to. */
"ios.conversation.ui.pre_chat.form.department.choice.message" = "กรุณาเลือกหัวข้อที่ท่านต้องการสอบถาม";

/* Offline message informing the user that there is no active agents, and to check back later. Shown when ChatConfiguration.isOfflineFormEnabled is disabled */
"ios.conversation.ui.offline.no_agents_online" = "ขณะนี้เป็นช่วงนอกเวลาทำการ เราจะติดต่อกลับท่านภายหลัง​เมื่อถึงเวลาทำการ​  วันจันทร์​ -​ วันศุกร์​ เวลา 08:00 - 17:00 น. ขอบคุณค่ะ";
"ios.ZDKRequests.requestList.title" = " ";
"ios.ZDKRequests.createRequest.title" = "แชท";
"ios.ZDKHelpCenter.support.contactSupport" = "แชท";
"ios.ZDKRequests.rightBarButton.title.contact" = "แชท";

/* Initial greetings from Answer Bot */
"ios.answer_bot.text.label.hi" = "สวัสดีค่ะ";
"ios.answer_bot.text.label.ask_a_question" = "ต้องการสอบถามข้อมูลเกี่ยวกับเรื่องอะไรคะ";

/* Shown after initial greetings if Support, Chat, or other Messaging engines are configured */
"ios.answer_bot.cell.text.inactivity_get_in_touch_message" = "หรือต้องการติดต่อสอบถามกับเจ้าหน้าที่หรือไม่";

/* Shown when no articles are found by Answer Bot */
"ios.common.ui.cell.text.get_in_touch" = "หรือต้องการติดต่อสอบถามกับเจ้าหน้าที่หรือไม่";
"ios.answer_bot.cell.text.ask_me_another_question" = "ต้องการสอบถามข้อมูลเรื่องอื่นเพิ่มเติมหรือไม่";

/* Shown when the end-user marks a question as resolved */
"ios.answer_bot.cell.text.question_resolved" = "ขอบพระคุณมากค่ะ";

/* Labels shown when an end-user can transfer to Support, Chat, and other contact engines */
"ios.ZDKRequests.createRequest.leaveAMessage.title" = "คุยกับเจ้าหน้าที่";

"ios.SupportEngine.greeting.message" = "กรุณาแจ้งข้อมูลที่คุณต้องการสอบถามเพิ่มเติมได้เลยค่ะ";


