{"auth": {"superappamplify": {"service": "Cognito", "providerPlugin": "awscloudformation", "dependsOn": [], "customAuth": false, "frontendAuthConfig": {"socialProviders": ["FACEBOOK", "GOOGLE", "APPLE"], "usernameAttributes": ["EMAIL"], "signupAttributes": ["EMAIL"], "passwordProtectionSettings": {"passwordPolicyMinLength": 8, "passwordPolicyCharacters": []}, "mfaConfiguration": "OFF", "mfaTypes": ["SMS"], "verificationMechanisms": ["EMAIL"]}}}, "storage": {"s3a386e174": {"service": "S3", "providerPlugin": "awscloudformation", "dependsOn": []}}, "api": {"superappamplify": {"service": "AppSync", "providerPlugin": "awscloudformation", "output": {"authConfig": {"defaultAuthentication": {"authenticationType": "API_KEY", "apiKeyConfig": {"apiKeyExpirationDays": 30, "description": "api key description"}}, "additionalAuthenticationProviders": [{"authenticationType": "AWS_IAM"}]}}, "dependsOn": []}}}