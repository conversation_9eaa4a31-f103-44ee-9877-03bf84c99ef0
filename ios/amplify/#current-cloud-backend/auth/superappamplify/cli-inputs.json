{"version": "1", "cognitoConfig": {"identityPoolName": "testAuthIdentityPool", "allowUnauthenticatedIdentities": false, "resourceNameTruncated": "superafe0e622a", "userPoolName": "superappamplify", "autoVerifiedAttributes": ["email"], "mfaConfiguration": "OFF", "mfaTypes": ["SMS Text Message"], "smsAuthenticationMessage": "Your authentication code is {####}", "smsVerificationMessage": "Your verification code is {####}", "emailVerificationSubject": "Your verification code", "emailVerificationMessage": "Your verification code is {####}", "defaultPasswordPolicy": false, "passwordPolicyMinLength": 8, "passwordPolicyCharacters": [], "requiredAttributes": ["email"], "aliasAttributes": [], "userpoolClientGenerateSecret": false, "userpoolClientRefreshTokenValidity": 30, "userpoolClientWriteAttributes": [], "userpoolClientReadAttributes": [], "userpoolClientLambdaRole": "superafe0e622a_userpoolclient_lambda_role", "userpoolClientSetAttributes": false, "sharedId": "fe0e622a", "resourceName": "superappamplify", "authSelections": "identityPoolAndUserPool", "serviceName": "Cognito", "usernameAttributes": ["email"], "useDefault": "manual", "userPoolGroups": false, "userPoolGroupList": [], "adminQueries": false, "thirdPartyAuth": false, "authProviders": [], "hostedUI": true, "hostedUIDomainName": "gioajokyhvep", "authProvidersUserPool": ["Facebook", "Google", "SignInWithApple"], "hostedUIProviderMeta": "[{\"ProviderName\":\"Facebook\",\"authorize_scopes\":\"email,public_profile\",\"AttributeMapping\":{\"email\":\"email\",\"username\":\"id\"}},{\"ProviderName\":\"Google\",\"authorize_scopes\":\"openid email profile\",\"AttributeMapping\":{\"email\":\"email\",\"username\":\"sub\"}},{\"ProviderName\":\"SignInWithApple\",\"authorize_scopes\":\"email\",\"AttributeMapping\":{\"email\":\"email\"}}]", "oAuthMetadata": "{\"AllowedOAuthFlows\":[\"code\"],\"AllowedOAuthScopes\":[\"phone\",\"email\",\"openid\",\"profile\",\"aws.cognito.signin.user.admin\"],\"CallbackURLs\":[\"myapp://callback/\"],\"LogoutURLs\":[\"myapp://signout/\"]}", "usernameCaseSensitive": false, "useEnabledMfas": true, "authRoleArn": {"Fn::GetAtt": ["AuthRole", "<PERSON><PERSON>"]}, "unauthRoleArn": {"Fn::GetAtt": ["UnauthRole", "<PERSON><PERSON>"]}, "breakCircularDependency": true, "dependsOn": []}}