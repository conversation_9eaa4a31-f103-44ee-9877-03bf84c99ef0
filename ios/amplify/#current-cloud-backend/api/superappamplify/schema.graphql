enum CardType {
  ID_CARD
  INSURANCE_CARD
}

type UserInfoModel @model @auth(rules: [{allow: public}]) {
  id: ID!
  email: String!
  username: String
  firstNameTH: String
  lastNameTH: String
  firstNameEN: String
  lastNameEN: String
  phoneNumberCountryCode: String
  phoneNumber: String
  idNumber: String
  profileImageKey: String
  mainCognitoID: String
  facebookCognitoID: String
  googleCognitoID: String
  appleCognitoID: String
  lineID: String
  pdpaAcceptation: Boolean
  isActive: Boolean
  accountConfirmed: Boolean
  answerLinkTotalHealthPopup: Boolean
  dateOfBirth: AWSDateTime
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  cards: [CardInfoModel] @hasMany(indexName: "byUserInfoModel", fields: ["id"])
}

type CardInfoModel @model @auth(rules: [{allow: public}]) {
  id: ID!
  company: String
  memberNumber: String
  frontImageKey: String
  frontImageUrl: String
  backImageKey: String
  backImageUrl: String
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  cardType: CardType!
  userInfoID: ID! @index(name: "byUserInfoModel")
}
 