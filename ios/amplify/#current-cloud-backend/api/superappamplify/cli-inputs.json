{"version": 1, "serviceConfiguration": {"apiName": "superappamplify", "serviceName": "AppSync", "gqlSchemaPath": "/tmp/amplify-d7fdbd4c-0f24-44df-99f6-970b1bb91a48/amplify/backend/api/superappamplify/schema.graphql", "defaultAuthType": {"mode": "API_KEY", "keyDescription": "api key description", "expirationTime": 30}, "conflictResolution": {"defaultResolutionStrategy": {"type": "AUTOMERGE"}}, "additionalAuthTypes": [{"mode": "AWS_IAM"}]}}