{"providers": {"awscloudformation": {"AuthRoleName": "amplify-superappamplify-uat-113101-authRole", "UnauthRoleArn": "arn:aws:iam::************:role/amplify-superappamplify-uat-113101-unauthRole", "AuthRoleArn": "arn:aws:iam::************:role/amplify-superappamplify-uat-113101-authRole", "Region": "ap-southeast-1", "DeploymentBucketName": "amplify-superappamplify-uat-113101-deployment", "UnauthRoleName": "amplify-superappamplify-uat-113101-unauthRole", "StackName": "amplify-superappamplify-uat-113101", "StackId": "arn:aws:cloudformation:ap-southeast-1:************:stack/amplify-superappamplify-uat-113101/ab56e200-d7f5-11ec-920f-06dd5191ee7e", "AmplifyAppId": "d2ovmrxh659ga6"}}, "auth": {"superappamplify": {"service": "Cognito", "providerPlugin": "awscloudformation", "dependsOn": [], "customAuth": false, "frontendAuthConfig": {"socialProviders": ["FACEBOOK", "GOOGLE", "APPLE"], "usernameAttributes": ["EMAIL"], "signupAttributes": ["EMAIL"], "passwordProtectionSettings": {"passwordPolicyMinLength": 8, "passwordPolicyCharacters": []}, "mfaConfiguration": "OFF", "mfaTypes": ["SMS"], "verificationMechanisms": ["EMAIL"]}, "output": {"UserPoolId": "ap-southeast-1_NgNolgxcr", "AppClientIDWeb": "6nc7pejeckummq5eoa85tv8am5", "AppClientID": "7mhme7nc1lt2s4gfodits9mnv5", "HostedUIDomain": "gioajokyhvep-uat", "IdentityPoolId": "ap-southeast-1:146a15c8-363f-40ff-a460-a8ccfd74aacc", "UserPoolArn": "arn:aws:cognito-idp:ap-southeast-1:************:userpool/ap-southeast-1_NgNolgxcr", "IdentityPoolName": "testAuthIdentityPool__uat", "OAuthMetadata": "{\"AllowedOAuthFlows\":[\"code\"],\"AllowedOAuthScopes\":[\"phone\",\"email\",\"openid\",\"profile\",\"aws.cognito.signin.user.admin\"],\"CallbackURLs\":[\"myapp://callback/\"],\"LogoutURLs\":[\"myapp://signout/\"]}", "UserPoolName": "superappamplify"}, "lastPushTimeStamp": "2022-07-19T06:44:40.502Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-superappamplify-uat-113101-deployment/amplify-cfn-templates/auth/superappamplify-cloudformation-template.json", "logicalId": "authsuperappamplify"}}}, "storage": {"s3a386e174": {"service": "S3", "providerPlugin": "awscloudformation", "dependsOn": [], "output": {"BucketName": "superappamplify606fa1d85f814bb18faa753d121e9fb4113101-uat", "Region": "ap-southeast-1"}, "lastPushTimeStamp": "2022-07-19T06:44:41.255Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-superappamplify-uat-113101-deployment/amplify-cfn-templates/storage/cloudformation-template.json", "logicalId": "storages3a386e174"}}}, "api": {"superappamplify": {"service": "AppSync", "providerPlugin": "awscloudformation", "output": {"authConfig": {"defaultAuthentication": {"authenticationType": "API_KEY", "apiKeyConfig": {"apiKeyExpirationDays": 30, "description": "api key description"}}, "additionalAuthenticationProviders": [{"authenticationType": "AWS_IAM"}]}, "GraphQLAPIIdOutput": "fdbhglnt4beutll4fkcnt75tdq", "GraphQLAPIEndpointOutput": "https://x4mbuz7t6rb2dg77hub6zgvssa.appsync-api.ap-southeast-1.amazonaws.com/graphql", "GraphQLAPIKeyOutput": "da2-b3boaij43bgw3khzvamgzsk5nm"}, "dependsOn": [], "lastPushTimeStamp": "2022-07-19T06:44:40.507Z", "providerMetadata": {"s3TemplateURL": "https://s3.amazonaws.com/amplify-superappamplify-uat-113101-deployment/amplify-cfn-templates/api/cloudformation-template.json", "logicalId": "apisuperappamplify"}, "lastPushDirHash": "NmEJGmVHMPWOmrjgRRaVglRtCj8="}}}