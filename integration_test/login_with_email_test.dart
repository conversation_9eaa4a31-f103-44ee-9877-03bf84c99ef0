
import 'common.dart';

void main() {

  patrolSetUp(() {
    // Smoke test for https://github.com/leancodepl/patrol/issues/2021
    expect(1 + 1, equals(2));
  });

  patrol(
    'login with email successfully',
    ($) async {
      await createApp($);
      // handle native notification permission request dialog
      $.native.denyPermission();
      await $.waitUntilVisible($(#onboardingNextButton));
      expect($(#onboardingNextButton).exists, equals(true));

      await $(#onboardingNextButton).tap();
      await $(#onboardingNextButton).tap();
      await $(#onboardingNextButton).tap();
      await $(#onboardingNextButton).tap();
      await $(#onboardingNextButton).tap();
      await $(#onboardingNextButton).tap();
      await $(#onboardingNextButton).tap();
      await $(#onboardingNextButton).tap();
      expect($(#loginWithEmailButton).exists, equals(true));

      await $(#loginWithEmailButton).tap();
      await $.waitUntilVisible($(#submitLoginButton));
      await $(#emailTextField).enterText('<EMAIL>');
      await $(#passwordTextField).enterText('*********');
      await $(#submitLoginButton).tap();
      // await $.waitUntilVisible($(#HomeScreen));
      // await $.native.pressHome();
    },
  );
}
