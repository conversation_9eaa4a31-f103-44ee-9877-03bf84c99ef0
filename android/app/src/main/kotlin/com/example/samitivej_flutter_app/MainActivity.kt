package net.svvh.well

import androidx.annotation.NonNull
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterFragmentActivity() {

    private val CHANNEL_NAME = "health_connect_device"

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
//        android.util.Log.i("asdasd", flutterEngine.platformChannel.channel.toString())
        HealthConnectWearable(MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL_NAME),this)
//        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler {
//                call, result ->
//            // This method is invoked on the main thread.
//            // TODO
//        }

    }
}
